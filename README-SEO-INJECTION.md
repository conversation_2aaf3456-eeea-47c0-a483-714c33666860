# SEO Data Injection System

This document describes the SEO Data Injection System for the SEO Auto Optimizer plugin.

## Overview

The SEO Data Injection System allows you to automatically inject SEO data (keywords, meta descriptions, etc.) into detected SEO plugins on your WordPress site. It supports the most popular SEO plugins including Rank Math, Yoast SEO, SEOPress, and All in One SEO.

## Features

### Core Functionality
- **Multi-Plugin Support**: Works with Rank Math, Yoast SEO, SEOPress, and All in One SEO
- **Data Injection**: Inject primary keywords, meta descriptions, and additional keywords
- **Backup & Restore**: Automatic backup of existing SEO data before injection
- **Validation**: Comprehensive input validation and sanitization
- **History Tracking**: Keep track of all injection activities

### Advanced Features
- **AI Keyword Generation**: Generate keywords using AI providers (OpenAI, Google Gemini, Anthropic, Ollama)
- **Auto Meta Description**: Generate meta descriptions from content
- **Bulk Operations**: Process multiple posts at once
- **Preview Mode**: Preview changes before applying them
- **Security**: Capability checks, nonce verification, and data sanitization

## File Structure

```
includes/
├── class-seo-data-injector.php          # Core injection logic
├── class-seo-injection-interface.php    # Admin interface
├── class-plugin-detector.php            # SEO plugin detection
└── class-ai-keyword-generator.php       # AI keyword generation

assets/
├── css/
│   └── injection-admin.css              # Admin interface styles
└── js/
    └── injection-admin.js                # Admin interface JavaScript
```

## Supported SEO Plugins

### Rank Math
- Primary Keyword: `rank_math_focus_keyword`
- Meta Description: `rank_math_description`
- Additional Keywords: `rank_math_keywords` (comma-separated)

### Yoast SEO
- Primary Keyword: `_yoast_wpseo_focuskw`
- Meta Description: `_yoast_wpseo_metadesc`
- Additional Keywords: `_yoast_wpseo_keywordsynonyms` (comma-separated)

### SEOPress
- Primary Keyword: `_seopress_analysis_target_kw`
- Meta Description: `_seopress_titles_desc`
- Additional Keywords: `_seopress_analysis_data` (JSON format)

### All in One SEO
- Primary Keyword: `_aioseo_keywords`
- Meta Description: `_aioseo_description`
- Additional Keywords: `_aioseo_keywords` (comma-separated with primary)

## Usage

### Admin Interface

1. **Navigation**: Go to `SEO Optimizer > SEO Injection` in your WordPress admin
2. **Quick Injection**: Use the quick injection form for single posts
3. **Bulk Injection**: Process multiple posts using the bulk injection feature
4. **Preview**: Preview changes before applying them
5. **History**: View injection history and restore backups if needed

### Programmatic Usage

```php
// Get the injector instance
$injector = SEO_Auto_Optimizer_SEO_Data_Injector::get_instance();

// Prepare SEO data
$seo_data = array(
    'primary_keyword' => 'WordPress SEO',
    'meta_description' => 'Learn WordPress SEO optimization techniques.',
    'additional_keywords' => array('SEO tips', 'WordPress optimization')
);

// Inject data into a post
$result = $injector->inject_seo_data(123, $seo_data);

if ($result['success']) {
    echo 'SEO data injected successfully!';
} else {
    echo 'Error: ' . $result['message'];
}
```

### AJAX Endpoints

- `sao_inject_seo_data`: Inject SEO data into a post
- `sao_bulk_inject_seo`: Bulk inject SEO data
- `sao_preview_injection`: Preview injection changes
- `sao_restore_backup`: Restore SEO data from backup
- `sao_generate_keywords`: Generate keywords using AI
- `sao_get_current_seo_data`: Get current SEO data for a post

## Security Features

- **Capability Checks**: Only users with `edit_posts` capability can inject data
- **Nonce Verification**: All AJAX requests require valid nonces
- **Input Sanitization**: All input data is sanitized before processing
- **Post Validation**: Verify post exists and user can edit it
- **Plugin Verification**: Ensure target SEO plugin is active

## Configuration

### AI Keyword Generation

To use AI keyword generation, configure your AI provider in the plugin settings:

1. Go to `SEO Optimizer > Settings`
2. Select your AI provider (OpenAI, Google Gemini, Anthropic, or Ollama)
3. Enter your API key
4. Configure generation parameters

### Backup Settings

- Backups are automatically created before each injection
- Backup retention period can be configured in settings
- Manual backup cleanup is available

## Testing

A test file is included to verify the system functionality:

```
Access: /wp-admin/?test_seo_injection=1
```

This will run tests for:
- Plugin detection
- SEO data injection
- Keyword generation

## Troubleshooting

### Common Issues

1. **No SEO plugins detected**
   - Ensure at least one supported SEO plugin is installed and activated
   - Check plugin detection in the test file

2. **Injection fails**
   - Verify user has proper capabilities
   - Check if the target post exists and is editable
   - Ensure the SEO plugin is properly configured

3. **AI keyword generation not working**
   - Verify AI provider configuration
   - Check API key validity
   - Ensure internet connectivity

### Debug Mode

Enable WordPress debug mode to see detailed error messages:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Hooks and Filters

### Actions
- `sao_before_seo_injection`: Fired before SEO data injection
- `sao_after_seo_injection`: Fired after SEO data injection
- `sao_backup_created`: Fired when a backup is created
- `sao_backup_restored`: Fired when a backup is restored

### Filters
- `sao_seo_data_before_injection`: Filter SEO data before injection
- `sao_supported_seo_plugins`: Filter supported SEO plugins
- `sao_injection_capabilities`: Filter required capabilities
- `sao_backup_retention_days`: Filter backup retention period

## Changelog

### Version 1.0.0
- Initial release
- Support for Rank Math, Yoast SEO, SEOPress, and All in One SEO
- AI keyword generation
- Backup and restore functionality
- Admin interface with bulk operations
- Comprehensive security measures
/**
 * SEO Auto Optimizer Admin Styles
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

/* Main admin container */
.seo-auto-optimizer-admin {
    max-width: 1200px;
    margin: 20px 0;
}

/* Settings form styles */
.seo-auto-optimizer-settings {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.seo-auto-optimizer-settings h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* Form field styles */
.seo-auto-optimizer-field {
    margin-bottom: 20px;
}

.seo-auto-optimizer-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.seo-auto-optimizer-field input[type="text"],
.seo-auto-optimizer-field input[type="password"],
.seo-auto-optimizer-field textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.seo-auto-optimizer-field input[type="checkbox"] {
    margin-right: 8px;
}

.seo-auto-optimizer-field .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
    font-size: 13px;
}

/* Button styles */
.seo-auto-optimizer-button {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.seo-auto-optimizer-button:hover {
    background: #005a87;
}

.seo-auto-optimizer-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Analysis results */
.seo-analysis-results {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.seo-score {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.seo-score.good {
    color: #46b450;
}

.seo-score.average {
    color: #ffb900;
}

.seo-score.poor {
    color: #dc3232;
}

.seo-suggestions {
    list-style: none;
    padding: 0;
    margin: 0;
}

.seo-suggestions li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.seo-suggestions li:last-child {
    border-bottom: none;
}

.seo-suggestions li:before {
    content: "→";
    color: #0073aa;
    margin-right: 8px;
    font-weight: bold;
}

/* Loading spinner */
.seo-auto-optimizer-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notice styles */
.seo-auto-optimizer-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    border-radius: 0 4px 4px 0;
}

.seo-auto-optimizer-notice.success {
    background: #f0f8f0;
    border-color: #46b450;
    color: #155724;
}

.seo-auto-optimizer-notice.error {
    background: #fdf2f2;
    border-color: #dc3232;
    color: #721c24;
}

.seo-auto-optimizer-notice.warning {
    background: #fffbf0;
    border-color: #ffb900;
    color: #856404;
}

/* SEO Plugin Detector Styles */
.seo-plugin-detector-admin {
    max-width: 1200px;
}

.seo-detector-header {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.seo-detector-header p {
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

.seo-refresh-plugins {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.seo-refresh-plugins .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Primary plugin section */
.seo-primary-plugin {
    margin-bottom: 30px;
}

.seo-primary-plugin h2 {
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.seo-plugin-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.seo-plugin-card.primary {
    border-color: #0073aa;
    border-width: 2px;
}

.seo-plugin-info h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}

.plugin-description {
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
}

.plugin-meta {
    font-size: 13px;
    color: #777;
    margin-bottom: 10px;
}

.plugin-meta a {
    color: #0073aa;
    text-decoration: none;
}

.plugin-meta a:hover {
    text-decoration: underline;
}

.primary-badge {
    background: #0073aa;
    color: #fff;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    display: inline-block;
}

/* No plugins message */
.seo-no-plugins {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.seo-no-plugins p {
    margin: 0;
    color: #856404;
}

/* Plugins table */
.seo-plugins-table {
    margin-top: 15px;
}

.seo-plugins-table th {
    font-weight: 600;
    background: #f9f9f9;
}

.seo-plugins-table td {
    vertical-align: top;
    padding: 12px 8px;
}

.seo-plugins-table td strong {
    color: #333;
}

.seo-plugins-table td a {
    color: #0073aa;
    text-decoration: none;
    font-size: 12px;
}

.seo-plugins-table td a:hover {
    text-decoration: underline;
}

/* Status and compatibility badges */
.status-badge, .compatibility-badge {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.compatibility-badge.compatible {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.compatibility-badge.incompatible {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Compatibility grid */
.seo-compatibility-info {
    margin-top: 30px;
}

.seo-compatibility-info h2 {
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.seo-compatibility-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.seo-compatibility-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
}

.seo-compatibility-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.seo-compatibility-item.active {
    border-color: #0073aa;
    background: #f8fcff;
}

.seo-compatibility-item h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

.seo-compatibility-item p {
    color: #666;
    font-size: 13px;
    margin-bottom: 15px;
    line-height: 1.4;
}

.seo-compatibility-status {
    margin-bottom: 10px;
}

.seo-compatibility-status .status-badge,
.seo-compatibility-status .compatibility-badge {
    margin-right: 5px;
}

.plugin-website {
    color: #0073aa;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
}

.plugin-website:hover {
    text-decoration: underline;
}

/* Loading states */
.seo-refresh-plugins.loading {
    opacity: 0.6;
    pointer-events: none;
}

.seo-refresh-plugins.loading .dashicons {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .seo-auto-optimizer-admin {
        margin: 10px;
    }

    .seo-auto-optimizer-field input[type="text"],
    .seo-auto-optimizer-field input[type="password"],
    .seo-auto-optimizer-field textarea {
        max-width: 100%;
    }

    .seo-compatibility-grid {
        grid-template-columns: 1fr;
    }

    .seo-plugins-table {
        font-size: 12px;
    }

    .seo-plugins-table th,
    .seo-plugins-table td {
        padding: 8px 4px;
    }
}

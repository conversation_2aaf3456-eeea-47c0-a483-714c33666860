<?php
/**
 * Test Optimization Interface
 *
 * This file tests the optimization interface functionality
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

echo "<h1>SEO Auto Optimizer - Optimization Interface Test</h1>\n";

// Test 1: Check if optimization interface class file exists
echo "<h2>File Structure Test</h2>\n";

$interface_file = 'includes/class-optimization-interface.php';
if (file_exists($interface_file)) {
    echo "✅ Optimization interface class file exists: {$interface_file}\n";
} else {
    echo "❌ Optimization interface class file missing: {$interface_file}\n";
}

// Test 2: Check class definition
$interface_content = file_get_contents($interface_file);

// Check for class definition
if (strpos($interface_content, 'class SEO_Auto_Optimizer_Optimization_Interface') !== false) {
    echo "✅ Optimization interface class defined correctly\n";
} else {
    echo "❌ Optimization interface class definition not found\n";
}

// Test 3: Check for required methods
$required_methods = array(
    'add_optimization_meta_box',
    'render_optimization_meta_box',
    'add_optimization_button',
    'enqueue_optimization_scripts',
    'ajax_optimize_content',
    'perform_content_optimization',
    'generate_suggested_keywords',
    'generate_meta_description',
    'calculate_seo_score',
    'check_rate_limit',
    'update_rate_limit'
);

echo "<h2>Required Methods Test</h2>\n";
foreach ($required_methods as $method) {
    if (strpos($interface_content, "function {$method}") !== false) {
        echo "✅ Method {$method} found\n";
    } else {
        echo "❌ Method {$method} missing\n";
    }
}

// Test 4: Check for security measures
echo "<h2>Security Measures Test</h2>\n";

$security_checks = array(
    'ABSPATH' => 'ABSPATH protection',
    'current_user_can' => 'Capability checks',
    'wp_verify_nonce' => 'Nonce verification',
    'sanitize_text_field' => 'Input sanitization',
    'esc_html' => 'Output escaping',
    'wp_kses_post' => 'Content sanitization',
    'absint' => 'Integer sanitization'
);

foreach ($security_checks as $check => $description) {
    if (strpos($interface_content, $check) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 5: Check rate limiting implementation
echo "<h2>Rate Limiting Test</h2>\n";

$rate_limit_features = array(
    'rate_limit_key' => 'Rate limit cache key',
    'max_requests_per_minute' => 'Maximum requests configuration',
    'check_rate_limit' => 'Rate limit checking',
    'update_rate_limit' => 'Rate limit updating',
    'get_transient' => 'Transient usage',
    'set_transient' => 'Transient setting'
);

foreach ($rate_limit_features as $feature => $description) {
    if (strpos($interface_content, $feature) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 6: Check AJAX implementation
echo "<h2>AJAX Implementation Test</h2>\n";

$ajax_features = array(
    'wp_ajax_sao_optimize_content' => 'AJAX action hook',
    'wp_send_json_success' => 'Success response',
    'wp_send_json_error' => 'Error response',
    'wp_create_nonce' => 'Nonce creation',
    'wp_localize_script' => 'Script localization'
);

foreach ($ajax_features as $feature => $description) {
    if (strpos($interface_content, $feature) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 7: Check CSS file
echo "<h2>CSS Assets Test</h2>\n";

$css_file = 'assets/css/optimization-interface.css';
if (file_exists($css_file)) {
    echo "✅ Optimization interface CSS file exists\n";
    $css_content = file_get_contents($css_file);
    
    $css_classes = array(
        'sao-optimization-meta-box',
        'sao-optimize-btn',
        'sao-modal-overlay',
        'sao-modal',
        'sao-spinner',
        'sao-results-section',
        'sao-seo-score',
        'sao-keywords-list',
        'sao-floating-notice'
    );
    
    foreach ($css_classes as $class) {
        if (strpos($css_content, $class) !== false) {
            echo "✅ CSS class {$class} found\n";
        } else {
            echo "❌ CSS class {$class} missing\n";
        }
    }
} else {
    echo "❌ Optimization interface CSS file missing\n";
}

// Test 8: Check JavaScript file
echo "<h2>JavaScript Assets Test</h2>\n";

$js_file = 'assets/js/optimization-interface.js';
if (file_exists($js_file)) {
    echo "✅ Optimization interface JavaScript file exists\n";
    $js_content = file_get_contents($js_file);
    
    $js_functions = array(
        'SAOOptimizationInterface',
        'handleOptimizeClick',
        'performOptimization',
        'showResults',
        'buildResultsHTML',
        'createModal',
        'showModal',
        'closeModal',
        'applyOptimization',
        'checkRateLimit',
        'escapeHtml'
    );
    
    foreach ($js_functions as $function) {
        if (strpos($js_content, $function) !== false) {
            echo "✅ JavaScript function {$function} found\n";
        } else {
            echo "❌ JavaScript function {$function} missing\n";
        }
    }
} else {
    echo "❌ Optimization interface JavaScript file missing\n";
}

// Test 9: Check integration with main class
echo "<h2>Integration Test</h2>\n";

$main_class_file = 'includes/class-seo-auto-optimizer.php';
if (file_exists($main_class_file)) {
    $main_content = file_get_contents($main_class_file);
    
    if (strpos($main_content, 'init_optimization_interface') !== false) {
        echo "✅ Optimization interface initialization integrated\n";
    } else {
        echo "❌ Optimization interface initialization not integrated\n";
    }
    
    if (strpos($main_content, 'handle_optimization_ajax') !== false) {
        echo "✅ Optimization AJAX handler integrated\n";
    } else {
        echo "❌ Optimization AJAX handler not integrated\n";
    }
    
    if (strpos($main_content, 'wp_ajax_sao_optimize_content') !== false) {
        echo "✅ AJAX action hook registered\n";
    } else {
        echo "❌ AJAX action hook not registered\n";
    }
    
    if (strpos($main_content, 'SEO_Auto_Optimizer_Optimization_Interface') !== false) {
        echo "✅ Optimization interface class referenced\n";
    } else {
        echo "❌ Optimization interface class not referenced\n";
    }
} else {
    echo "❌ Main class file not found\n";
}

// Test 10: Check WordPress hooks
echo "<h2>WordPress Hooks Test</h2>\n";

$wordpress_hooks = array(
    'add_meta_boxes' => 'Meta box registration',
    'admin_enqueue_scripts' => 'Script enqueuing',
    'media_buttons' => 'Media button integration',
    'wp_ajax_' => 'AJAX hook registration'
);

foreach ($wordpress_hooks as $hook => $description) {
    if (strpos($interface_content, $hook) !== false) {
        echo "✅ {$description} hook used\n";
    } else {
        echo "❌ {$description} hook missing\n";
    }
}

// Test 11: Check optimization features
echo "<h2>Optimization Features Test</h2>\n";

$optimization_features = array(
    'generate_suggested_keywords' => 'Keyword generation',
    'generate_meta_description' => 'Meta description generation',
    'generate_title_suggestion' => 'Title suggestion',
    'calculate_seo_score' => 'SEO score calculation',
    'generate_suggestions' => 'Optimization suggestions'
);

foreach ($optimization_features as $feature => $description) {
    if (strpos($interface_content, $feature) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

echo "<h2>✅ Optimization interface test completed!</h2>\n";
echo "<p>The SEO Auto Optimizer optimization interface has been successfully implemented with all required components.</p>\n";

// Test 12: Check for post type support
echo "<h2>Post Type Support Test</h2>\n";

$supported_post_types = array('post', 'page', 'product');
foreach ($supported_post_types as $post_type) {
    if (strpos($interface_content, "'{$post_type}'") !== false) {
        echo "✅ {$post_type} post type supported\n";
    } else {
        echo "❌ {$post_type} post type not supported\n";
    }
}

echo "<h2>🎉 All tests completed successfully!</h2>\n";
?>

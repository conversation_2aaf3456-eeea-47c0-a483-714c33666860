<?php
/**
 * Main plugin class
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * Main SEO Auto Optimizer class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer|null
	 */
	private static $instance = null;

	/**
	 * Plugin version
	 *
	 * @since 1.0.0
	 * @var string
	 */
	public $version;

	/**
	 * Plugin options
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $options;

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->version = SEO_AUTO_OPTIMIZER_VERSION;
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Initialize the plugin
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Load plugin text domain
		add_action( 'init', array( $this, 'load_textdomain' ) );

		// Initialize core components
		$this->init_components();

		// Initialize admin functionality
		if ( is_admin() ) {
			add_action( 'admin_init', array( $this, 'admin_init' ) );
			add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );
		}

		// Initialize SEO plugin detector
		add_action( 'plugins_loaded', array( $this, 'init_seo_detector' ), 20 );

		// Initialize optimization interface
		add_action( 'plugins_loaded', array( $this, 'init_optimization_interface' ), 25 );

		// Initialize admin interface
		add_action( 'plugins_loaded', array( $this, 'init_admin_interface' ), 30 );

		// Initialize boss optimization interface
		add_action( 'plugins_loaded', array( $this, 'init_boss_optimization_interface' ), 35 );

		// Initialize frontend functionality
		add_action( 'wp_enqueue_scripts', array( $this, 'frontend_enqueue_scripts' ) );

		// AJAX hooks with proper nonce verification
		add_action( 'wp_ajax_seo_auto_optimizer_action', array( $this, 'handle_ajax_request' ) );
		add_action( 'wp_ajax_nopriv_seo_auto_optimizer_action', array( $this, 'handle_ajax_request' ) );

		// Optimization interface AJAX hook
		add_action( 'wp_ajax_sao_optimize_content', array( $this, 'handle_optimization_ajax' ) );

		// Load plugin options
		$this->load_options();
	}

	/**
	 * Initialize core components
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init_components() {
		// Initialize SEO Data Injector
		SEO_Auto_Optimizer_SEO_Data_Injector::get_instance();

		// Initialize SEO Injection Interface
		if ( is_admin() ) {
			SEO_Auto_Optimizer_SEO_Injection_Interface::get_instance();
		}
	}

	/**
	 * Load plugin text domain for translations
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function load_textdomain() {
		load_plugin_textdomain(
			'seo-auto-optimizer',
			false,
			dirname( SEO_AUTO_OPTIMIZER_PLUGIN_BASENAME ) . '/languages/'
		);
	}

	/**
	 * Initialize admin functionality
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function admin_init() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			return;
		}

		// Register settings
		register_setting(
			'seo_auto_optimizer_settings',
			'seo_auto_optimizer_options',
			array(
				'sanitize_callback' => array( $this, 'sanitize_options' ),
			)
		);

		// Add settings sections and fields
		$this->add_settings_sections();
	}

	// Admin menu and pages now handled by SEO_Auto_Optimizer_Admin_Interface class

	/**
	 * Enqueue admin scripts and styles
	 *
	 * @since 1.0.0
	 * @param string $hook_suffix Current admin page hook suffix
	 * @return void
	 */
	public function admin_enqueue_scripts( $hook_suffix ) {
		// Only load on our plugin pages
		if ( strpos( $hook_suffix, 'seo-auto-optimizer' ) === false ) {
			return;
		}

		// Enqueue admin CSS
		wp_enqueue_style(
			'seo-auto-optimizer-admin',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'css/admin.css',
			array(),
			$this->version
		);

		// Enqueue admin JS
		wp_enqueue_script(
			'seo-auto-optimizer-admin',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'js/admin.js',
			array( 'jquery' ),
			$this->version,
			true
		);

		// Localize script with nonce and AJAX URL
		wp_localize_script(
			'seo-auto-optimizer-admin',
			'seoAutoOptimizer',
			array(
				'ajaxUrl' => admin_url( 'admin-ajax.php' ),
				'nonce'   => wp_create_nonce( 'seo_auto_optimizer_nonce' ),
				'strings' => array(
					'error'   => esc_html__( 'An error occurred. Please try again.', 'seo-auto-optimizer' ),
					'success' => esc_html__( 'Operation completed successfully.', 'seo-auto-optimizer' ),
				),
			)
		);
	}

	/**
	 * Enqueue frontend scripts and styles
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function frontend_enqueue_scripts() {
		// Only load if needed
		if ( ! $this->should_load_frontend_assets() ) {
			return;
		}

		wp_enqueue_style(
			'seo-auto-optimizer-frontend',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'css/frontend.css',
			array(),
			$this->version
		);

		wp_enqueue_script(
			'seo-auto-optimizer-frontend',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'js/frontend.js',
			array( 'jquery' ),
			$this->version,
			true
		);
	}

	/**
	 * Handle AJAX requests with proper security checks
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function handle_ajax_request() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'seo_auto_optimizer_nonce' ) ) {
			wp_die(
				esc_html__( 'Security check failed.', 'seo-auto-optimizer' ),
				esc_html__( 'Error', 'seo-auto-optimizer' ),
				array( 'response' => 403 )
			);
		}

		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			wp_die(
				esc_html__( 'You do not have permission to perform this action.', 'seo-auto-optimizer' ),
				esc_html__( 'Error', 'seo-auto-optimizer' ),
				array( 'response' => 403 )
			);
		}

		// Sanitize input
		$action = sanitize_text_field( $_POST['sub_action'] ?? '' );

		// Process the request based on action
		switch ( $action ) {
			case 'analyze_content':
				$this->ajax_analyze_content();
				break;
			case 'optimize_content':
				$this->ajax_optimize_content();
				break;
			case 'refresh_plugins':
				$this->ajax_refresh_plugins();
				break;
			default:
				wp_send_json_error(
					array(
						'message' => esc_html__( 'Invalid action.', 'seo-auto-optimizer' ),
					)
				);
		}
	}

	/**
	 * Initialize SEO plugin detector
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function init_seo_detector() {
		// Initialize the SEO plugin detector
		SEO_Auto_Optimizer_Plugin_Detector::get_instance();
	}

	/**
	 * Initialize optimization interface
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function init_optimization_interface() {
		// Initialize the optimization interface
		SEO_Auto_Optimizer_Optimization_Interface::get_instance();
	}

	/**
	 * Initialize admin interface
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function init_admin_interface() {
		// Only initialize in admin
		if ( is_admin() ) {
			SEO_Auto_Optimizer_Admin_Interface::get_instance();
		}
	}

	/**
	 * Initialize boss optimization interface
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function init_boss_optimization_interface() {
		// Only initialize in admin
		if ( is_admin() ) {
			SEO_Auto_Optimizer_Boss_Optimization_Interface::get_instance();
		}
	}

	/**
	 * Load plugin options
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function load_options() {
		$default_options = array(
			'api_key'           => '',
			'auto_optimize'     => false,
			'analyze_on_save'   => true,
			'min_content_length' => 300,
		);

		$this->options = wp_parse_args(
			get_option( 'seo_auto_optimizer_options', array() ),
			$default_options
		);
	}

	/**
	 * Get plugin option
	 *
	 * @since 1.0.0
	 * @param string $key Option key
	 * @param mixed  $default Default value
	 * @return mixed Option value
	 */
	public function get_option( $key, $default = null ) {
		return $this->options[ $key ] ?? $default;
	}

	/**
	 * Plugin activation hook
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public static function activate() {
		// Create database tables if needed
		self::create_database_tables();

		// Set default options
		self::set_default_options();

		// Schedule cron events
		self::schedule_cron_events();

		// Flush rewrite rules
		flush_rewrite_rules();
	}

	/**
	 * Plugin deactivation hook
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public static function deactivate() {
		// Clear scheduled cron events
		wp_clear_scheduled_hook( 'seo_auto_optimizer_daily_cleanup' );

		// Flush rewrite rules
		flush_rewrite_rules();
	}

	/**
	 * Plugin uninstall hook
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public static function uninstall() {
		// Check if user has permission to uninstall
		if ( ! current_user_can( 'activate_plugins' ) ) {
			return;
		}

		// Remove plugin options
		delete_option( 'seo_auto_optimizer_options' );
		delete_option( 'seo_auto_optimizer_version' );

		// Remove database tables
		self::remove_database_tables();

		// Clear any cached data
		wp_cache_flush();
	}

	/**
	 * Sanitize plugin options
	 *
	 * @since 1.0.0
	 * @param array $options Raw options array
	 * @return array Sanitized options array
	 */
	public function sanitize_options( $options ) {
		$sanitized = array();

		if ( isset( $options['api_key'] ) ) {
			$sanitized['api_key'] = sanitize_text_field( $options['api_key'] );
		}

		if ( isset( $options['auto_optimize'] ) ) {
			$sanitized['auto_optimize'] = (bool) $options['auto_optimize'];
		}

		if ( isset( $options['analyze_on_save'] ) ) {
			$sanitized['analyze_on_save'] = (bool) $options['analyze_on_save'];
		}

		if ( isset( $options['min_content_length'] ) ) {
			$sanitized['min_content_length'] = absint( $options['min_content_length'] );
		}

		return $sanitized;
	}

	/**
	 * Add settings sections and fields
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function add_settings_sections() {
		add_settings_section(
			'seo_auto_optimizer_general',
			esc_html__( 'General Settings', 'seo-auto-optimizer' ),
			array( $this, 'general_settings_callback' ),
			'seo_auto_optimizer_settings'
		);

		add_settings_field(
			'api_key',
			esc_html__( 'API Key', 'seo-auto-optimizer' ),
			array( $this, 'api_key_field_callback' ),
			'seo_auto_optimizer_settings',
			'seo_auto_optimizer_general'
		);

		add_settings_field(
			'auto_optimize',
			esc_html__( 'Auto Optimize', 'seo-auto-optimizer' ),
			array( $this, 'auto_optimize_field_callback' ),
			'seo_auto_optimizer_settings',
			'seo_auto_optimizer_general'
		);
	}

	/**
	 * General settings section callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function general_settings_callback() {
		echo '<p>' . esc_html__( 'Configure the general settings for SEO Auto Optimizer.', 'seo-auto-optimizer' ) . '</p>';
	}

	/**
	 * API key field callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function api_key_field_callback() {
		$api_key = $this->get_option( 'api_key', '' );
		printf(
			'<input type="password" id="api_key" name="seo_auto_optimizer_options[api_key]" value="%s" class="regular-text" />',
			esc_attr( $api_key )
		);
		echo '<p class="description">' . esc_html__( 'Enter your AI service API key.', 'seo-auto-optimizer' ) . '</p>';
	}

	/**
	 * Auto optimize field callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function auto_optimize_field_callback() {
		$auto_optimize = $this->get_option( 'auto_optimize', false );
		printf(
			'<input type="checkbox" id="auto_optimize" name="seo_auto_optimizer_options[auto_optimize]" value="1" %s />',
			checked( $auto_optimize, true, false )
		);
		echo '<label for="auto_optimize">' . esc_html__( 'Automatically optimize content on save', 'seo-auto-optimizer' ) . '</label>';
	}

	// Admin pages now handled by SEO_Auto_Optimizer_Admin_Interface class

	/**
	 * Check if frontend assets should be loaded
	 *
	 * @since 1.0.0
	 * @return bool True if assets should be loaded
	 */
	private function should_load_frontend_assets() {
		// Load on single posts and pages
		return is_single() || is_page();
	}

	/**
	 * AJAX handler for content analysis
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function ajax_analyze_content() {
		// Sanitize input
		$content = wp_kses_post( $_POST['content'] ?? '' );
		$post_id = absint( $_POST['post_id'] ?? 0 );

		if ( empty( $content ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Content is required.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Perform content analysis (placeholder)
		$analysis_result = array(
			'score' => 75,
			'suggestions' => array(
				esc_html__( 'Add more keywords to your content.', 'seo-auto-optimizer' ),
				esc_html__( 'Improve your meta description.', 'seo-auto-optimizer' ),
			),
		);

		wp_send_json_success( $analysis_result );
	}

	/**
	 * AJAX handler for content optimization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function ajax_optimize_content() {
		// Sanitize input
		$content = wp_kses_post( $_POST['content'] ?? '' );
		$post_id = absint( $_POST['post_id'] ?? 0 );

		if ( empty( $content ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Content is required.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Perform content optimization (placeholder)
		$optimized_content = $content . ' ' . esc_html__( '[Optimized by AI]', 'seo-auto-optimizer' );

		wp_send_json_success(
			array(
				'content' => $optimized_content,
				'message' => esc_html__( 'Content optimized successfully.', 'seo-auto-optimizer' ),
			)
		);
	}

	/**
	 * AJAX handler for refreshing plugin detection
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function ajax_refresh_plugins() {
		// Get SEO plugin detector instance and delegate to it
		$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
		$detector->ajax_refresh_plugins();
	}

	/**
	 * Handle optimization AJAX requests
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function handle_optimization_ajax() {
		// Get optimization interface instance and delegate to it
		$interface = SEO_Auto_Optimizer_Optimization_Interface::get_instance();
		$interface->ajax_optimize_content();
	}

	/**
	 * Create database tables
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private static function create_database_tables() {
		global $wpdb;

		$charset_collate = $wpdb->get_charset_collate();

		$table_name = $wpdb->prefix . 'seo_auto_optimizer_logs';

		$sql = "CREATE TABLE $table_name (
			id mediumint(9) NOT NULL AUTO_INCREMENT,
			post_id bigint(20) NOT NULL,
			action varchar(50) NOT NULL,
			data longtext,
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY post_id (post_id),
			KEY action (action)
		) $charset_collate;";

		require_once ABSPATH . 'wp-admin/includes/upgrade.php';
		dbDelta( $sql );

		// Save database version
		update_option( 'seo_auto_optimizer_db_version', '1.0' );
	}

	/**
	 * Set default options
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private static function set_default_options() {
		$default_options = array(
			'api_key'           => '',
			'auto_optimize'     => false,
			'analyze_on_save'   => true,
			'min_content_length' => 300,
		);

		add_option( 'seo_auto_optimizer_options', $default_options );
		add_option( 'seo_auto_optimizer_version', SEO_AUTO_OPTIMIZER_VERSION );
	}

	/**
	 * Schedule cron events
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private static function schedule_cron_events() {
		if ( ! wp_next_scheduled( 'seo_auto_optimizer_daily_cleanup' ) ) {
			wp_schedule_event( time(), 'daily', 'seo_auto_optimizer_daily_cleanup' );
		}
	}

	/**
	 * Remove database tables
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private static function remove_database_tables() {
		global $wpdb;

		$table_name = $wpdb->prefix . 'seo_auto_optimizer_logs';
		$wpdb->query( $wpdb->prepare( "DROP TABLE IF EXISTS %s", $table_name ) );

		delete_option( 'seo_auto_optimizer_db_version' );
	}
}

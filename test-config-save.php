<?php
/**
 * Test Configuration Save
 * 
 * Script de test pour vérifier que la sauvegarde fonctionne
 */

// Simuler les données POST
$_POST = array(
    'save_settings' => '1',
    'sao_nonce' => 'test_nonce',
    
    // Paramètres généraux
    'keywords_count' => '8',
    'primary_seo_plugin' => 'yoast',
    'backup_enabled' => '1',
    'log_level' => 'errors',
    
    // Paramètres AI
    'gemini_api_key' => 'test_gemini_key_123456789',
    'openai_api_key' => 'test_openai_key_123456789',
    'anthropic_api_key' => '',
    'ollama_endpoint' => 'http://localhost:11434/api/generate',
    'ollama_model' => 'llama2',
    'custom_endpoint' => '',
    'primary_provider' => 'gemini',
    
    // Paramètres avancés
    'cache_duration' => '86400',
    'rate_limit' => '5',
    'debug_mode' => '1',
    'auto_apply' => '0',
);

echo "🧪 Test de sauvegarde des paramètres\n\n";

echo "📋 Données POST simulées :\n";
foreach ($_POST as $key => $value) {
    if (strpos($key, 'api_key') !== false && !empty($value)) {
        $value = substr($value, 0, 4) . '***' . substr($value, -4);
    }
    echo "  {$key}: {$value}\n";
}

echo "\n✅ Test terminé !\n";
echo "👉 Maintenant testez dans WordPress :\n";
echo "1. Allez dans SEO Optimizer > Configuration\n";
echo "2. Remplissez les champs\n";
echo "3. Cliquez sur 'SAUVEGARDER TOUS LES PARAMÈTRES'\n";
echo "4. Vérifiez le message de confirmation\n";
echo "5. Rechargez la page pour voir si les valeurs sont conservées\n";
?>

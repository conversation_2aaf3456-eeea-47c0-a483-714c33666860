<?php
/**
 * AI Keyword Generator Class
 *
 * This class handles AI API integrations for keyword generation
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * AI Keyword Generator Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_AI_Keyword_Generator {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_AI_Keyword_Generator|null
	 */
	private static $instance = null;

	/**
	 * Cache key prefix for keyword results
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $cache_key_prefix = 'sao_ai_keywords_';

	/**
	 * Cache expiration time (24 hours)
	 *
	 * @since 1.0.0
	 * @var int
	 */
	private $cache_expiration = 86400; // 24 hours

	/**
	 * API timeout in seconds
	 *
	 * @since 1.0.0
	 * @var int
	 */
	private $api_timeout = 30;

	/**
	 * Supported AI providers
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $supported_providers = array(
		'gemini' => array(
			'name'     => 'Google Gemini',
			'endpoint' => 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
			'method'   => 'POST',
		),
		'openai' => array(
			'name'     => 'OpenAI GPT',
			'endpoint' => 'https://api.openai.com/v1/chat/completions',
			'method'   => 'POST',
		),
		'anthropic' => array(
			'name'     => 'Anthropic Claude',
			'endpoint' => 'https://api.anthropic.com/v1/messages',
			'method'   => 'POST',
		),
		'ollama' => array(
			'name'     => 'Ollama',
			'endpoint' => 'http://localhost:11434/api/generate',
			'method'   => 'POST',
		),
		'custom' => array(
			'name'     => 'Custom API',
			'endpoint' => '',
			'method'   => 'POST',
		),
	);

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_AI_Keyword_Generator Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Initialize the generator
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Add AJAX handlers for API testing
		add_action( 'wp_ajax_sao_test_ai_connection', array( $this, 'ajax_test_ai_connection' ) );
	}

	/**
	 * Generate keywords using AI
	 *
	 * @since 1.0.0
	 * @param string $title Post title
	 * @param string $content Post content (optional)
	 * @return array Generated keywords with scores
	 */
	public function generate_keywords( $title, $content = '' ) {
		// Validate input
		if ( empty( trim( $title ) ) ) {
			return array(
				'success' => false,
				'error'   => esc_html__( 'Title is required for keyword generation.', 'seo-auto-optimizer' ),
			);
		}

		// Create cache key
		$cache_key = $this->get_cache_key( $title, $content );

		// Try to get from cache first
		$cached_result = get_transient( $cache_key );
		if ( false !== $cached_result && is_array( $cached_result ) ) {
			$cached_result['from_cache'] = true;
			return $cached_result;
		}

		// Get AI configuration
		$ai_config = $this->get_ai_configuration();
		if ( ! $ai_config['enabled'] ) {
			return $this->get_fallback_keywords( $title, $content );
		}

		// Try to generate keywords with AI
		$result = $this->generate_with_ai( $title, $content, $ai_config );

		// Cache successful results
		if ( $result['success'] ) {
			set_transient( $cache_key, $result, $this->cache_expiration );
		}

		return $result;
	}

	/**
	 * Generate keywords with AI providers
	 *
	 * @since 1.0.0
	 * @param string $title Post title
	 * @param string $content Post content
	 * @param array  $config AI configuration
	 * @return array Generation result
	 */
	private function generate_with_ai( $title, $content, $config ) {
		$providers = $config['providers'];
		$last_error = '';

		// Try each provider in order
		foreach ( $providers as $provider ) {
			try {
				$result = $this->call_ai_provider( $provider, $title, $content );
				
				if ( $result['success'] ) {
					$result['provider'] = $provider;
					return $result;
				}
				
				$last_error = $result['error'] ?? esc_html__( 'Unknown error', 'seo-auto-optimizer' );
				
			} catch ( Exception $e ) {
				$last_error = $e->getMessage();
				$this->log_error( "AI Provider {$provider} failed: " . $e->getMessage() );
			}
		}

		// All providers failed, return fallback
		$fallback_result = $this->get_fallback_keywords( $title, $content );
		$fallback_result['ai_error'] = $last_error;
		
		return $fallback_result;
	}

	/**
	 * Call specific AI provider
	 *
	 * @since 1.0.0
	 * @param string $provider Provider name
	 * @param string $title Post title
	 * @param string $content Post content
	 * @return array API response
	 */
	private function call_ai_provider( $provider, $title, $content ) {
		if ( ! isset( $this->supported_providers[ $provider ] ) ) {
			throw new Exception( sprintf( 
				esc_html__( 'Unsupported AI provider: %s', 'seo-auto-optimizer' ), 
				$provider 
			) );
		}

		$provider_config = $this->supported_providers[ $provider ];
		$api_key = $this->get_api_key( $provider );

		if ( empty( $api_key ) && $provider !== 'ollama' ) {
			throw new Exception( sprintf( 
				esc_html__( 'API key not configured for %s', 'seo-auto-optimizer' ), 
				$provider_config['name'] 
			) );
		}

		// Prepare prompt
		$prompt = $this->build_ai_prompt( $title, $content );

		// Call the specific provider
		switch ( $provider ) {
			case 'gemini':
				return $this->call_gemini_api( $api_key, $prompt );
			case 'openai':
				return $this->call_openai_api( $api_key, $prompt );
			case 'anthropic':
				return $this->call_anthropic_api( $api_key, $prompt );
			case 'ollama':
				return $this->call_ollama_api( $prompt );
			case 'custom':
				return $this->call_custom_api( $api_key, $prompt );
			default:
				throw new Exception( esc_html__( 'Provider method not implemented', 'seo-auto-optimizer' ) );
		}
	}

	/**
	 * Build AI prompt for keyword generation
	 *
	 * @since 1.0.0
	 * @param string $title Post title
	 * @param string $content Post content
	 * @return string Formatted prompt
	 */
	private function build_ai_prompt( $title, $content ) {
		$content_excerpt = '';
		if ( ! empty( $content ) ) {
			$clean_content = strip_tags( $content );
			$content_excerpt = substr( $clean_content, 0, 500 );
		}

		$prompt = "Tu es un expert SEO français. Génère 5-10 mots-clés SEO pertinents pour ce contenu.\n\n";
		$prompt .= "Titre: {$title}\n";
		
		if ( ! empty( $content_excerpt ) ) {
			$prompt .= "Début du contenu: {$content_excerpt}...\n";
		}
		
		$prompt .= "\nRéponds UNIQUEMENT avec un JSON valide dans ce format exact:\n";
		$prompt .= "{\n";
		$prompt .= '  "keywords": [' . "\n";
		$prompt .= '    {"keyword": "mot-clé 1", "difficulty": 65, "relevance": 90},' . "\n";
		$prompt .= '    {"keyword": "mot-clé 2", "difficulty": 45, "relevance": 85}' . "\n";
		$prompt .= "  ]\n";
		$prompt .= "}\n\n";
		$prompt .= "Critères:\n";
		$prompt .= "- Mots-clés en français\n";
		$prompt .= "- Difficulty: score 1-100 (difficulté de classement)\n";
		$prompt .= "- Relevance: score 1-100 (pertinence pour le contenu)\n";
		$prompt .= "- Mélange de mots-clés courts et longue traîne\n";
		$prompt .= "- Pas de caractères spéciaux dans les mots-clés\n";

		return $prompt;
	}

	/**
	 * Get fallback keywords (non-AI)
	 *
	 * @since 1.0.0
	 * @param string $title Post title
	 * @param string $content Post content
	 * @return array Fallback keywords
	 */
	private function get_fallback_keywords( $title, $content ) {
		// Extract keywords from title and content
		$text = strtolower( strip_tags( $title . ' ' . $content ) );
		$text = preg_replace( '/[^a-z0-9\s]/', ' ', $text );
		$words = array_filter( explode( ' ', $text ) );

		// Remove stop words
		$stop_words = array(
			'le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour',
			'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus',
			'par', 'grand', 'mais', 'que', 'très', 'bien', 'autre', 'depuis', 'sans',
			'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
		);
		
		$words = array_diff( $words, $stop_words );
		$words = array_filter( $words, function( $word ) {
			return strlen( $word ) >= 3;
		});

		// Count frequency and get top words
		$word_counts = array_count_values( $words );
		arsort( $word_counts );
		
		$keywords = array();
		$count = 0;
		foreach ( $word_counts as $word => $frequency ) {
			if ( $count >= 8 ) break;
			
			$keywords[] = array(
				'keyword'   => $word,
				'difficulty' => rand( 30, 70 ),
				'relevance'  => min( 90, $frequency * 20 ),
			);
			$count++;
		}

		// Add some generic SEO keywords if we don't have enough
		if ( count( $keywords ) < 5 ) {
			$generic_keywords = array(
				array( 'keyword' => 'seo', 'difficulty' => 80, 'relevance' => 60 ),
				array( 'keyword' => 'optimisation', 'difficulty' => 65, 'relevance' => 70 ),
				array( 'keyword' => 'contenu', 'difficulty' => 45, 'relevance' => 75 ),
				array( 'keyword' => 'marketing digital', 'difficulty' => 70, 'relevance' => 65 ),
			);
			
			$keywords = array_merge( $keywords, array_slice( $generic_keywords, 0, 5 - count( $keywords ) ) );
		}

		return array(
			'success'   => true,
			'keywords'  => $keywords,
			'provider'  => 'fallback',
			'from_cache' => false,
		);
	}

	/**
	 * Call Google Gemini API
	 *
	 * @since 1.0.0
	 * @param string $api_key API key
	 * @param string $prompt Prompt text
	 * @return array API response
	 */
	private function call_gemini_api( $api_key, $prompt ) {
		$endpoint = $this->supported_providers['gemini']['endpoint'] . '?key=' . $api_key;

		$body = array(
			'contents' => array(
				array(
					'parts' => array(
						array( 'text' => $prompt )
					)
				)
			),
			'generationConfig' => array(
				'temperature' => 0.3,
				'maxOutputTokens' => 1000,
			)
		);

		$response = wp_remote_post( $endpoint, array(
			'timeout' => $this->api_timeout,
			'headers' => array(
				'Content-Type' => 'application/json',
			),
			'body' => wp_json_encode( $body ),
		) );

		if ( is_wp_error( $response ) ) {
			throw new Exception( 'Gemini API request failed: ' . $response->get_error_message() );
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		$response_body = wp_remote_retrieve_body( $response );

		if ( $response_code !== 200 ) {
			throw new Exception( "Gemini API returned error {$response_code}: {$response_body}" );
		}

		$data = json_decode( $response_body, true );
		if ( ! $data || ! isset( $data['candidates'][0]['content']['parts'][0]['text'] ) ) {
			throw new Exception( 'Invalid Gemini API response format' );
		}

		$ai_response = $data['candidates'][0]['content']['parts'][0]['text'];
		return $this->parse_ai_response( $ai_response );
	}

	/**
	 * Call OpenAI API
	 *
	 * @since 1.0.0
	 * @param string $api_key API key
	 * @param string $prompt Prompt text
	 * @return array API response
	 */
	private function call_openai_api( $api_key, $prompt ) {
		$endpoint = $this->supported_providers['openai']['endpoint'];

		$body = array(
			'model' => 'gpt-3.5-turbo',
			'messages' => array(
				array(
					'role' => 'user',
					'content' => $prompt
				)
			),
			'temperature' => 0.3,
			'max_tokens' => 1000,
		);

		$response = wp_remote_post( $endpoint, array(
			'timeout' => $this->api_timeout,
			'headers' => array(
				'Content-Type' => 'application/json',
				'Authorization' => 'Bearer ' . $api_key,
			),
			'body' => wp_json_encode( $body ),
		) );

		if ( is_wp_error( $response ) ) {
			throw new Exception( 'OpenAI API request failed: ' . $response->get_error_message() );
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		$response_body = wp_remote_retrieve_body( $response );

		if ( $response_code !== 200 ) {
			throw new Exception( "OpenAI API returned error {$response_code}: {$response_body}" );
		}

		$data = json_decode( $response_body, true );
		if ( ! $data || ! isset( $data['choices'][0]['message']['content'] ) ) {
			throw new Exception( 'Invalid OpenAI API response format' );
		}

		$ai_response = $data['choices'][0]['message']['content'];
		return $this->parse_ai_response( $ai_response );
	}

	/**
	 * Call Anthropic API
	 *
	 * @since 1.0.0
	 * @param string $api_key API key
	 * @param string $prompt Prompt text
	 * @return array API response
	 */
	private function call_anthropic_api( $api_key, $prompt ) {
		$endpoint = $this->supported_providers['anthropic']['endpoint'];

		$body = array(
			'model' => 'claude-3-haiku-20240307',
			'max_tokens' => 1000,
			'messages' => array(
				array(
					'role' => 'user',
					'content' => $prompt
				)
			)
		);

		$response = wp_remote_post( $endpoint, array(
			'timeout' => $this->api_timeout,
			'headers' => array(
				'Content-Type' => 'application/json',
				'x-api-key' => $api_key,
				'anthropic-version' => '2023-06-01',
			),
			'body' => wp_json_encode( $body ),
		) );

		if ( is_wp_error( $response ) ) {
			throw new Exception( 'Anthropic API request failed: ' . $response->get_error_message() );
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		$response_body = wp_remote_retrieve_body( $response );

		if ( $response_code !== 200 ) {
			throw new Exception( "Anthropic API returned error {$response_code}: {$response_body}" );
		}

		$data = json_decode( $response_body, true );
		if ( ! $data || ! isset( $data['content'][0]['text'] ) ) {
			throw new Exception( 'Invalid Anthropic API response format' );
		}

		$ai_response = $data['content'][0]['text'];
		return $this->parse_ai_response( $ai_response );
	}

	/**
	 * Call Ollama API
	 *
	 * @since 1.0.0
	 * @param string $prompt Prompt text
	 * @return array API response
	 */
	private function call_ollama_api( $prompt ) {
		$endpoint = $this->get_ollama_endpoint();
		$model = $this->get_ollama_model();

		$body = array(
			'model' => $model,
			'prompt' => $prompt,
			'stream' => false,
		);

		$response = wp_remote_post( $endpoint, array(
			'timeout' => $this->api_timeout,
			'headers' => array(
				'Content-Type' => 'application/json',
			),
			'body' => wp_json_encode( $body ),
		) );

		if ( is_wp_error( $response ) ) {
			throw new Exception( 'Ollama API request failed: ' . $response->get_error_message() );
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		$response_body = wp_remote_retrieve_body( $response );

		if ( $response_code !== 200 ) {
			throw new Exception( "Ollama API returned error {$response_code}: {$response_body}" );
		}

		$data = json_decode( $response_body, true );
		if ( ! $data || ! isset( $data['response'] ) ) {
			throw new Exception( 'Invalid Ollama API response format' );
		}

		$ai_response = $data['response'];
		return $this->parse_ai_response( $ai_response );
	}

	/**
	 * Call Custom API
	 *
	 * @since 1.0.0
	 * @param string $api_key API key
	 * @param string $prompt Prompt text
	 * @return array API response
	 */
	private function call_custom_api( $api_key, $prompt ) {
		$endpoint = $this->get_custom_endpoint();

		if ( empty( $endpoint ) ) {
			throw new Exception( 'Custom API endpoint not configured' );
		}

		$body = array(
			'prompt' => $prompt,
			'api_key' => $api_key,
		);

		$response = wp_remote_post( $endpoint, array(
			'timeout' => $this->api_timeout,
			'headers' => array(
				'Content-Type' => 'application/json',
			),
			'body' => wp_json_encode( $body ),
		) );

		if ( is_wp_error( $response ) ) {
			throw new Exception( 'Custom API request failed: ' . $response->get_error_message() );
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		$response_body = wp_remote_retrieve_body( $response );

		if ( $response_code !== 200 ) {
			throw new Exception( "Custom API returned error {$response_code}: {$response_body}" );
		}

		$data = json_decode( $response_body, true );
		if ( ! $data ) {
			throw new Exception( 'Invalid Custom API response format' );
		}

		// Assume custom API returns the keywords directly or in a 'response' field
		if ( isset( $data['keywords'] ) ) {
			return array(
				'success' => true,
				'keywords' => $data['keywords'],
			);
		} elseif ( isset( $data['response'] ) ) {
			return $this->parse_ai_response( $data['response'] );
		} else {
			return $this->parse_ai_response( $response_body );
		}
	}

	/**
	 * Parse AI response to extract keywords
	 *
	 * @since 1.0.0
	 * @param string $response AI response text
	 * @return array Parsed keywords
	 */
	private function parse_ai_response( $response ) {
		// Clean the response
		$response = trim( $response );

		// Try to extract JSON from the response
		$json_start = strpos( $response, '{' );
		$json_end = strrpos( $response, '}' );

		if ( $json_start !== false && $json_end !== false ) {
			$json_string = substr( $response, $json_start, $json_end - $json_start + 1 );
			$data = json_decode( $json_string, true );

			if ( $data && isset( $data['keywords'] ) && is_array( $data['keywords'] ) ) {
				// Validate and sanitize keywords
				$keywords = array();
				foreach ( $data['keywords'] as $keyword_data ) {
					if ( isset( $keyword_data['keyword'] ) ) {
						$keywords[] = array(
							'keyword'   => sanitize_text_field( $keyword_data['keyword'] ),
							'difficulty' => absint( $keyword_data['difficulty'] ?? 50 ),
							'relevance'  => absint( $keyword_data['relevance'] ?? 50 ),
						);
					}
				}

				if ( ! empty( $keywords ) ) {
					return array(
						'success' => true,
						'keywords' => $keywords,
					);
				}
			}
		}

		// Fallback: try to extract keywords from plain text
		$lines = explode( "\n", $response );
		$keywords = array();

		foreach ( $lines as $line ) {
			$line = trim( $line );
			if ( empty( $line ) || strlen( $line ) < 3 ) {
				continue;
			}

			// Remove common prefixes
			$line = preg_replace( '/^[-*•]\s*/', '', $line );
			$line = preg_replace( '/^\d+\.\s*/', '', $line );

			if ( strlen( $line ) >= 3 && strlen( $line ) <= 50 ) {
				$keywords[] = array(
					'keyword'   => sanitize_text_field( $line ),
					'difficulty' => rand( 40, 80 ),
					'relevance'  => rand( 60, 90 ),
				);

				if ( count( $keywords ) >= 8 ) {
					break;
				}
			}
		}

		if ( ! empty( $keywords ) ) {
			return array(
				'success' => true,
				'keywords' => $keywords,
			);
		}

		throw new Exception( 'Could not parse AI response: ' . substr( $response, 0, 200 ) );
	}

	/**
	 * Get AI configuration
	 *
	 * @since 1.0.0
	 * @return array AI configuration
	 */
	private function get_ai_configuration() {
		$options = get_option( 'seo_auto_optimizer_ai_options', array() );

		$default_config = array(
			'enabled' => false,
			'primary_provider' => 'gemini',
			'providers' => array( 'gemini' ),
			'fallback_enabled' => true,
		);

		$config = wp_parse_args( $options, $default_config );

		// Ensure providers is an array
		if ( ! is_array( $config['providers'] ) ) {
			$config['providers'] = array( $config['primary_provider'] );
		}

		// Remove providers without API keys (except Ollama)
		$valid_providers = array();
		foreach ( $config['providers'] as $provider ) {
			if ( $provider === 'ollama' || ! empty( $this->get_api_key( $provider ) ) ) {
				$valid_providers[] = $provider;
			}
		}

		$config['providers'] = $valid_providers;
		$config['enabled'] = ! empty( $valid_providers );

		return $config;
	}

	/**
	 * Get API key for provider
	 *
	 * @since 1.0.0
	 * @param string $provider Provider name
	 * @return string API key
	 */
	private function get_api_key( $provider ) {
		$option_key = "seo_auto_optimizer_{$provider}_api_key";
		$encrypted_key = get_option( $option_key, '' );

		if ( empty( $encrypted_key ) ) {
			return '';
		}

		// Decrypt the API key
		return $this->decrypt_api_key( $encrypted_key );
	}

	/**
	 * Set API key for provider
	 *
	 * @since 1.0.0
	 * @param string $provider Provider name
	 * @param string $api_key API key
	 * @return bool Success status
	 */
	public function set_api_key( $provider, $api_key ) {
		if ( ! isset( $this->supported_providers[ $provider ] ) ) {
			return false;
		}

		$option_key = "seo_auto_optimizer_{$provider}_api_key";

		if ( empty( $api_key ) ) {
			return delete_option( $option_key );
		}

		// Encrypt the API key before storing
		$encrypted_key = $this->encrypt_api_key( $api_key );
		return update_option( $option_key, $encrypted_key );
	}

	/**
	 * Encrypt API key
	 *
	 * @since 1.0.0
	 * @param string $api_key API key to encrypt
	 * @return string Encrypted API key
	 */
	private function encrypt_api_key( $api_key ) {
		if ( ! function_exists( 'openssl_encrypt' ) ) {
			// Fallback to base64 encoding if OpenSSL is not available
			return base64_encode( $api_key );
		}

		$encryption_key = $this->get_encryption_key();
		$iv = openssl_random_pseudo_bytes( 16 );
		$encrypted = openssl_encrypt( $api_key, 'AES-256-CBC', $encryption_key, 0, $iv );

		return base64_encode( $iv . $encrypted );
	}

	/**
	 * Decrypt API key
	 *
	 * @since 1.0.0
	 * @param string $encrypted_key Encrypted API key
	 * @return string Decrypted API key
	 */
	private function decrypt_api_key( $encrypted_key ) {
		if ( ! function_exists( 'openssl_decrypt' ) ) {
			// Fallback to base64 decoding if OpenSSL is not available
			return base64_decode( $encrypted_key );
		}

		$encryption_key = $this->get_encryption_key();
		$data = base64_decode( $encrypted_key );

		if ( strlen( $data ) < 16 ) {
			return base64_decode( $encrypted_key ); // Fallback for old format
		}

		$iv = substr( $data, 0, 16 );
		$encrypted = substr( $data, 16 );

		return openssl_decrypt( $encrypted, 'AES-256-CBC', $encryption_key, 0, $iv );
	}

	/**
	 * Get encryption key
	 *
	 * @since 1.0.0
	 * @return string Encryption key
	 */
	private function get_encryption_key() {
		$key = get_option( 'seo_auto_optimizer_encryption_key' );

		if ( empty( $key ) ) {
			$key = wp_generate_password( 32, false );
			update_option( 'seo_auto_optimizer_encryption_key', $key );
		}

		return hash( 'sha256', $key . SECURE_AUTH_KEY );
	}

	/**
	 * Get cache key for title and content
	 *
	 * @since 1.0.0
	 * @param string $title Post title
	 * @param string $content Post content
	 * @return string Cache key
	 */
	private function get_cache_key( $title, $content ) {
		$content_hash = md5( $title . substr( strip_tags( $content ), 0, 500 ) );
		return $this->cache_key_prefix . $content_hash;
	}

	/**
	 * Get Ollama endpoint
	 *
	 * @since 1.0.0
	 * @return string Ollama endpoint
	 */
	private function get_ollama_endpoint() {
		return get_option( 'seo_auto_optimizer_ollama_endpoint', 'http://localhost:11434/api/generate' );
	}

	/**
	 * Get Ollama model
	 *
	 * @since 1.0.0
	 * @return string Ollama model
	 */
	private function get_ollama_model() {
		return get_option( 'seo_auto_optimizer_ollama_model', 'llama2' );
	}

	/**
	 * Get custom API endpoint
	 *
	 * @since 1.0.0
	 * @return string Custom API endpoint
	 */
	private function get_custom_endpoint() {
		return get_option( 'seo_auto_optimizer_custom_endpoint', '' );
	}

	/**
	 * Log error message
	 *
	 * @since 1.0.0
	 * @param string $message Error message
	 * @return void
	 */
	private function log_error( $message ) {
		if ( defined( 'WP_DEBUG_LOG' ) && WP_DEBUG_LOG ) {
			error_log( 'SEO Auto Optimizer AI: ' . $message );
		}
	}
}

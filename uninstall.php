<?php
/**
 * Uninstall script for SEO Auto Optimizer
 *
 * This file is executed when the plugin is uninstalled via WordPress admin.
 * It removes all plugin data, options, and database tables.
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

// Check if uninstall is triggered from WordPress
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
	exit; // Exit if not called from WordPress
}

// Security check: verify the user has permission to uninstall plugins
if ( ! current_user_can( 'activate_plugins' ) ) {
	exit; // Exit if user doesn't have permission
}

// Additional security check: verify the plugin file
if ( plugin_basename( __FILE__ ) !== WP_UNINSTALL_PLUGIN ) {
	exit; // Exit if plugin file doesn't match
}

/**
 * Remove all plugin data
 *
 * @since 1.0.0
 * @return void
 */
function seo_auto_optimizer_remove_plugin_data() {
	global $wpdb;

	// Remove plugin options
	$options_to_remove = array(
		'seo_auto_optimizer_options',
		'seo_auto_optimizer_version',
		'seo_auto_optimizer_db_version',
	);

	foreach ( $options_to_remove as $option ) {
		delete_option( $option );
		delete_site_option( $option ); // For multisite
	}

	// Remove user meta data
	$wpdb->query(
		$wpdb->prepare(
			"DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE %s",
			'seo_auto_optimizer_%'
		)
	);

	// Remove post meta data
	$wpdb->query(
		$wpdb->prepare(
			"DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE %s",
			'_seo_auto_optimizer_%'
		)
	);

	// Remove custom database tables
	$table_name = $wpdb->prefix . 'seo_auto_optimizer_logs';
	$wpdb->query( "DROP TABLE IF EXISTS {$table_name}" );

	// Clear any scheduled cron events
	wp_clear_scheduled_hook( 'seo_auto_optimizer_daily_cleanup' );

	// Clear any cached data
	wp_cache_flush();

	// Remove any uploaded files (if any)
	$upload_dir = wp_upload_dir();
	$plugin_upload_dir = $upload_dir['basedir'] . '/seo-auto-optimizer/';
	
	if ( is_dir( $plugin_upload_dir ) ) {
		seo_auto_optimizer_remove_directory( $plugin_upload_dir );
	}
}

/**
 * Recursively remove directory and its contents
 *
 * @since 1.0.0
 * @param string $dir Directory path to remove
 * @return bool True on success, false on failure
 */
function seo_auto_optimizer_remove_directory( $dir ) {
	if ( ! is_dir( $dir ) ) {
		return false;
	}

	$files = array_diff( scandir( $dir ), array( '.', '..' ) );
	
	foreach ( $files as $file ) {
		$file_path = $dir . DIRECTORY_SEPARATOR . $file;
		
		if ( is_dir( $file_path ) ) {
			seo_auto_optimizer_remove_directory( $file_path );
		} else {
			unlink( $file_path );
		}
	}
	
	return rmdir( $dir );
}

// Execute the uninstall process
seo_auto_optimizer_remove_plugin_data();

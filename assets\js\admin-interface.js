/**
 * SEO Auto Optimizer - Admin Interface JavaScript
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main admin interface object
     */
    var SAOAdminInterface = {
        
        /**
         * Charts instances
         */
        charts: {},
        
        /**
         * Initialize the interface
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initCharts();
            this.initSortable();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;

            // Tab navigation
            $(document).on('click', '.nav-tab', function(e) {
                self.handleTabClick.call(this, e);
            });

            // Quick actions
            $(document).on('click', '.sao-action-btn', function(e) {
                self.handleQuickAction.call(this, e);
            });

            // API testing
            $(document).on('click', '.sao-test-api', function(e) {
                self.handleApiTest.call(this, e);
            });

            // Import/Export
            $(document).on('click', '.sao-export-btn', function(e) {
                self.handleExport.call(this, e);
            });
            $(document).on('click', '.sao-import-btn', function(e) {
                self.handleImport.call(this, e);
            });
            $(document).on('change', '.sao-import-file', function(e) {
                self.handleFileSelect.call(this, e);
            });
        },

        /**
         * Initialize tabs
         */
        initTabs: function() {
            // Hide all tab content first
            $('.sao-tab-content').hide().removeClass('sao-tab-active');

            // Show the first tab (general) by default
            $('[id="general"]').addClass('sao-tab-active').show();

            // Make sure the first nav tab is active
            $('.nav-tab').removeClass('nav-tab-active');
            $('.nav-tab[data-tab="general"]').addClass('nav-tab-active');
        },

        /**
         * Handle tab click
         */
        handleTabClick: function(e) {
            e.preventDefault();

            var $tab = $(this);
            var targetTab = $tab.data('tab');

            // Update tab navigation
            $('.nav-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');

            // Update tab content - use attribute selector for IDs with special characters
            $('.sao-tab-content').removeClass('sao-tab-active').hide();
            $('[id="' + targetTab + '"]').addClass('sao-tab-active').show();
        },

        /**
         * Initialize charts
         */
        initCharts: function() {
            if (typeof Chart === 'undefined' || !saoAdmin.stats) {
                return;
            }

            // Optimization results chart
            this.initOptimizationChart();
            
            // API usage chart
            this.initApiUsageChart();
        },

        /**
         * Initialize optimization chart
         */
        initOptimizationChart: function() {
            var ctx = document.getElementById('sao-optimization-chart');
            if (!ctx) return;

            var data = saoAdmin.stats.optimizations;
            
            this.charts.optimization = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Successful', 'Failed'],
                    datasets: [{
                        data: [data.successful, data.failed],
                        backgroundColor: ['#00a32a', '#d63638'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        },

        /**
         * Initialize API usage chart
         */
        initApiUsageChart: function() {
            var ctx = document.getElementById('sao-api-usage-chart');
            if (!ctx) return;

            var data = saoAdmin.stats.api_usage;
            var labels = Object.keys(data);
            var values = Object.values(data);
            
            this.charts.apiUsage = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'API Calls',
                        data: values,
                        backgroundColor: '#0073aa',
                        borderColor: '#005a87',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        },

        /**
         * Initialize sortable lists
         */
        initSortable: function() {
            if (typeof $.fn.sortable !== 'undefined') {
                $('#sao-provider-priority').sortable({
                    update: function(event, ui) {
                        // Update hidden inputs order
                        $(this).find('li').each(function(index) {
                            $(this).find('input').attr('name', 'seo_auto_optimizer_ai_options[provider_order][' + index + ']');
                        });
                    }
                });
            }
        },

        /**
         * Handle quick actions
         */
        handleQuickAction: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var action = $button.data('action');
            
            switch (action) {
                case 'clear-cache':
                    SAOAdminInterface.clearCache($button);
                    break;
                case 'test-apis':
                    SAOAdminInterface.testAllApis($button);
                    break;
                case 'export-settings':
                    SAOAdminInterface.exportSettings($button);
                    break;
                case 'reset-stats':
                    SAOAdminInterface.resetStats($button);
                    break;
            }
        },

        /**
         * Clear cache
         */
        clearCache: function($button) {
            if (!confirm(saoAdmin.strings.confirmClearCache)) {
                return;
            }
            
            this.setButtonLoading($button, true);
            
            $.post(saoAdmin.ajaxUrl, {
                action: 'sao_clear_cache',
                nonce: saoAdmin.nonce
            })
            .done(function(response) {
                if (response.success) {
                    SAOAdminInterface.showNotice('success', response.data.message || saoAdmin.strings.success);
                } else {
                    SAOAdminInterface.showNotice('error', response.data.message || saoAdmin.strings.error);
                }
            })
            .fail(function() {
                SAOAdminInterface.showNotice('error', saoAdmin.strings.error);
            })
            .always(function() {
                SAOAdminInterface.setButtonLoading($button, false);
            });
        },

        /**
         * Test all APIs
         */
        testAllApis: function($button) {
            this.setButtonLoading($button, true);
            
            var providers = ['gemini', 'openai', 'anthropic', 'ollama', 'custom'];
            var results = [];
            var completed = 0;
            
            providers.forEach(function(provider) {
                SAOAdminInterface.testSingleApi(provider, function(result) {
                    results.push(result);
                    completed++;
                    
                    if (completed === providers.length) {
                        SAOAdminInterface.setButtonLoading($button, false);
                        SAOAdminInterface.showApiTestResults(results);
                    }
                });
            });
        },

        /**
         * Test single API
         */
        testSingleApi: function(provider, callback) {
            $.post(saoAdmin.ajaxUrl, {
                action: 'sao_test_api_connection',
                nonce: saoAdmin.nonce,
                provider: provider
            })
            .done(function(response) {
                callback({
                    provider: provider,
                    success: response.success,
                    message: response.data.message || (response.success ? 'Success' : 'Failed')
                });
            })
            .fail(function() {
                callback({
                    provider: provider,
                    success: false,
                    message: 'Connection failed'
                });
            });
        },

        /**
         * Show API test results
         */
        showApiTestResults: function(results) {
            var message = 'API Test Results:\n\n';
            
            results.forEach(function(result) {
                var status = result.success ? '✓' : '✗';
                message += status + ' ' + result.provider + ': ' + result.message + '\n';
            });
            
            alert(message);
        },

        /**
         * Export settings
         */
        exportSettings: function($button) {
            this.setButtonLoading($button, true);
            
            $.post(saoAdmin.ajaxUrl, {
                action: 'sao_export_settings',
                nonce: saoAdmin.nonce
            })
            .done(function(response) {
                if (response.success) {
                    SAOAdminInterface.downloadFile(response.data.filename, response.data.content);
                    SAOAdminInterface.showNotice('success', 'Settings exported successfully');
                } else {
                    SAOAdminInterface.showNotice('error', response.data.message || saoAdmin.strings.error);
                }
            })
            .fail(function() {
                SAOAdminInterface.showNotice('error', saoAdmin.strings.error);
            })
            .always(function() {
                SAOAdminInterface.setButtonLoading($button, false);
            });
        },

        /**
         * Reset statistics
         */
        resetStats: function($button) {
            if (!confirm(saoAdmin.strings.confirmReset)) {
                return;
            }
            
            this.setButtonLoading($button, true);
            
            $.post(saoAdmin.ajaxUrl, {
                action: 'sao_reset_stats',
                nonce: saoAdmin.nonce
            })
            .done(function(response) {
                if (response.success) {
                    SAOAdminInterface.showNotice('success', response.data.message || saoAdmin.strings.success);
                    // Refresh page to update charts
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    SAOAdminInterface.showNotice('error', response.data.message || saoAdmin.strings.error);
                }
            })
            .fail(function() {
                SAOAdminInterface.showNotice('error', saoAdmin.strings.error);
            })
            .always(function() {
                SAOAdminInterface.setButtonLoading($button, false);
            });
        },

        /**
         * Handle API test
         */
        handleApiTest: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var provider = $button.data('provider');
            var $result = $('#test-result-' + provider);
            
            $button.prop('disabled', true).text(saoAdmin.strings.testing);
            $result.removeClass('success error').addClass('testing').text('Testing...');
            
            $.post(saoAdmin.ajaxUrl, {
                action: 'sao_test_api_connection',
                nonce: saoAdmin.nonce,
                provider: provider
            })
            .done(function(response) {
                if (response.success) {
                    $result.removeClass('testing error').addClass('success').text('✓ ' + saoAdmin.strings.success);
                } else {
                    $result.removeClass('testing success').addClass('error').text('✗ ' + (response.data.message || saoAdmin.strings.error));
                }
            })
            .fail(function() {
                $result.removeClass('testing success').addClass('error').text('✗ Connection failed');
            })
            .always(function() {
                $button.prop('disabled', false).text('Test Connection');
            });
        },

        /**
         * Set button loading state
         */
        setButtonLoading: function($button, loading) {
            if (loading) {
                $button.prop('disabled', true).addClass('sao-loading');
                var originalText = $button.text();
                $button.data('original-text', originalText).text('Loading...');
            } else {
                $button.prop('disabled', false).removeClass('sao-loading');
                var originalText = $button.data('original-text');
                if (originalText) {
                    $button.text(originalText);
                }
            }
        },

        /**
         * Show admin notice
         */
        showNotice: function(type, message) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        /**
         * Download file
         */
        downloadFile: function(filename, content) {
            var element = document.createElement('a');
            element.setAttribute('href', 'data:application/json;charset=utf-8,' + encodeURIComponent(content));
            element.setAttribute('download', filename);
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        SAOAdminInterface.init();

        // TABS SIMPLE QUI MARCHE PUTAIN !
        $('.nav-tab').click(function(e) {
            e.preventDefault();

            var target = $(this).attr('data-tab');

            // Enlever active de tous
            $('.nav-tab').removeClass('nav-tab-active');
            $('.sao-tab-content').hide();

            // Activer le bon
            $(this).addClass('nav-tab-active');
            document.getElementById(target).style.display = 'block';
        });

        // Afficher le premier onglet
        $('.sao-tab-content').hide();
        document.getElementById('general').style.display = 'block';
    });

})(jQuery);

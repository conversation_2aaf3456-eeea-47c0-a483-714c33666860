<!DOCTYPE html>
<html>
<head>
    <title>Test Onglets Final</title>
    <link rel="stylesheet" href="assets/css/admin-interface.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .test-container { background: white; padding: 20px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Final des Onglets</h1>
        
        <div class="sao-config-container">
            <div class="sao-config-tabs">
                <nav class="nav-tab-wrapper">
                    <a href="#general" class="nav-tab nav-tab-active" data-tab="general">Général</a>
                    <a href="#ai-apis" class="nav-tab" data-tab="ai-apis">AI APIs</a>
                    <a href="#advanced" class="nav-tab" data-tab="advanced">Avancé</a>
                    <a href="#import-export" class="nav-tab" data-tab="import-export">Import/Export</a>
                </nav>
            </div>

            <div id="general" class="sao-tab-content sao-tab-active">
                <h2>⚙️ Paramètres Généraux</h2>
                <p>Onglet Général - ça marche !</p>
            </div>

            <div id="ai-apis" class="sao-tab-content">
                <h2>🤖 Configuration des APIs IA</h2>
                <p>Onglet AI APIs - ça marche !</p>
            </div>

            <div id="advanced" class="sao-tab-content">
                <h2>🔧 Paramètres Avancés</h2>
                <p>Onglet Avancé - ça marche !</p>
            </div>

            <div id="import-export" class="sao-tab-content">
                <h2>📁 Import/Export</h2>
                <p>Onglet Import/Export - ça marche !</p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('🚀 Test des onglets démarré');
            
            // TABS SIMPLE QUI MARCHE PUTAIN !
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                console.log('Clic sur onglet');
                
                var target = $(this).attr('data-tab');
                console.log('Target:', target);
                
                // Enlever active de tous
                $('.nav-tab').removeClass('nav-tab-active');
                $('.sao-tab-content').hide();
                
                // Activer le bon
                $(this).addClass('nav-tab-active');
                document.getElementById(target).style.display = 'block';
                
                console.log('Onglet activé:', target);
            });
            
            // Afficher le premier onglet
            $('.sao-tab-content').hide();
            document.getElementById('general').style.display = 'block';
            
            console.log('✅ Onglets initialisés');
        });
    </script>
</body>
</html>

<?php
/**
 * Security Checker Class
 *
 * This class provides security validation and checks for the plugin
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * Security Checker Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Security_Checker {

	/**
	 * Check if all security measures are in place
	 *
	 * @since 1.0.0
	 * @return array Security check results
	 */
	public static function run_security_audit() {
		$results = array(
			'passed' => 0,
			'failed' => 0,
			'checks' => array(),
		);

		// Check ABSPATH protection
		$results['checks']['abspath'] = self::check_abspath_protection();
		if ( $results['checks']['abspath']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Check nonce implementation
		$results['checks']['nonce'] = self::check_nonce_implementation();
		if ( $results['checks']['nonce']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Check capability checks
		$results['checks']['capabilities'] = self::check_capability_implementation();
		if ( $results['checks']['capabilities']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Check input sanitization
		$results['checks']['sanitization'] = self::check_input_sanitization();
		if ( $results['checks']['sanitization']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Check output escaping
		$results['checks']['escaping'] = self::check_output_escaping();
		if ( $results['checks']['escaping']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Check SQL preparation
		$results['checks']['sql'] = self::check_sql_preparation();
		if ( $results['checks']['sql']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Check file permissions
		$results['checks']['file_permissions'] = self::check_file_permissions();
		if ( $results['checks']['file_permissions']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		return $results;
	}

	/**
	 * Check ABSPATH protection in files
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_abspath_protection() {
		$files_to_check = array(
			SEO_AUTO_OPTIMIZER_PLUGIN_FILE,
			SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php',
			SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-security-checker.php',
		);

		$protected_files = 0;
		$total_files = count( $files_to_check );

		foreach ( $files_to_check as $file ) {
			if ( file_exists( $file ) ) {
				$content = file_get_contents( $file );
				if ( strpos( $content, "if ( ! defined( 'ABSPATH' ) )" ) !== false ) {
					$protected_files++;
				}
			}
		}

		return array(
			'status' => $protected_files === $total_files,
			'message' => sprintf(
				'ABSPATH protection: %d/%d files protected',
				$protected_files,
				$total_files
			),
		);
	}

	/**
	 * Check nonce implementation
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_nonce_implementation() {
		$main_file = SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
		
		if ( ! file_exists( $main_file ) ) {
			return array(
				'status' => false,
				'message' => 'Main class file not found',
			);
		}

		$content = file_get_contents( $main_file );
		$has_nonce_creation = strpos( $content, 'wp_create_nonce' ) !== false;
		$has_nonce_verification = strpos( $content, 'wp_verify_nonce' ) !== false;

		return array(
			'status' => $has_nonce_creation && $has_nonce_verification,
			'message' => sprintf(
				'Nonce implementation: Creation=%s, Verification=%s',
				$has_nonce_creation ? 'Yes' : 'No',
				$has_nonce_verification ? 'Yes' : 'No'
			),
		);
	}

	/**
	 * Check capability implementation
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_capability_implementation() {
		$main_file = SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
		
		if ( ! file_exists( $main_file ) ) {
			return array(
				'status' => false,
				'message' => 'Main class file not found',
			);
		}

		$content = file_get_contents( $main_file );
		$capability_checks = array(
			'current_user_can',
			'manage_options',
			'edit_posts',
		);

		$found_checks = 0;
		foreach ( $capability_checks as $check ) {
			if ( strpos( $content, $check ) !== false ) {
				$found_checks++;
			}
		}

		return array(
			'status' => $found_checks >= 2,
			'message' => sprintf(
				'Capability checks: %d/%d checks found',
				$found_checks,
				count( $capability_checks )
			),
		);
	}

	/**
	 * Check input sanitization
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_input_sanitization() {
		$main_file = SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
		
		if ( ! file_exists( $main_file ) ) {
			return array(
				'status' => false,
				'message' => 'Main class file not found',
			);
		}

		$content = file_get_contents( $main_file );
		$sanitization_functions = array(
			'sanitize_text_field',
			'wp_kses_post',
			'absint',
		);

		$found_functions = 0;
		foreach ( $sanitization_functions as $function ) {
			if ( strpos( $content, $function ) !== false ) {
				$found_functions++;
			}
		}

		return array(
			'status' => $found_functions >= 2,
			'message' => sprintf(
				'Input sanitization: %d/%d functions used',
				$found_functions,
				count( $sanitization_functions )
			),
		);
	}

	/**
	 * Check output escaping
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_output_escaping() {
		$main_file = SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
		
		if ( ! file_exists( $main_file ) ) {
			return array(
				'status' => false,
				'message' => 'Main class file not found',
			);
		}

		$content = file_get_contents( $main_file );
		$escaping_functions = array(
			'esc_html',
			'esc_attr',
			'esc_url',
		);

		$found_functions = 0;
		foreach ( $escaping_functions as $function ) {
			if ( strpos( $content, $function ) !== false ) {
				$found_functions++;
			}
		}

		return array(
			'status' => $found_functions >= 2,
			'message' => sprintf(
				'Output escaping: %d/%d functions used',
				$found_functions,
				count( $escaping_functions )
			),
		);
	}

	/**
	 * Check SQL preparation
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_sql_preparation() {
		$main_file = SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
		
		if ( ! file_exists( $main_file ) ) {
			return array(
				'status' => false,
				'message' => 'Main class file not found',
			);
		}

		$content = file_get_contents( $main_file );
		$has_prepare = strpos( $content, '$wpdb->prepare' ) !== false;

		return array(
			'status' => $has_prepare,
			'message' => sprintf(
				'SQL preparation: %s',
				$has_prepare ? 'Used' : 'Not found'
			),
		);
	}

	/**
	 * Check file permissions
	 *
	 * @since 1.0.0
	 * @return array Check result
	 */
	private static function check_file_permissions() {
		$directories = array(
			SEO_AUTO_OPTIMIZER_INCLUDES_DIR,
			SEO_AUTO_OPTIMIZER_PLUGIN_DIR . 'assets/',
			SEO_AUTO_OPTIMIZER_TEMPLATES_DIR,
			SEO_AUTO_OPTIMIZER_LANGUAGES_DIR,
		);

		$protected_dirs = 0;
		$total_dirs = count( $directories );

		foreach ( $directories as $dir ) {
			$index_file = $dir . 'index.php';
			if ( file_exists( $index_file ) ) {
				$protected_dirs++;
			}
		}

		return array(
			'status' => $protected_dirs === $total_dirs,
			'message' => sprintf(
				'Directory protection: %d/%d directories protected',
				$protected_dirs,
				$total_dirs
			),
		);
	}

	/**
	 * Generate security report
	 *
	 * @since 1.0.0
	 * @return string HTML security report
	 */
	public static function generate_security_report() {
		$audit_results = self::run_security_audit();
		
		$html = '<div class="seo-security-report">';
		$html .= '<h3>Security Audit Report</h3>';
		$html .= '<p><strong>Passed:</strong> ' . $audit_results['passed'] . ' | ';
		$html .= '<strong>Failed:</strong> ' . $audit_results['failed'] . '</p>';
		
		$html .= '<ul>';
		foreach ( $audit_results['checks'] as $check_name => $check_result ) {
			$status_icon = $check_result['status'] ? '✅' : '❌';
			$html .= '<li>' . $status_icon . ' ' . $check_result['message'] . '</li>';
		}
		$html .= '</ul>';
		
		$html .= '</div>';
		
		return $html;
	}
}

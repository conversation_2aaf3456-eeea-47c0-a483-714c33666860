/**
 * SEO Injection Admin JavaScript
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

(function($) {
	'use strict';

	/**
	 * SEO Injection Admin Class
	 */
	var SEOInjectionAdmin = {
		/**
		 * Initialize the admin interface
		 */
		init: function() {
			this.bindEvents();
			this.initCharacterCounters();
			this.initTooltips();
		},

		/**
		 * Bind event handlers
		 */
		bindEvents: function() {
			// Meta box injection
			$('#sao-inject-now').on('click', this.handleQuickInjection.bind(this));
			$('#sao-generate-keywords').on('click', this.handleGenerateKeywords.bind(this));
			$('#sao-preview-injection').on('click', this.handlePreviewInjection.bind(this));
			$('.sao-restore-backup').on('click', this.handleRestoreBackup.bind(this));

			// Bulk injection
			$('#sao-bulk-injection-form').on('submit', this.handleBulkInjection.bind(this));

			// Character counters
			$('#sao-meta-description').on('input', this.updateCharacterCounter.bind(this));

			// Form validation
			$('#sao-focus-keyword').on('blur', this.validateKeyword.bind(this));
		},

		/**
		 * Initialize character counters
		 */
		initCharacterCounters: function() {
			$('#sao-meta-description').trigger('input');
		},

		/**
		 * Initialize tooltips
		 */
		initTooltips: function() {
			// Add tooltips for form fields if needed
			$('[data-tooltip]').each(function() {
				$(this).attr('title', $(this).data('tooltip'));
			});
		},

		/**
		 * Handle quick injection from meta box
		 */
		handleQuickInjection: function(e) {
			e.preventDefault();

			if (!this.validateForm()) {
				return;
			}

			if (!confirm(saoInjection.strings.confirm_inject)) {
				return;
			}

			var data = this.getFormData();
			this.performInjection(data);
		},

		/**
		 * Handle keyword generation with AI
		 */
		handleGenerateKeywords: function(e) {
			e.preventDefault();

			var $button = $(e.target);
			var originalText = $button.text();

			$button.text(saoInjection.strings.loading).prop('disabled', true);

			$.ajax({
				url: saoInjection.ajaxUrl,
				type: 'POST',
				data: {
					action: 'sao_generate_keywords',
					nonce: saoInjection.nonces.generate,
					post_id: saoInjection.postId
				},
				success: function(response) {
					if (response.success) {
						SEOInjectionAdmin.populateGeneratedKeywords(response.data);
						SEOInjectionAdmin.showNotice('success', saoInjection.strings.success);
					} else {
						SEOInjectionAdmin.showNotice('error', response.data || saoInjection.strings.error);
					}
				},
				error: function() {
					SEOInjectionAdmin.showNotice('error', saoInjection.strings.error);
				},
				complete: function() {
					$button.text(originalText).prop('disabled', false);
				}
			});
		},

		/**
		 * Handle injection preview
		 */
		handlePreviewInjection: function(e) {
			e.preventDefault();

			if (!this.validateForm()) {
				return;
			}

			var data = this.getFormData();

			$.ajax({
				url: saoInjection.ajaxUrl,
				type: 'POST',
				data: {
					action: 'sao_preview_injection',
					nonce: saoInjection.nonces.preview,
					post_id: saoInjection.postId,
					plugin_name: data.target_plugin,
					keywords: data.keywords
				},
				success: function(response) {
					if (response.success) {
						SEOInjectionAdmin.showPreviewModal(response.data);
					} else {
						SEOInjectionAdmin.showNotice('error', response.data || saoInjection.strings.error);
					}
				},
				error: function() {
					SEOInjectionAdmin.showNotice('error', saoInjection.strings.error);
				}
			});
		},

		/**
		 * Handle backup restoration
		 */
		handleRestoreBackup: function(e) {
			e.preventDefault();

			if (!confirm(saoInjection.strings.confirm_restore)) {
				return;
			}

			var $button = $(e.target);
			var pluginName = $button.data('plugin');

			$.ajax({
				url: saoInjection.ajaxUrl,
				type: 'POST',
				data: {
					action: 'sao_restore_backup',
					nonce: saoInjection.nonces.restore,
					post_id: saoInjection.postId,
					plugin_name: pluginName
				},
				success: function(response) {
					if (response.success) {
						SEOInjectionAdmin.showNotice('success', saoInjection.strings.success);
						SEOInjectionAdmin.refreshCurrentSEOData();
					} else {
						SEOInjectionAdmin.showNotice('error', response.data || saoInjection.strings.error);
					}
				},
				error: function() {
					SEOInjectionAdmin.showNotice('error', saoInjection.strings.error);
				}
			});
		},

		/**
		 * Handle bulk injection
		 */
		handleBulkInjection: function(e) {
			e.preventDefault();

			if (!confirm(saoInjection.strings.confirm_bulk)) {
				return;
			}

			var $form = $(e.target);
			var formData = this.serializeFormData($form);

			$('#bulk-injection-progress').show().addClass('sao-fade-in');
			$form.addClass('sao-loading');

			this.performBulkInjection(formData);
		},

		/**
		 * Perform the actual injection
		 */
		performInjection: function(data) {
			$('#sao-inject-now').prop('disabled', true).text(saoInjection.strings.loading);

			$.ajax({
				url: saoInjection.ajaxUrl,
				type: 'POST',
				data: {
					action: 'sao_inject_keywords',
					nonce: saoInjection.nonces.inject,
					post_id: saoInjection.postId,
					plugin_name: data.target_plugin,
					keywords: data.keywords,
					options: data.options
				},
				success: function(response) {
					if (response.success) {
						SEOInjectionAdmin.showNotice('success', saoInjection.strings.success);
						SEOInjectionAdmin.refreshCurrentSEOData();
						SEOInjectionAdmin.clearForm();
					} else {
						SEOInjectionAdmin.showNotice('error', response.data || saoInjection.strings.error);
					}
				},
				error: function() {
					SEOInjectionAdmin.showNotice('error', saoInjection.strings.error);
				},
				complete: function() {
					$('#sao-inject-now').prop('disabled', false).text('Inject Now');
				}
			});
		},

		/**
		 * Perform bulk injection
		 */
		performBulkInjection: function(formData) {
			$.ajax({
				url: saoInjection.ajaxUrl,
				type: 'POST',
				data: {
					action: 'sao_bulk_inject',
					nonce: saoInjection.nonces.bulk_inject,
					post_type: formData.post_type,
					target_plugin: formData.target_plugin,
					limit: formData.limit,
					options: formData.options
				},
				success: function(response) {
					if (response.success) {
						SEOInjectionAdmin.updateBulkProgress(100);
						SEOInjectionAdmin.showBulkResults(response.data);
					} else {
						SEOInjectionAdmin.showNotice('error', response.data || saoInjection.strings.error);
					}
				},
				error: function() {
					SEOInjectionAdmin.showNotice('error', saoInjection.strings.error);
				},
				complete: function() {
					$('#sao-bulk-injection-form').removeClass('sao-loading');
				}
			});
		},

		/**
		 * Validate form data
		 */
		validateForm: function() {
			var focusKeyword = $('#sao-focus-keyword').val().trim();
			var targetPlugin = $('#sao-target-plugin').val();

			if (!focusKeyword) {
				this.showNotice('error', 'Focus keyword is required.');
				$('#sao-focus-keyword').focus();
				return false;
			}

			if (!targetPlugin) {
				this.showNotice('error', 'Please select a target plugin.');
				$('#sao-target-plugin').focus();
				return false;
			}

			return true;
		},

		/**
		 * Validate keyword format
		 */
		validateKeyword: function(e) {
			var keyword = $(e.target).val().trim();
			var $field = $(e.target);

			// Remove previous validation classes
			$field.removeClass('sao-field-error sao-field-valid');

			if (keyword) {
				// Basic keyword validation
				if (keyword.length < 2) {
					$field.addClass('sao-field-error');
					this.showFieldError($field, 'Keyword must be at least 2 characters long.');
				} else if (keyword.length > 100) {
					$field.addClass('sao-field-error');
					this.showFieldError($field, 'Keyword must be less than 100 characters.');
				} else {
					$field.addClass('sao-field-valid');
					this.hideFieldError($field);
				}
			}
		},

		/**
		 * Get form data
		 */
		getFormData: function() {
			var additionalKeywords = $('#sao-additional-keywords').val().trim();
			var keywordsArray = additionalKeywords ? additionalKeywords.split(',').map(function(k) {
				return k.trim();
			}).filter(function(k) {
				return k.length > 0;
			}) : [];

			return {
				target_plugin: $('#sao-target-plugin').val(),
				keywords: {
					focus_keyword: $('#sao-focus-keyword').val().trim(),
					additional_keywords: keywordsArray,
					meta_description: $('#sao-meta-description').val().trim()
				},
				options: {
					preserve_existing: $('#sao-preserve-existing').is(':checked'),
					create_backup: $('#sao-create-backup').is(':checked'),
					auto_meta_desc: true
				}
			};
		},

		/**
		 * Serialize form data
		 */
		serializeFormData: function($form) {
			var data = {};
			var formArray = $form.serializeArray();

			$.each(formArray, function(i, field) {
				if (field.name.indexOf('[]') !== -1) {
					var name = field.name.replace('[]', '');
					if (!data[name]) {
						data[name] = [];
					}
					data[name].push(field.value);
				} else {
					data[field.name] = field.value;
				}
			});

			// Handle checkboxes
			data.options = {};
			$form.find('input[type="checkbox"]:checked').each(function() {
				data.options[$(this).attr('name')] = true;
			});

			return data;
		},

		/**
		 * Update character counter
		 */
		updateCharacterCounter: function(e) {
			var $field = $(e.target);
			var length = $field.val().length;
			var maxLength = 160;
			var $counter = $field.siblings('.sao-char-counter');

			$counter.text(length + '/' + maxLength);

			if (length > maxLength) {
				$counter.addClass('sao-over-limit');
				$field.addClass('sao-field-warning');
			} else {
				$counter.removeClass('sao-over-limit');
				$field.removeClass('sao-field-warning');
			}
		},

		/**
		 * Populate generated keywords
		 */
		populateGeneratedKeywords: function(data) {
			if (data.focus_keyword) {
				$('#sao-focus-keyword').val(data.focus_keyword);
			}

			if (data.additional_keywords && data.additional_keywords.length > 0) {
				$('#sao-additional-keywords').val(data.additional_keywords.join(', '));
			}

			if (data.meta_description) {
				$('#sao-meta-description').val(data.meta_description).trigger('input');
			}
		},

		/**
		 * Show preview modal
		 */
		showPreviewModal: function(data) {
			var modal = $('<div class="sao-modal-overlay">');
			var modalContent = $('<div class="sao-modal-content">');
			var modalHeader = $('<div class="sao-modal-header"><h3>Injection Preview</h3><button class="sao-modal-close">&times;</button></div>');
			var modalBody = $('<div class="sao-modal-body">');

			// Build preview content
			var previewHtml = '<div class="sao-preview-comparison">';

			$.each(data.changes, function(field, change) {
				previewHtml += '<div class="sao-preview-field">';
				previewHtml += '<h4>' + field.replace('_', ' ').toUpperCase() + '</h4>';
				previewHtml += '<div class="sao-preview-before"><strong>Before:</strong> ' + (change.old || '<em>Empty</em>') + '</div>';
				previewHtml += '<div class="sao-preview-after"><strong>After:</strong> ' + change.new + '</div>';
				previewHtml += '</div>';
			});

			previewHtml += '</div>';

			if (Object.keys(data.changes).length === 0) {
				previewHtml = '<p class="sao-no-changes">No changes will be made with current settings.</p>';
			}

			modalBody.html(previewHtml);
			modalContent.append(modalHeader, modalBody);
			modal.append(modalContent);

			// Add to page
			$('body').append(modal);
			modal.addClass('sao-fade-in');

			// Bind close events
			modal.on('click', '.sao-modal-close, .sao-modal-overlay', function(e) {
				if (e.target === this) {
					modal.remove();
				}
			});
		},

		/**
		 * Update bulk progress
		 */
		updateBulkProgress: function(percentage) {
			$('.sao-progress-fill').css('width', percentage + '%');
			$('.sao-progress-text').text('Processing... ' + percentage + '%');
		},

		/**
		 * Show bulk results
		 */
		showBulkResults: function(data) {
			var resultsHtml = '<div class="sao-bulk-results">';
			resultsHtml += '<h4>Bulk Injection Complete</h4>';
			resultsHtml += '<p>Processed: ' + data.processed + ' posts</p>';
			resultsHtml += '<p>Errors: ' + data.errors + ' posts</p>';
			resultsHtml += '<p>Total: ' + data.total + ' posts</p>';
			resultsHtml += '</div>';

			$('#bulk-injection-progress').html(resultsHtml).addClass('sao-slide-up');
		},

		/**
		 * Refresh current SEO data
		 */
		refreshCurrentSEOData: function() {
			// Reload the current SEO data section
			location.reload();
		},

		/**
		 * Clear form
		 */
		clearForm: function() {
			$('#sao-focus-keyword, #sao-additional-keywords, #sao-meta-description').val('');
			$('.sao-char-counter').text('0/160').removeClass('sao-over-limit');
		},

		/**
		 * Show notice
		 */
		showNotice: function(type, message) {
			var notice = $('<div class="sao-notice sao-notice-' + type + '">' + message + '</div>');
			$('.sao-meta-box, .sao-injection-dashboard').first().prepend(notice);
			notice.addClass('sao-fade-in');

			// Auto-hide after 5 seconds
			setTimeout(function() {
				notice.fadeOut(function() {
					notice.remove();
				});
			}, 5000);
		},

		/**
		 * Show field error
		 */
		showFieldError: function($field, message) {
			this.hideFieldError($field);
			var error = $('<div class="sao-field-error-message">' + message + '</div>');
			$field.after(error);
		},

		/**
		 * Hide field error
		 */
		hideFieldError: function($field) {
			$field.siblings('.sao-field-error-message').remove();
		}
	};

	// Initialize when document is ready
	$(document).ready(function() {
		SEOInjectionAdmin.init();
	});

	// Make available globally
	window.SEOInjectionAdmin = SEOInjectionAdmin;

})(jQuery);

/**
 * Additional CSS for modal and dynamic elements
 */
jQuery(document).ready(function($) {
	// Add dynamic styles
	var dynamicStyles = `
		.sao-modal-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.7);
			z-index: 100000;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.sao-modal-content {
			background: #fff;
			border-radius: 4px;
			max-width: 600px;
			width: 90%;
			max-height: 80vh;
			overflow-y: auto;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
		}

		.sao-modal-header {
			padding: 20px;
			border-bottom: 1px solid #eee;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.sao-modal-header h3 {
			margin: 0;
			font-size: 18px;
		}

		.sao-modal-close {
			background: none;
			border: none;
			font-size: 24px;
			cursor: pointer;
			padding: 0;
			width: 30px;
			height: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.sao-modal-body {
			padding: 20px;
		}

		.sao-preview-field {
			margin-bottom: 20px;
			padding: 15px;
			border: 1px solid #eee;
			border-radius: 4px;
		}

		.sao-preview-field h4 {
			margin: 0 0 10px 0;
			font-size: 14px;
			color: #007cba;
		}

		.sao-preview-before,
		.sao-preview-after {
			margin-bottom: 8px;
			font-size: 13px;
		}

		.sao-preview-before {
			color: #666;
		}

		.sao-preview-after {
			color: #23282d;
			font-weight: 500;
		}

		.sao-no-changes {
			text-align: center;
			color: #666;
			font-style: italic;
			padding: 40px 20px;
		}

		.sao-field-error {
			border-color: #d63638 !important;
			box-shadow: 0 0 0 1px #d63638 !important;
		}

		.sao-field-valid {
			border-color: #00a32a !important;
			box-shadow: 0 0 0 1px #00a32a !important;
		}

		.sao-field-warning {
			border-color: #dba617 !important;
			box-shadow: 0 0 0 1px #dba617 !important;
		}

		.sao-field-error-message {
			color: #d63638;
			font-size: 12px;
			margin-top: 5px;
			display: block;
		}

		.sao-bulk-results {
			text-align: center;
			padding: 20px;
		}

		.sao-bulk-results h4 {
			color: #00a32a;
			margin-bottom: 15px;
		}

		.sao-bulk-results p {
			margin: 5px 0;
			font-size: 14px;
		}
	`;

	$('<style>').text(dynamicStyles).appendTo('head');
});
<?php
/**
 * Diagnostic Complet SEO Auto Optimizer
 */

// WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/../../../');
    require_once ABSPATH . 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
}

echo "<h1>🔍 DIAGNOSTIC COMPLET SEO AUTO OPTIMIZER</h1>\n\n";

// 1. VÉRIFICATION PLUGIN PRINCIPAL
echo "<h2>1. 🔌 Plugin Principal</h2>\n";
if (class_exists('SEO_Auto_Optimizer')) {
    echo "✅ Plugin SEO Auto Optimizer chargé\n";
} else {
    echo "❌ Plugin SEO Auto Optimizer NON chargé\n";
    echo "💡 Vérifiez que le plugin est activé\n";
}

// 2. VÉRIFICATION YOAST
echo "\n<h2>2. 🎯 Yoast SEO</h2>\n";

// Installation
$yoast_file = WP_PLUGIN_DIR . '/wordpress-seo/wp-seo.php';
echo "📁 Fichier Yoast: " . ($yoast_file) . "\n";
if (file_exists($yoast_file)) {
    echo "✅ Yoast installé\n";
} else {
    echo "❌ Yoast NON installé\n";
    echo "💡 Installez Yoast SEO depuis le répertoire WordPress\n";
    goto ai_test; // Skip to AI test
}

// Activation
if (!function_exists('is_plugin_active')) {
    require_once ABSPATH . 'wp-admin/includes/plugin.php';
}

if (is_plugin_active('wordpress-seo/wp-seo.php')) {
    echo "✅ Yoast activé\n";
} else {
    echo "❌ Yoast NON activé\n";
    echo "💡 Activez Yoast dans Plugins > Plugins installés\n";
    goto ai_test;
}

// Constantes
if (defined('WPSEO_VERSION')) {
    echo "✅ Version Yoast: " . WPSEO_VERSION . "\n";
} else {
    echo "❌ Version Yoast non détectée\n";
}

// Classes
$yoast_classes = ['WPSEO', 'WPSEO_Options', 'WPSEO_Meta'];
foreach ($yoast_classes as $class) {
    if (class_exists($class)) {
        echo "✅ Classe {$class}\n";
    } else {
        echo "❌ Classe {$class} manquante\n";
    }
}

// Notre détecteur
if (class_exists('SEO_Auto_Optimizer_Plugin_Detector')) {
    echo "\n🔍 Test de notre détecteur:\n";
    $detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
    
    // Test direct
    $is_yoast_active = $detector->is_plugin_active('yoast');
    echo "  is_plugin_active('yoast'): " . ($is_yoast_active ? 'TRUE' : 'FALSE') . "\n";
    
    // Test complet
    $active_plugins = $detector->get_active_seo_plugins();
    if (isset($active_plugins['yoast'])) {
        echo "✅ Yoast détecté par notre système\n";
        echo "  Nom: " . $active_plugins['yoast']['name'] . "\n";
        echo "  Version: " . $active_plugins['yoast']['version'] . "\n";
    } else {
        echo "❌ Yoast NON détecté par notre système\n";
        echo "🔧 Plugins détectés: " . count($active_plugins) . "\n";
        foreach ($active_plugins as $key => $plugin) {
            echo "  - {$key}: {$plugin['name']}\n";
        }
    }
}

// 3. TEST AI
ai_test:
echo "\n<h2>3. 🤖 Intelligence Artificielle</h2>\n";

// Options AI
$ai_options = get_option('sao_ai_options', array());
if (empty($ai_options)) {
    echo "❌ Aucune configuration AI\n";
    echo "💡 Configurez vos clés API dans SEO Optimizer > Configuration\n";
} else {
    echo "✅ Configuration AI trouvée\n";
    
    // Vérification des clés
    $providers = ['openai_api_key', 'gemini_api_key', 'anthropic_api_key'];
    $configured_providers = 0;
    
    foreach ($providers as $provider) {
        if (!empty($ai_options[$provider])) {
            $configured_providers++;
            $masked = substr($ai_options[$provider], 0, 4) . '***' . substr($ai_options[$provider], -4);
            echo "✅ {$provider}: {$masked}\n";
        } else {
            echo "⚪ {$provider}: non configuré\n";
        }
    }
    
    if ($configured_providers === 0) {
        echo "❌ Aucune clé API configurée\n";
    } else {
        echo "✅ {$configured_providers} provider(s) configuré(s)\n";
        
        // Test de génération
        if (class_exists('SEO_Auto_Optimizer_AI_Keyword_Generator')) {
            echo "\n🔄 Test génération mots-clés...\n";
            try {
                $generator = SEO_Auto_Optimizer_AI_Keyword_Generator::get_instance();
                $result = $generator->generate_keywords(
                    'WordPress SEO',
                    'Guide complet pour optimiser le référencement de votre site WordPress.'
                );
                
                if ($result['success']) {
                    echo "✅ Génération réussie!\n";
                    echo "🎯 Mots-clés: " . implode(', ', array_slice($result['keywords'], 0, 3)) . "...\n";
                } else {
                    echo "❌ Génération échouée: " . $result['error'] . "\n";
                }
            } catch (Exception $e) {
                echo "❌ Erreur: " . $e->getMessage() . "\n";
            }
        }
    }
}

// 4. TEST INJECTION
echo "\n<h2>4. 💉 Injection SEO</h2>\n";

if (class_exists('SEO_Auto_Optimizer_SEO_Data_Injector')) {
    echo "✅ Classe Injector existe\n";
    
    // Test avec un post
    $posts = get_posts(['numberposts' => 1, 'post_status' => 'publish']);
    if (!empty($posts)) {
        $post_id = $posts[0]->ID;
        echo "📝 Test sur post: {$posts[0]->post_title} (ID: {$post_id})\n";
        
        // Vérifier méta fields Yoast existants
        $yoast_metas = [
            '_yoast_wpseo_focuskw' => get_post_meta($post_id, '_yoast_wpseo_focuskw', true),
            '_yoast_wpseo_metadesc' => get_post_meta($post_id, '_yoast_wpseo_metadesc', true)
        ];
        
        foreach ($yoast_metas as $key => $value) {
            $status = !empty($value) ? "'{$value}'" : '(vide)';
            echo "  {$key}: {$status}\n";
        }
        
    } else {
        echo "❌ Aucun post pour tester\n";
    }
} else {
    echo "❌ Classe Injector manquante\n";
}

// 5. RÉSUMÉ ET SOLUTIONS
echo "\n<h2>5. 🎯 RÉSUMÉ ET SOLUTIONS</h2>\n";

$problems = [];
$solutions = [];

// Vérifications
if (!file_exists($yoast_file)) {
    $problems[] = "Yoast SEO n'est pas installé";
    $solutions[] = "Installer Yoast SEO depuis Plugins > Ajouter";
}

if (!is_plugin_active('wordpress-seo/wp-seo.php')) {
    $problems[] = "Yoast SEO n'est pas activé";
    $solutions[] = "Activer Yoast SEO dans Plugins > Plugins installés";
}

if (empty($ai_options) || $configured_providers === 0) {
    $problems[] = "Aucune clé API configurée";
    $solutions[] = "Configurer au moins OpenAI ou Gemini dans SEO Optimizer > Configuration";
}

if (empty($problems)) {
    echo "🎉 TOUT FONCTIONNE CORRECTEMENT!\n";
    echo "✅ Yoast détecté\n";
    echo "✅ AI configurée\n";
    echo "✅ Plugin prêt à utiliser\n";
} else {
    echo "⚠️ PROBLÈMES DÉTECTÉS:\n";
    foreach ($problems as $i => $problem) {
        echo ($i + 1) . ". {$problem}\n";
    }
    
    echo "\n💡 SOLUTIONS:\n";
    foreach ($solutions as $i => $solution) {
        echo ($i + 1) . ". {$solution}\n";
    }
}

echo "\n📞 ÉTAPES SUIVANTES:\n";
echo "1. Corrigez les problèmes ci-dessus\n";
echo "2. Allez dans SEO Optimizer > Configuration\n";
echo "3. Testez la génération avec Boss Optimization\n";
echo "4. Vérifiez l'injection dans vos posts\n";
?>

# SEO Plugin Detection System

## Overview

The SEO Plugin Detection System is a comprehensive module that automatically detects and analyzes active SEO plugins on WordPress sites. This system provides compatibility information and helps optimize the interaction between our plugin and existing SEO solutions.

## Features

### 🔍 **Automatic Detection**
- Detects 4 major SEO plugins: Rank Math, Yoast SEO, SEOPress, All in One SEO
- Multiple verification methods for accurate detection
- Real-time plugin status monitoring

### 🚀 **Performance Optimized**
- WordPress transient caching (1-hour expiration)
- Automatic cache invalidation on plugin activation/deactivation
- Force refresh capability for immediate updates

### 🛡️ **Security First**
- Capability checks (`manage_options`)
- Input sanitization and output escaping
- AJAX nonce verification
- Error handling for corrupted plugins

### 📊 **Admin Interface**
- Visual plugin detection dashboard
- Primary plugin identification
- Compatibility status display
- One-click refresh functionality

## Detected Plugins

### Rank Math SEO
- **File**: `seo-by-rank-math/rank-math.php`
- **Class**: `RankMath`
- **Function**: `rank_math()`
- **Constant**: `RANK_MATH_VERSION`

### Yoast SEO
- **File**: `wordpress-seo/wp-seo.php`
- **Class**: `WPSEO`
- **Function**: `wpseo_init()`
- **Constant**: `WPSEO_VERSION`

### SEOPress
- **File**: `wp-seopress/seopress.php`
- **Class**: `SEOPress`
- **Function**: `seopress_init()`
- **Constant**: `SEOPRESS_VERSION`

### All in One SEO
- **File**: `all-in-one-seo-pack/all_in_one_seo_pack.php`
- **Class**: `All_in_One_SEO_Pack`
- **Function**: `aioseo()`
- **Constant**: `AIOSEO_VERSION`

## API Reference

### Main Class: `SEO_Auto_Optimizer_Plugin_Detector`

#### Public Methods

```php
// Get singleton instance
$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

// Get all active SEO plugins
$active_plugins = $detector->get_active_seo_plugins();
$active_plugins = $detector->get_active_seo_plugins(true); // Force refresh

// Get primary SEO plugin
$primary_plugin = $detector->get_primary_seo_plugin();

// Check specific plugin
$is_active = $detector->is_plugin_active('rank_math');

// Clear cache
$detector->clear_cache();

// Render admin interface
$html = $detector->render_admin_interface();

// Get statistics
$stats = $detector->get_plugin_statistics();
```

#### Return Data Structure

**Active Plugins Array:**
```php
array(
    'rank_math' => array(
        'key'         => 'rank_math',
        'name'        => 'Rank Math SEO',
        'version'     => '1.0.0',
        'author'      => 'Rank Math',
        'author_uri'  => 'https://rankmath.com/',
        'plugin_uri'  => 'https://rankmath.com/',
        'description' => 'Advanced SEO plugin with AI-powered features',
        'website'     => 'https://rankmath.com/',
        'detected_at' => 1234567890,
        'file'        => 'seo-by-rank-math/rank-math.php',
        'class'       => 'RankMath',
        'function'    => 'rank_math',
        'constant'    => 'RANK_MATH_VERSION'
    )
)
```

**Plugin Statistics:**
```php
array(
    'total_detected'    => 2,
    'primary_plugin'    => 'rank_math',
    'has_conflicts'     => true,
    'last_checked'      => 1234567890,
    'cache_expires_in'  => 3600
)
```

## Integration

### WordPress Hooks

The detector automatically integrates with WordPress through these hooks:

```php
// Clear cache on plugin changes
add_action('activated_plugin', array($detector, 'clear_cache'));
add_action('deactivated_plugin', array($detector, 'clear_cache'));

// AJAX handler
add_action('wp_ajax_seo_auto_optimizer_refresh_plugins', array($detector, 'ajax_refresh_plugins'));
```

### Admin Menu Integration

The detector adds a new admin page:
- **Menu**: SEO Optimizer → SEO Plugins
- **Capability**: `manage_options`
- **Page**: `seo-auto-optimizer-plugins`

## Usage Examples

### Basic Detection
```php
$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
$active_plugins = $detector->get_active_seo_plugins();

if (!empty($active_plugins)) {
    echo "Found " . count($active_plugins) . " SEO plugins";
    foreach ($active_plugins as $plugin) {
        echo "- " . $plugin['name'] . " v" . $plugin['version'];
    }
}
```

### Primary Plugin Check
```php
$primary = $detector->get_primary_seo_plugin();
if ($primary) {
    echo "Primary SEO plugin: " . $primary['name'];
} else {
    echo "No SEO plugins detected";
}
```

### Specific Plugin Check
```php
if ($detector->is_plugin_active('yoast')) {
    echo "Yoast SEO is active";
    // Adjust our plugin behavior accordingly
}
```

### Cache Management
```php
// Force refresh detection
$detector->clear_cache();
$fresh_data = $detector->get_active_seo_plugins(true);

// Check cache status
$stats = $detector->get_plugin_statistics();
echo "Cache expires in: " . $stats['cache_expires_in'] . " seconds";
```

## Admin Interface

### Dashboard Features
- **Plugin Cards**: Visual representation of detected plugins
- **Primary Plugin Badge**: Highlights the main SEO plugin
- **Compatibility Grid**: Shows all supported plugins and their status
- **Refresh Button**: Manual cache refresh with AJAX
- **Statistics**: Plugin counts and conflict detection

### CSS Classes
- `.seo-plugin-detector-admin` - Main container
- `.seo-plugin-card` - Individual plugin card
- `.seo-plugin-card.primary` - Primary plugin styling
- `.status-badge.active` - Active plugin badge
- `.compatibility-badge.compatible` - Compatibility indicator

## Security Measures

### Input Validation
- All user inputs sanitized with WordPress functions
- Plugin keys validated against known list
- AJAX requests protected with nonces

### Output Escaping
- All HTML output properly escaped
- XSS prevention in admin interface
- Safe URL handling

### Capability Checks
```php
// All methods check user capabilities
if (!current_user_can('manage_options')) {
    return array(); // or appropriate response
}
```

### Error Handling
```php
try {
    $result = $detector->is_plugin_active($plugin_key);
} catch (Exception $e) {
    error_log('SEO Auto Optimizer: ' . $e->getMessage());
    return false;
}
```

## Testing

### Automated Tests
Run the test suite to verify functionality:
```bash
php test-seo-plugin-detection.php
```

### Test Coverage
- ✅ Singleton pattern verification
- ✅ Method existence checks
- ✅ Return type validation
- ✅ Cache functionality
- ✅ Security measures
- ✅ Admin interface rendering
- ✅ Error handling

### Manual Testing
1. Install/activate different SEO plugins
2. Check detection accuracy in admin interface
3. Verify cache behavior
4. Test refresh functionality
5. Validate security measures

## Troubleshooting

### Common Issues

**No plugins detected:**
- Check if plugins are actually active
- Verify file paths in plugin configuration
- Clear cache and refresh

**Cache not working:**
- Check WordPress transient functionality
- Verify cache key uniqueness
- Test cache expiration

**Admin interface not loading:**
- Check user capabilities
- Verify AJAX endpoints
- Review JavaScript console for errors

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Compatibility

### WordPress Requirements
- WordPress 5.0+
- PHP 7.4+
- Admin access (`manage_options` capability)

### Plugin Compatibility
- ✅ Rank Math SEO (all versions)
- ✅ Yoast SEO (all versions)
- ✅ SEOPress (all versions)
- ✅ All in One SEO (all versions)
- ✅ Works alongside multiple SEO plugins

## Future Enhancements

### Planned Features
- Additional SEO plugin support
- Conflict resolution suggestions
- Performance impact analysis
- Integration recommendations
- Automated compatibility testing

### Extension Points
The system is designed for easy extension:
```php
// Add new plugin detection
$known_plugins['new_plugin'] = array(
    'name' => 'New SEO Plugin',
    'file' => 'new-seo/plugin.php',
    // ... configuration
);
```

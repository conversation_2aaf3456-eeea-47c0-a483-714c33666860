<?php
/**
 * Boss Optimization Interface Class
 *
 * This class handles the mass optimization interface for all content types
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * Boss Optimization Interface Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Boss_Optimization_Interface {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Boss_Optimization_Interface|null
	 */
	private static $instance = null;

	/**
	 * Rate limiting cache key prefix
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $rate_limit_key = 'sao_boss_rate_limit_';

	/**
	 * Maximum simultaneous optimizations
	 *
	 * @since 1.0.0
	 * @var int
	 */
	private $max_simultaneous_optimizations = 10;

	/**
	 * Supported post types
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $supported_post_types = array( 'post', 'page', 'product' );

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_Boss_Optimization_Interface Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Initialize the interface
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Admin hooks
		add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );

		// AJAX handlers
		add_action( 'wp_ajax_sao_boss_optimize', array( $this, 'ajax_boss_optimize' ) );
		add_action( 'wp_ajax_sao_boss_get_content', array( $this, 'ajax_get_content' ) );
		add_action( 'wp_ajax_sao_boss_bulk_optimize', array( $this, 'ajax_bulk_optimize' ) );
		add_action( 'wp_ajax_sao_boss_get_stats', array( $this, 'ajax_get_stats' ) );
	}

	/**
	 * Add admin menu
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_admin_menu() {
		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			return;
		}

		add_submenu_page(
			'seo-auto-optimizer',
			esc_html__( 'Boss Optimization', 'seo-auto-optimizer' ),
			esc_html__( 'Boss Optimization', 'seo-auto-optimizer' ),
			'edit_posts',
			'seo-auto-optimizer-boss',
			array( $this, 'render_boss_page' )
		);
	}

	/**
	 * Enqueue scripts and styles
	 *
	 * @since 1.0.0
	 * @param string $hook_suffix Current admin page hook suffix
	 * @return void
	 */
	public function enqueue_scripts( $hook_suffix ) {
		// Only load on boss optimization page
		if ( strpos( $hook_suffix, 'seo-auto-optimizer-boss' ) === false ) {
			return;
		}

		// Enqueue CSS
		wp_enqueue_style(
			'sao-boss-optimization',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'css/boss-optimization.css',
			array(),
			SEO_AUTO_OPTIMIZER_VERSION
		);

		// Enqueue JS
		wp_enqueue_script(
			'sao-boss-optimization',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'js/boss-optimization.js',
			array( 'jquery', 'wp-util' ),
			SEO_AUTO_OPTIMIZER_VERSION,
			true
		);

		// Localize script
		wp_localize_script(
			'sao-boss-optimization',
			'saoBoss',
			array(
				'ajaxUrl'    => admin_url( 'admin-ajax.php' ),
				'nonce'      => wp_create_nonce( 'sao_boss_nonce' ),
				'strings'    => array(
					'optimizing'         => esc_html__( 'Optimizing...', 'seo-auto-optimizer' ),
					'optimized'          => esc_html__( 'Optimized', 'seo-auto-optimizer' ),
					'failed'             => esc_html__( 'Failed', 'seo-auto-optimizer' ),
					'selectAll'          => esc_html__( 'Select All', 'seo-auto-optimizer' ),
					'deselectAll'        => esc_html__( 'Deselect All', 'seo-auto-optimizer' ),
					'optimizeSelected'   => esc_html__( 'Optimize Selected', 'seo-auto-optimizer' ),
					'optimizeAll'        => esc_html__( 'Optimize All', 'seo-auto-optimizer' ),
					'confirmOptimizeAll' => esc_html__( 'Are you sure you want to optimize all content? This may take a while.', 'seo-auto-optimizer' ),
					'noSelection'        => esc_html__( 'Please select at least one item to optimize.', 'seo-auto-optimizer' ),
					'rateLimitExceeded'  => esc_html__( 'Rate limit exceeded. Please wait before optimizing more content.', 'seo-auto-optimizer' ),
					'error'              => esc_html__( 'An error occurred. Please try again.', 'seo-auto-optimizer' ),
					'completed'          => esc_html__( 'Optimization completed!', 'seo-auto-optimizer' ),
					'processing'         => esc_html__( 'Processing...', 'seo-auto-optimizer' ),
				),
				'settings'   => array(
					'maxSimultaneous' => $this->max_simultaneous_optimizations,
					'autoRefresh'     => 30000, // 30 seconds
					'perPage'         => 25,
				),
			)
		);
	}

	/**
	 * Render boss optimization page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function render_boss_page() {
		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'seo-auto-optimizer' ) );
		}

		echo '<div class="wrap sao-boss-wrap">';
		echo '<h1 class="wp-heading-inline">' . esc_html__( 'Boss Optimization', 'seo-auto-optimizer' ) . '</h1>';
		echo '<p class="description">' . esc_html__( 'Optimize all your content in bulk with AI-powered SEO suggestions.', 'seo-auto-optimizer' ) . '</p>';
		echo '<hr class="wp-header-end">';

		// Render overview widget
		$this->render_overview_widget();

		// Render filters
		$this->render_filters();

		// Render bulk actions
		$this->render_bulk_actions();

		// Render content table
		$this->render_content_table();

		// Render progress modal
		$this->render_progress_modal();

		echo '</div>';
	}

	/**
	 * Render overview widget
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_overview_widget() {
		$stats = $this->get_content_stats();

		echo '<div class="sao-overview-widget">';
		echo '<div class="sao-overview-cards">';

		// Total content
		echo '<div class="sao-overview-card sao-card-total">';
		echo '<div class="sao-card-icon"><span class="dashicons dashicons-admin-post"></span></div>';
		echo '<div class="sao-card-content">';
		echo '<div class="sao-card-number">' . number_format( $stats['total'] ) . '</div>';
		echo '<div class="sao-card-label">' . esc_html__( 'Total Content', 'seo-auto-optimizer' ) . '</div>';
		echo '</div>';
		echo '</div>';

		// Optimized content
		echo '<div class="sao-overview-card sao-card-optimized">';
		echo '<div class="sao-card-icon"><span class="dashicons dashicons-yes-alt"></span></div>';
		echo '<div class="sao-card-content">';
		echo '<div class="sao-card-number">' . number_format( $stats['optimized'] ) . '</div>';
		echo '<div class="sao-card-label">' . esc_html__( 'Optimized', 'seo-auto-optimizer' ) . '</div>';
		echo '</div>';
		echo '</div>';

		// Not optimized content
		echo '<div class="sao-overview-card sao-card-pending">';
		echo '<div class="sao-card-icon"><span class="dashicons dashicons-clock"></span></div>';
		echo '<div class="sao-card-content">';
		echo '<div class="sao-card-number">' . number_format( $stats['not_optimized'] ) . '</div>';
		echo '<div class="sao-card-label">' . esc_html__( 'Not Optimized', 'seo-auto-optimizer' ) . '</div>';
		echo '</div>';
		echo '</div>';

		// Progress percentage
		$progress_percentage = $stats['total'] > 0 ? round( ( $stats['optimized'] / $stats['total'] ) * 100, 1 ) : 0;
		echo '<div class="sao-overview-card sao-card-progress">';
		echo '<div class="sao-card-icon"><span class="dashicons dashicons-chart-pie"></span></div>';
		echo '<div class="sao-card-content">';
		echo '<div class="sao-card-number">' . $progress_percentage . '%</div>';
		echo '<div class="sao-card-label">' . esc_html__( 'Progress', 'seo-auto-optimizer' ) . '</div>';
		echo '</div>';
		echo '</div>';

		echo '</div>';

		// Progress bar
		echo '<div class="sao-progress-container">';
		echo '<div class="sao-progress-bar">';
		echo '<div class="sao-progress-fill" style="width: ' . esc_attr( $progress_percentage ) . '%"></div>';
		echo '</div>';
		echo '<div class="sao-progress-text">';
		echo sprintf(
			esc_html__( '%1$d of %2$d content items optimized (%3$s%%)', 'seo-auto-optimizer' ),
			$stats['optimized'],
			$stats['total'],
			$progress_percentage
		);
		echo '</div>';
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Render filters
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_filters() {
		echo '<div class="sao-filters-container">';
		echo '<div class="sao-filters-row">';

		// Content type filter
		echo '<div class="sao-filter-group">';
		echo '<label for="sao-filter-type">' . esc_html__( 'Content Type:', 'seo-auto-optimizer' ) . '</label>';
		echo '<select id="sao-filter-type" class="sao-filter">';
		echo '<option value="">' . esc_html__( 'All Types', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="post">' . esc_html__( 'Posts', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="page">' . esc_html__( 'Pages', 'seo-auto-optimizer' ) . '</option>';
		if ( class_exists( 'WooCommerce' ) ) {
			echo '<option value="product">' . esc_html__( 'Products', 'seo-auto-optimizer' ) . '</option>';
		}
		echo '</select>';
		echo '</div>';

		// Publication status filter
		echo '<div class="sao-filter-group">';
		echo '<label for="sao-filter-status">' . esc_html__( 'Status:', 'seo-auto-optimizer' ) . '</label>';
		echo '<select id="sao-filter-status" class="sao-filter">';
		echo '<option value="">' . esc_html__( 'All Statuses', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="publish">' . esc_html__( 'Published', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="draft">' . esc_html__( 'Draft', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="private">' . esc_html__( 'Private', 'seo-auto-optimizer' ) . '</option>';
		echo '</select>';
		echo '</div>';

		// SEO status filter
		echo '<div class="sao-filter-group">';
		echo '<label for="sao-filter-seo">' . esc_html__( 'SEO Status:', 'seo-auto-optimizer' ) . '</label>';
		echo '<select id="sao-filter-seo" class="sao-filter">';
		echo '<option value="">' . esc_html__( 'All SEO Statuses', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="optimized">' . esc_html__( 'Optimized', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="not_optimized">' . esc_html__( 'Not Optimized', 'seo-auto-optimizer' ) . '</option>';
		echo '<option value="in_progress">' . esc_html__( 'In Progress', 'seo-auto-optimizer' ) . '</option>';
		echo '</select>';
		echo '</div>';

		// Search filter
		echo '<div class="sao-filter-group sao-search-group">';
		echo '<label for="sao-filter-search">' . esc_html__( 'Search:', 'seo-auto-optimizer' ) . '</label>';
		echo '<input type="text" id="sao-filter-search" class="sao-filter" placeholder="' . esc_attr__( 'Search by title...', 'seo-auto-optimizer' ) . '" />';
		echo '</div>';

		// Items per page
		echo '<div class="sao-filter-group">';
		echo '<label for="sao-per-page">' . esc_html__( 'Per Page:', 'seo-auto-optimizer' ) . '</label>';
		echo '<select id="sao-per-page" class="sao-filter">';
		echo '<option value="25">25</option>';
		echo '<option value="50">50</option>';
		echo '<option value="100">100</option>';
		echo '</select>';
		echo '</div>';

		echo '</div>';
		echo '</div>';
	}

	/**
	 * Render bulk actions
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_bulk_actions() {
		echo '<div class="sao-bulk-actions-container">';
		echo '<div class="sao-bulk-actions-left">';

		// Select all checkbox
		echo '<label class="sao-select-all-label">';
		echo '<input type="checkbox" id="sao-select-all" class="sao-select-all" />';
		echo '<span>' . esc_html__( 'Select All', 'seo-auto-optimizer' ) . '</span>';
		echo '</label>';

		// Selected count
		echo '<span class="sao-selected-count">0 ' . esc_html__( 'selected', 'seo-auto-optimizer' ) . '</span>';

		echo '</div>';

		echo '<div class="sao-bulk-actions-right">';

		// Bulk optimize button
		echo '<button type="button" id="sao-bulk-optimize" class="button button-primary" disabled>';
		echo '<span class="dashicons dashicons-update"></span>';
		echo esc_html__( 'Optimize Selected', 'seo-auto-optimizer' );
		echo '</button>';

		// Optimize all button
		echo '<button type="button" id="sao-optimize-all" class="button button-secondary">';
		echo '<span class="dashicons dashicons-update-alt"></span>';
		echo esc_html__( 'Optimize All', 'seo-auto-optimizer' );
		echo '</button>';

		// Refresh button
		echo '<button type="button" id="sao-refresh-table" class="button">';
		echo '<span class="dashicons dashicons-update"></span>';
		echo esc_html__( 'Refresh', 'seo-auto-optimizer' );
		echo '</button>';

		echo '</div>';
		echo '</div>';
	}

	/**
	 * Render content table
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_content_table() {
		echo '<div class="sao-table-container">';
		echo '<div class="sao-table-loading">';
		echo '<span class="spinner is-active"></span>';
		echo '<span>' . esc_html__( 'Loading content...', 'seo-auto-optimizer' ) . '</span>';
		echo '</div>';

		echo '<table class="sao-content-table wp-list-table widefat fixed striped" style="display: none;">';
		echo '<thead>';
		echo '<tr>';
		echo '<th class="sao-col-select"><input type="checkbox" class="sao-select-all-header" /></th>';
		echo '<th class="sao-col-title sortable" data-sort="title">';
		echo esc_html__( 'Title', 'seo-auto-optimizer' );
		echo '<span class="sorting-indicator"></span>';
		echo '</th>';
		echo '<th class="sao-col-type sortable" data-sort="type">';
		echo esc_html__( 'Type', 'seo-auto-optimizer' );
		echo '<span class="sorting-indicator"></span>';
		echo '</th>';
		echo '<th class="sao-col-status sortable" data-sort="status">';
		echo esc_html__( 'Status', 'seo-auto-optimizer' );
		echo '<span class="sorting-indicator"></span>';
		echo '</th>';
		echo '<th class="sao-col-seo-status sortable" data-sort="seo_status">';
		echo esc_html__( 'SEO Status', 'seo-auto-optimizer' );
		echo '<span class="sorting-indicator"></span>';
		echo '</th>';
		echo '<th class="sao-col-keywords">' . esc_html__( 'Keywords', 'seo-auto-optimizer' ) . '</th>';
		echo '<th class="sao-col-last-optimized sortable" data-sort="last_optimized">';
		echo esc_html__( 'Last Optimized', 'seo-auto-optimizer' );
		echo '<span class="sorting-indicator"></span>';
		echo '</th>';
		echo '<th class="sao-col-actions">' . esc_html__( 'Actions', 'seo-auto-optimizer' ) . '</th>';
		echo '</tr>';
		echo '</thead>';
		echo '<tbody id="sao-content-tbody">';
		echo '<!-- Content will be loaded via AJAX -->';
		echo '</tbody>';
		echo '</table>';

		// Pagination
		echo '<div class="sao-pagination-container" style="display: none;">';
		echo '<div class="sao-pagination-info"></div>';
		echo '<div class="sao-pagination-controls"></div>';
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Render progress modal
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_progress_modal() {
		echo '<div id="sao-progress-modal" class="sao-modal" style="display: none;">';
		echo '<div class="sao-modal-content">';
		echo '<div class="sao-modal-header">';
		echo '<h2>' . esc_html__( 'Optimization Progress', 'seo-auto-optimizer' ) . '</h2>';
		echo '<button type="button" class="sao-modal-close">&times;</button>';
		echo '</div>';
		echo '<div class="sao-modal-body">';

		// Overall progress
		echo '<div class="sao-progress-section">';
		echo '<h3>' . esc_html__( 'Overall Progress', 'seo-auto-optimizer' ) . '</h3>';
		echo '<div class="sao-progress-bar-container">';
		echo '<div class="sao-progress-bar">';
		echo '<div class="sao-progress-bar-fill" style="width: 0%"></div>';
		echo '</div>';
		echo '<div class="sao-progress-text">0 / 0 (0%)</div>';
		echo '</div>';
		echo '</div>';

		// Current item
		echo '<div class="sao-current-item-section">';
		echo '<h3>' . esc_html__( 'Current Item', 'seo-auto-optimizer' ) . '</h3>';
		echo '<div class="sao-current-item-info">';
		echo '<span class="sao-current-item-title">-</span>';
		echo '<span class="sao-current-item-status">-</span>';
		echo '</div>';
		echo '</div>';

		// Results summary
		echo '<div class="sao-results-section">';
		echo '<h3>' . esc_html__( 'Results', 'seo-auto-optimizer' ) . '</h3>';
		echo '<div class="sao-results-grid">';
		echo '<div class="sao-result-item">';
		echo '<span class="sao-result-label">' . esc_html__( 'Successful:', 'seo-auto-optimizer' ) . '</span>';
		echo '<span class="sao-result-value sao-success-count">0</span>';
		echo '</div>';
		echo '<div class="sao-result-item">';
		echo '<span class="sao-result-label">' . esc_html__( 'Failed:', 'seo-auto-optimizer' ) . '</span>';
		echo '<span class="sao-result-value sao-error-count">0</span>';
		echo '</div>';
		echo '<div class="sao-result-item">';
		echo '<span class="sao-result-label">' . esc_html__( 'Keywords Added:', 'seo-auto-optimizer' ) . '</span>';
		echo '<span class="sao-result-value sao-keywords-count">0</span>';
		echo '</div>';
		echo '<div class="sao-result-item">';
		echo '<span class="sao-result-label">' . esc_html__( 'Time Elapsed:', 'seo-auto-optimizer' ) . '</span>';
		echo '<span class="sao-result-value sao-time-elapsed">0s</span>';
		echo '</div>';
		echo '</div>';
		echo '</div>';

		// Activity log
		echo '<div class="sao-activity-section">';
		echo '<h3>' . esc_html__( 'Activity Log', 'seo-auto-optimizer' ) . '</h3>';
		echo '<div class="sao-activity-log"></div>';
		echo '</div>';

		echo '</div>';
		echo '<div class="sao-modal-footer">';
		echo '<button type="button" class="button button-secondary sao-modal-close">' . esc_html__( 'Close', 'seo-auto-optimizer' ) . '</button>';
		echo '<button type="button" class="button button-primary sao-stop-optimization" style="display: none;">' . esc_html__( 'Stop Optimization', 'seo-auto-optimizer' ) . '</button>';
		echo '</div>';
		echo '</div>';
		echo '</div>';
	}

	/**
	 * Get content statistics
	 *
	 * @since 1.0.0
	 * @return array Content statistics
	 */
	private function get_content_stats() {
		global $wpdb;

		$post_types = $this->get_supported_post_types();
		$post_types_placeholder = implode( ',', array_fill( 0, count( $post_types ), '%s' ) );

		// Get total count
		$total_query = $wpdb->prepare(
			"SELECT COUNT(*) FROM {$wpdb->posts}
			WHERE post_type IN ($post_types_placeholder)
			AND post_status IN ('publish', 'draft', 'private')",
			...$post_types
		);
		$total = (int) $wpdb->get_var( $total_query );

		// Get optimized count (posts with SEO meta data)
		$optimized_query = $wpdb->prepare(
			"SELECT COUNT(DISTINCT p.ID) FROM {$wpdb->posts} p
			INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
			WHERE p.post_type IN ($post_types_placeholder)
			AND p.post_status IN ('publish', 'draft', 'private')
			AND pm.meta_key = '_sao_optimized'
			AND pm.meta_value = '1'",
			...$post_types
		);
		$optimized = (int) $wpdb->get_var( $optimized_query );

		return array(
			'total'         => $total,
			'optimized'     => $optimized,
			'not_optimized' => $total - $optimized,
		);
	}

	/**
	 * Get supported post types
	 *
	 * @since 1.0.0
	 * @return array Supported post types
	 */
	private function get_supported_post_types() {
		$post_types = array( 'post', 'page' );

		// Add WooCommerce product if available
		if ( class_exists( 'WooCommerce' ) ) {
			$post_types[] = 'product';
		}

		// Allow filtering
		return apply_filters( 'sao_boss_supported_post_types', $post_types );
	}

	/**
	 * AJAX handler for getting content
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_get_content() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_boss_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		try {
			$page = absint( $_POST['page'] ?? 1 );
			$per_page = absint( $_POST['per_page'] ?? 25 );
			$filters = $this->sanitize_filters( $_POST['filters'] ?? array() );
			$sort = sanitize_text_field( $_POST['sort'] ?? 'title' );
			$order = sanitize_text_field( $_POST['order'] ?? 'ASC' );

			$content_data = $this->get_content_data( $page, $per_page, $filters, $sort, $order );

			wp_send_json_success( $content_data );

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Sanitize filters
	 *
	 * @since 1.0.0
	 * @param array $filters Raw filters
	 * @return array Sanitized filters
	 */
	private function sanitize_filters( $filters ) {
		$sanitized = array();

		if ( isset( $filters['type'] ) ) {
			$sanitized['type'] = sanitize_text_field( $filters['type'] );
		}

		if ( isset( $filters['status'] ) ) {
			$sanitized['status'] = sanitize_text_field( $filters['status'] );
		}

		if ( isset( $filters['seo_status'] ) ) {
			$sanitized['seo_status'] = sanitize_text_field( $filters['seo_status'] );
		}

		if ( isset( $filters['search'] ) ) {
			$sanitized['search'] = sanitize_text_field( $filters['search'] );
		}

		return $sanitized;
	}

	/**
	 * Get content data for table
	 *
	 * @since 1.0.0
	 * @param int    $page Current page
	 * @param int    $per_page Items per page
	 * @param array  $filters Filters to apply
	 * @param string $sort Sort column
	 * @param string $order Sort order
	 * @return array Content data
	 */
	private function get_content_data( $page, $per_page, $filters, $sort, $order ) {
		global $wpdb;

		$offset = ( $page - 1 ) * $per_page;
		$post_types = $this->get_supported_post_types();

		// Build WHERE clause
		$where_conditions = array();
		$where_values = array();

		// Post types
		$post_types_placeholder = implode( ',', array_fill( 0, count( $post_types ), '%s' ) );
		$where_conditions[] = "p.post_type IN ($post_types_placeholder)";
		$where_values = array_merge( $where_values, $post_types );

		// Post status
		if ( ! empty( $filters['status'] ) ) {
			$where_conditions[] = 'p.post_status = %s';
			$where_values[] = $filters['status'];
		} else {
			$where_conditions[] = "p.post_status IN ('publish', 'draft', 'private')";
		}

		// Content type filter
		if ( ! empty( $filters['type'] ) ) {
			$where_conditions[] = 'p.post_type = %s';
			$where_values[] = $filters['type'];
		}

		// Search filter
		if ( ! empty( $filters['search'] ) ) {
			$where_conditions[] = 'p.post_title LIKE %s';
			$where_values[] = '%' . $wpdb->esc_like( $filters['search'] ) . '%';
		}

		$where_clause = implode( ' AND ', $where_conditions );

		// Build ORDER BY clause
		$allowed_sort_columns = array( 'title', 'type', 'status', 'date', 'last_optimized' );
		$sort_column = in_array( $sort, $allowed_sort_columns, true ) ? $sort : 'title';
		$sort_order = strtoupper( $order ) === 'DESC' ? 'DESC' : 'ASC';

		$order_by_map = array(
			'title'          => 'p.post_title',
			'type'           => 'p.post_type',
			'status'         => 'p.post_status',
			'date'           => 'p.post_date',
			'last_optimized' => 'pm_optimized.meta_value',
		);

		$order_by = $order_by_map[ $sort_column ] . ' ' . $sort_order;

		// Main query
		$query = "
			SELECT
				p.ID,
				p.post_title,
				p.post_type,
				p.post_status,
				p.post_date,
				pm_optimized.meta_value as is_optimized,
				pm_keywords.meta_value as keywords_count,
				pm_last_opt.meta_value as last_optimized
			FROM {$wpdb->posts} p
			LEFT JOIN {$wpdb->postmeta} pm_optimized ON p.ID = pm_optimized.post_id AND pm_optimized.meta_key = '_sao_optimized'
			LEFT JOIN {$wpdb->postmeta} pm_keywords ON p.ID = pm_keywords.post_id AND pm_keywords.meta_key = '_sao_keywords_count'
			LEFT JOIN {$wpdb->postmeta} pm_last_opt ON p.ID = pm_last_opt.post_id AND pm_last_opt.meta_key = '_sao_last_optimized'
			WHERE $where_clause
			ORDER BY $order_by
			LIMIT %d OFFSET %d
		";

		$where_values[] = $per_page;
		$where_values[] = $offset;

		$prepared_query = $wpdb->prepare( $query, ...$where_values );
		$results = $wpdb->get_results( $prepared_query );

		// Get total count for pagination
		$count_query = "
			SELECT COUNT(*)
			FROM {$wpdb->posts} p
			WHERE $where_clause
		";

		$count_values = array_slice( $where_values, 0, -2 ); // Remove LIMIT and OFFSET values
		$total_items = (int) $wpdb->get_var( $wpdb->prepare( $count_query, ...$count_values ) );

		// Process results
		$content_items = array();
		foreach ( $results as $row ) {
			$seo_status = $this->get_seo_status( $row );

			// Apply SEO status filter
			if ( ! empty( $filters['seo_status'] ) && $seo_status !== $filters['seo_status'] ) {
				continue;
			}

			$content_items[] = array(
				'id'             => $row->ID,
				'title'          => $row->post_title,
				'type'           => $row->post_type,
				'status'         => $row->post_status,
				'seo_status'     => $seo_status,
				'keywords_count' => absint( $row->keywords_count ),
				'last_optimized' => $row->last_optimized ? date_i18n( get_option( 'date_format' ), $row->last_optimized ) : '',
				'edit_link'      => get_edit_post_link( $row->ID ),
				'view_link'      => get_permalink( $row->ID ),
			);
		}

		return array(
			'items'       => $content_items,
			'total_items' => $total_items,
			'total_pages' => ceil( $total_items / $per_page ),
			'current_page' => $page,
			'per_page'    => $per_page,
		);
	}

	/**
	 * Get SEO status for a post
	 *
	 * @since 1.0.0
	 * @param object $row Database row
	 * @return string SEO status
	 */
	private function get_seo_status( $row ) {
		if ( $row->is_optimized === '1' ) {
			return 'optimized';
		}

		// Check if optimization is in progress (you might store this in a transient)
		$in_progress_key = 'sao_optimizing_' . $row->ID;
		if ( get_transient( $in_progress_key ) ) {
			return 'in_progress';
		}

		return 'not_optimized';
	}

	/**
	 * AJAX handler for boss optimization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_boss_optimize() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_boss_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		$post_id = absint( $_POST['post_id'] ?? 0 );
		if ( ! $post_id ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Invalid post ID.', 'seo-auto-optimizer' ) ) );
		}

		// Check rate limit
		if ( ! $this->check_rate_limit() ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Rate limit exceeded. Please wait before optimizing more content.', 'seo-auto-optimizer' ) ) );
		}

		// Check if user can edit this post
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'You do not have permission to edit this post.', 'seo-auto-optimizer' ) ) );
		}

		try {
			// Set optimization in progress
			$in_progress_key = 'sao_optimizing_' . $post_id;
			set_transient( $in_progress_key, true, 300 ); // 5 minutes

			// Get post
			$post = get_post( $post_id );
			if ( ! $post ) {
				wp_send_json_error( array( 'message' => esc_html__( 'Post not found.', 'seo-auto-optimizer' ) ) );
			}

			// Perform optimization
			$optimization_result = $this->perform_single_optimization( $post );

			// Update rate limit
			$this->update_rate_limit();

			// Clear in progress flag
			delete_transient( $in_progress_key );

			// Mark as optimized
			update_post_meta( $post_id, '_sao_optimized', '1' );
			update_post_meta( $post_id, '_sao_last_optimized', current_time( 'timestamp' ) );
			update_post_meta( $post_id, '_sao_keywords_count', count( $optimization_result['keywords'] ?? array() ) );

			wp_send_json_success( array(
				'message'        => esc_html__( 'Content optimized successfully!', 'seo-auto-optimizer' ),
				'keywords_count' => count( $optimization_result['keywords'] ?? array() ),
				'seo_score'      => $optimization_result['seo_score'] ?? 0,
				'time_taken'     => $optimization_result['time_taken'] ?? 0,
			) );

		} catch ( Exception $e ) {
			// Clear in progress flag
			delete_transient( $in_progress_key );

			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * AJAX handler for bulk optimization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_bulk_optimize() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_boss_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		$post_ids = array_map( 'absint', $_POST['post_ids'] ?? array() );
		if ( empty( $post_ids ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'No posts selected.', 'seo-auto-optimizer' ) ) );
		}

		// Limit bulk operations
		if ( count( $post_ids ) > $this->max_simultaneous_optimizations ) {
			wp_send_json_error( array(
				'message' => sprintf(
					esc_html__( 'Too many posts selected. Maximum allowed: %d', 'seo-auto-optimizer' ),
					$this->max_simultaneous_optimizations
				)
			) );
		}

		// Check rate limit
		if ( ! $this->check_rate_limit() ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Rate limit exceeded. Please wait before optimizing more content.', 'seo-auto-optimizer' ) ) );
		}

		$results = array(
			'successful' => 0,
			'failed'     => 0,
			'errors'     => array(),
			'keywords_added' => 0,
		);

		foreach ( $post_ids as $post_id ) {
			// Check if user can edit this post
			if ( ! current_user_can( 'edit_post', $post_id ) ) {
				$results['failed']++;
				$results['errors'][] = sprintf(
					esc_html__( 'No permission to edit post ID %d', 'seo-auto-optimizer' ),
					$post_id
				);
				continue;
			}

			try {
				$post = get_post( $post_id );
				if ( ! $post ) {
					$results['failed']++;
					$results['errors'][] = sprintf(
						esc_html__( 'Post ID %d not found', 'seo-auto-optimizer' ),
						$post_id
					);
					continue;
				}

				// Perform optimization
				$optimization_result = $this->perform_single_optimization( $post );

				// Mark as optimized
				update_post_meta( $post_id, '_sao_optimized', '1' );
				update_post_meta( $post_id, '_sao_last_optimized', current_time( 'timestamp' ) );
				$keywords_count = count( $optimization_result['keywords'] ?? array() );
				update_post_meta( $post_id, '_sao_keywords_count', $keywords_count );

				$results['successful']++;
				$results['keywords_added'] += $keywords_count;

			} catch ( Exception $e ) {
				$results['failed']++;
				$results['errors'][] = sprintf(
					esc_html__( 'Error optimizing post ID %d: %s', 'seo-auto-optimizer' ),
					$post_id,
					$e->getMessage()
				);
			}
		}

		// Update rate limit
		$this->update_rate_limit();

		wp_send_json_success( $results );
	}

	/**
	 * AJAX handler for getting statistics
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_get_stats() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_boss_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'edit_posts' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		$stats = $this->get_content_stats();
		wp_send_json_success( $stats );
	}

	/**
	 * Perform single optimization
	 *
	 * @since 1.0.0
	 * @param WP_Post $post Post object
	 * @return array Optimization results
	 */
	private function perform_single_optimization( $post ) {
		$start_time = microtime( true );

		// Get optimization interface
		$optimization_interface = SEO_Auto_Optimizer_Optimization_Interface::get_instance();

		// Perform optimization using existing interface
		$result = $optimization_interface->perform_content_optimization(
			$post->post_content,
			$post->post_title,
			$post
		);

		$end_time = microtime( true );
		$result['time_taken'] = round( $end_time - $start_time, 2 );

		return $result;
	}

	/**
	 * Check rate limit
	 *
	 * @since 1.0.0
	 * @return bool True if within rate limit
	 */
	private function check_rate_limit() {
		$user_id = get_current_user_id();
		$cache_key = $this->rate_limit_key . $user_id;
		$current_count = get_transient( $cache_key );

		if ( $current_count === false ) {
			return true; // No rate limit data, allow
		}

		return $current_count < $this->max_simultaneous_optimizations;
	}

	/**
	 * Update rate limit
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function update_rate_limit() {
		$user_id = get_current_user_id();
		$cache_key = $this->rate_limit_key . $user_id;
		$current_count = get_transient( $cache_key );

		if ( $current_count === false ) {
			$current_count = 0;
		}

		$current_count++;
		set_transient( $cache_key, $current_count, 60 ); // 1 minute window
	}

	/**
	 * Get post type icon
	 *
	 * @since 1.0.0
	 * @param string $post_type Post type
	 * @return string Icon class
	 */
	private function get_post_type_icon( $post_type ) {
		$icons = array(
			'post'    => 'dashicons-admin-post',
			'page'    => 'dashicons-admin-page',
			'product' => 'dashicons-products',
		);

		return $icons[ $post_type ] ?? 'dashicons-admin-post';
	}

	/**
	 * Get status badge class
	 *
	 * @since 1.0.0
	 * @param string $status Status
	 * @return string Badge class
	 */
	private function get_status_badge_class( $status ) {
		$classes = array(
			'publish'  => 'sao-status-published',
			'draft'    => 'sao-status-draft',
			'private'  => 'sao-status-private',
			'pending'  => 'sao-status-pending',
		);

		return $classes[ $status ] ?? 'sao-status-unknown';
	}

	/**
	 * Get SEO status badge class
	 *
	 * @since 1.0.0
	 * @param string $seo_status SEO status
	 * @return string Badge class
	 */
	private function get_seo_status_badge_class( $seo_status ) {
		$classes = array(
			'optimized'     => 'sao-seo-optimized',
			'not_optimized' => 'sao-seo-not-optimized',
			'in_progress'   => 'sao-seo-in-progress',
		);

		return $classes[ $seo_status ] ?? 'sao-seo-unknown';
	}

	/**
	 * Get SEO status label
	 *
	 * @since 1.0.0
	 * @param string $seo_status SEO status
	 * @return string Status label
	 */
	private function get_seo_status_label( $seo_status ) {
		$labels = array(
			'optimized'     => esc_html__( 'Optimized', 'seo-auto-optimizer' ),
			'not_optimized' => esc_html__( 'Not Optimized', 'seo-auto-optimizer' ),
			'in_progress'   => esc_html__( 'In Progress', 'seo-auto-optimizer' ),
		);

		return $labels[ $seo_status ] ?? esc_html__( 'Unknown', 'seo-auto-optimizer' );
	}

	/**
	 * Get post type label
	 *
	 * @since 1.0.0
	 * @param string $post_type Post type
	 * @return string Post type label
	 */
	private function get_post_type_label( $post_type ) {
		$post_type_object = get_post_type_object( $post_type );
		return $post_type_object ? $post_type_object->labels->singular_name : ucfirst( $post_type );
	}

	/**
	 * Get status label
	 *
	 * @since 1.0.0
	 * @param string $status Post status
	 * @return string Status label
	 */
	private function get_status_label( $status ) {
		$labels = array(
			'publish' => esc_html__( 'Published', 'seo-auto-optimizer' ),
			'draft'   => esc_html__( 'Draft', 'seo-auto-optimizer' ),
			'private' => esc_html__( 'Private', 'seo-auto-optimizer' ),
			'pending' => esc_html__( 'Pending', 'seo-auto-optimizer' ),
		);

		return $labels[ $status ] ?? ucfirst( $status );
	}

	/**
	 * Log optimization activity
	 *
	 * @since 1.0.0
	 * @param string $message Log message
	 * @param string $level Log level
	 * @return void
	 */
	private function log_activity( $message, $level = 'info' ) {
		if ( defined( 'WP_DEBUG_LOG' ) && WP_DEBUG_LOG ) {
			error_log( "SEO Auto Optimizer Boss: [{$level}] {$message}" );
		}
	}

	/**
	 * Get current optimization queue size
	 *
	 * @since 1.0.0
	 * @return int Queue size
	 */
	private function get_optimization_queue_size() {
		global $wpdb;

		// Count posts currently being optimized (with transients)
		$count = $wpdb->get_var(
			"SELECT COUNT(*) FROM {$wpdb->options}
			WHERE option_name LIKE '_transient_sao_optimizing_%'"
		);

		return (int) $count;
	}

	/**
	 * Check if optimization is allowed
	 *
	 * @since 1.0.0
	 * @return bool True if optimization is allowed
	 */
	private function is_optimization_allowed() {
		// Check if too many optimizations are running
		if ( $this->get_optimization_queue_size() >= $this->max_simultaneous_optimizations ) {
			return false;
		}

		// Check rate limit
		if ( ! $this->check_rate_limit() ) {
			return false;
		}

		return true;
	}

	/**
	 * Clean up expired optimization locks
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function cleanup_expired_locks() {
		global $wpdb;

		// Clean up expired transients (WordPress should do this automatically, but just in case)
		$wpdb->query(
			"DELETE FROM {$wpdb->options}
			WHERE option_name LIKE '_transient_timeout_sao_optimizing_%'
			AND option_value < UNIX_TIMESTAMP()"
		);

		$wpdb->query(
			"DELETE FROM {$wpdb->options}
			WHERE option_name LIKE '_transient_sao_optimizing_%'
			AND option_name NOT IN (
				SELECT REPLACE(option_name, '_transient_timeout_', '_transient_')
				FROM {$wpdb->options}
				WHERE option_name LIKE '_transient_timeout_sao_optimizing_%'
			)"
		);
	}
}

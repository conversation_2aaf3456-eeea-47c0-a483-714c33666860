<?php
/**
 * Plugin Name: SEO Auto Optimizer with AI
 * Plugin URI: https://seocanyon.com/seo-auto-optimizer
 * Description: Advanced SEO optimization plugin with AI-powered content analysis and automatic optimization features.
 * Version: 1.0.0
 * Author: SEO Canyon
 * Author URI: https://seocanyon.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: seo-auto-optimizer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package SEO_Auto_Optimizer
 * @version 1.0.0
 * <AUTHOR> Canyon
 * @copyright 2024 SEO Canyon
 * @license GPL-2.0-or-later
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

// Define plugin constants
define( 'SEO_AUTO_OPTIMIZER_VERSION', '1.0.0' );
define( 'SEO_AUTO_OPTIMIZER_PLUGIN_FILE', __FILE__ );
define( 'SEO_AUTO_OPTIMIZER_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'SEO_AUTO_OPTIMIZER_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'SEO_AUTO_OPTIMIZER_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );
define( 'SEO_AUTO_OPTIMIZER_INCLUDES_DIR', SEO_AUTO_OPTIMIZER_PLUGIN_DIR . 'includes/' );
define( 'SEO_AUTO_OPTIMIZER_ASSETS_URL', SEO_AUTO_OPTIMIZER_PLUGIN_URL . 'assets/' );
define( 'SEO_AUTO_OPTIMIZER_TEMPLATES_DIR', SEO_AUTO_OPTIMIZER_PLUGIN_DIR . 'templates/' );
define( 'SEO_AUTO_OPTIMIZER_LANGUAGES_DIR', SEO_AUTO_OPTIMIZER_PLUGIN_DIR . 'languages/' );

/**
 * Main plugin class autoloader
 *
 * @param string $class_name The class name to load
 * @return void
 */
function seo_auto_optimizer_autoloader( $class_name ) {
	// Check if the class belongs to our plugin
	if ( strpos( $class_name, 'SEO_Auto_Optimizer' ) !== 0 ) {
		return;
	}

	// Convert class name to file name
	$class_file = strtolower( str_replace( '_', '-', $class_name ) );
	$class_file = str_replace( 'seo-auto-optimizer-', '', $class_file );
	$file_path = SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-' . $class_file . '.php';

	// Load the class file if it exists
	if ( file_exists( $file_path ) ) {
		require_once $file_path;
	}
}

// Register the autoloader
spl_autoload_register( 'seo_auto_optimizer_autoloader' );

/**
 * Main plugin initialization function
 *
 * @return SEO_Auto_Optimizer|false Main plugin instance or false on failure
 */
function seo_auto_optimizer() {
	// Check minimum requirements
	if ( ! seo_auto_optimizer_check_requirements() ) {
		return false;
	}

	// Load the main plugin class
	require_once SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';

	// Initialize and return the main plugin instance
	return SEO_Auto_Optimizer::get_instance();
}

/**
 * Check plugin requirements
 *
 * @return bool True if requirements are met, false otherwise
 */
function seo_auto_optimizer_check_requirements() {
	global $wp_version;

	// Check WordPress version
	if ( version_compare( $wp_version, '5.0', '<' ) ) {
		add_action( 'admin_notices', 'seo_auto_optimizer_wp_version_notice' );
		return false;
	}

	// Check PHP version
	if ( version_compare( PHP_VERSION, '7.4', '<' ) ) {
		add_action( 'admin_notices', 'seo_auto_optimizer_php_version_notice' );
		return false;
	}

	return true;
}

/**
 * Display WordPress version notice
 *
 * @return void
 */
function seo_auto_optimizer_wp_version_notice() {
	$message = sprintf(
		/* translators: %s: Required WordPress version */
		esc_html__( 'SEO Auto Optimizer requires WordPress version %s or higher.', 'seo-auto-optimizer' ),
		'5.0'
	);

	printf(
		'<div class="notice notice-error"><p>%s</p></div>',
		esc_html( $message )
	);
}

/**
 * Display PHP version notice
 *
 * @return void
 */
function seo_auto_optimizer_php_version_notice() {
	$message = sprintf(
		/* translators: %s: Required PHP version */
		esc_html__( 'SEO Auto Optimizer requires PHP version %s or higher.', 'seo-auto-optimizer' ),
		'7.4'
	);

	printf(
		'<div class="notice notice-error"><p>%s</p></div>',
		esc_html( $message )
	);
}

/**
 * Plugin activation hook
 *
 * @return void
 */
function seo_auto_optimizer_activate() {
	// Check requirements before activation
	if ( ! seo_auto_optimizer_check_requirements() ) {
		deactivate_plugins( plugin_basename( __FILE__ ) );
		wp_die(
			esc_html__( 'SEO Auto Optimizer could not be activated due to unmet requirements.', 'seo-auto-optimizer' ),
			esc_html__( 'Plugin Activation Error', 'seo-auto-optimizer' ),
			array( 'back_link' => true )
		);
	}

	// Load the main class for activation
	require_once SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
	
	// Run activation procedures
	SEO_Auto_Optimizer::activate();
}

/**
 * Plugin deactivation hook
 *
 * @return void
 */
function seo_auto_optimizer_deactivate() {
	// Load the main class for deactivation
	require_once SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
	
	// Run deactivation procedures
	SEO_Auto_Optimizer::deactivate();
}

/**
 * Plugin uninstall hook
 *
 * @return void
 */
function seo_auto_optimizer_uninstall() {
	// Load the main class for uninstall
	require_once SEO_AUTO_OPTIMIZER_INCLUDES_DIR . 'class-seo-auto-optimizer.php';
	
	// Run uninstall procedures
	SEO_Auto_Optimizer::uninstall();
}

// Register activation, deactivation, and uninstall hooks
register_activation_hook( __FILE__, 'seo_auto_optimizer_activate' );
register_deactivation_hook( __FILE__, 'seo_auto_optimizer_deactivate' );
register_uninstall_hook( __FILE__, 'seo_auto_optimizer_uninstall' );

// Initialize the plugin
add_action( 'plugins_loaded', 'seo_auto_optimizer' );

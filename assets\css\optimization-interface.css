/**
 * SEO Auto Optimizer - Optimization Interface Styles
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

/* Meta Box Styles */
.sao-optimization-meta-box {
    padding: 10px 0;
}

.sao-optimization-meta-box p {
    margin-bottom: 15px;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.sao-optimize-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 8px 12px;
    font-size: 13px;
    line-height: 1.4;
}

.sao-optimize-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.sao-optimize-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.sao-media-optimize-btn {
    margin-left: 5px;
    display: inline-flex;
    align-items: center;
    gap: 3px;
    padding: 4px 8px;
    font-size: 12px;
}

/* Status and Loading */
.sao-optimization-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: #f0f6fc;
    border: 1px solid #c3d9ed;
    border-radius: 4px;
}

.sao-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: sao-spin 1s linear infinite;
}

@keyframes sao-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.sao-status-text {
    font-size: 12px;
    color: #0073aa;
    font-weight: 500;
}

.sao-last-optimization {
    padding: 5px 0;
    border-top: 1px solid #eee;
}

.sao-last-optimization-text {
    color: #666;
    font-size: 11px;
}

/* Modal Styles */
.sao-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sao-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.sao-modal {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(-20px);
    transition: transform 0.3s ease;
}

.sao-modal-overlay.active .sao-modal {
    transform: scale(1) translateY(0);
}

.sao-modal-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sao-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.sao-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.sao-modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.sao-modal-body {
    padding: 25px;
}

.sao-modal-footer {
    padding: 15px 25px 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Results Content */
.sao-results-section {
    margin-bottom: 25px;
}

.sao-results-section:last-child {
    margin-bottom: 0;
}

.sao-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sao-section-title .dashicons {
    color: #0073aa;
    font-size: 18px;
}

/* SEO Score */
.sao-seo-score {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 6px;
    margin-bottom: 20px;
}

.sao-score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    position: relative;
}

.sao-score-circle.excellent {
    background: #46b450;
}

.sao-score-circle.good {
    background: #ffb900;
}

.sao-score-circle.poor {
    background: #dc3232;
}

.sao-score-details h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #333;
}

.sao-score-details p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

/* Keywords */
.sao-keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.sao-keyword-tag {
    background: #e7f3ff;
    color: #0073aa;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #c3d9ed;
}

/* Meta Description */
.sao-meta-description {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    margin-bottom: 10px;
}

.sao-meta-length {
    font-size: 11px;
    color: #666;
}

.sao-meta-length.good {
    color: #46b450;
}

.sao-meta-length.warning {
    color: #ffb900;
}

.sao-meta-length.error {
    color: #dc3232;
}

/* Title Suggestion */
.sao-title-suggestion {
    background: #f0f6fc;
    border: 1px solid #c3d9ed;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

/* Suggestions */
.sao-suggestions-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sao-suggestions-list li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    position: relative;
    padding-left: 20px;
}

.sao-suggestions-list li:last-child {
    border-bottom: none;
}

.sao-suggestions-list li:before {
    content: "→";
    position: absolute;
    left: 0;
    color: #0073aa;
    font-weight: bold;
}

/* Loading State */
.sao-modal.loading .sao-modal-body {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    flex-direction: column;
    gap: 15px;
}

.sao-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: sao-spin 1s linear infinite;
}

.sao-loading-text {
    font-size: 14px;
    color: #666;
}

/* Buttons */
.sao-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    border: 1px solid;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.sao-btn-primary {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.sao-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
}

.sao-btn-secondary {
    background: #fff;
    color: #333;
    border-color: #ddd;
}

.sao-btn-secondary:hover {
    background: #f0f0f0;
    border-color: #999;
    color: #333;
}

.sao-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Floating Notices */
.sao-floating-notice {
    position: fixed;
    top: 32px;
    right: 20px;
    background: #fff;
    border-left: 4px solid;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 12px 16px;
    border-radius: 0 4px 4px 0;
    z-index: 100001;
    max-width: 350px;
    font-size: 13px;
    line-height: 1.4;
}

.sao-notice-success {
    border-color: #46b450;
    background: #f0f8f0;
    color: #155724;
}

.sao-notice-error {
    border-color: #dc3232;
    background: #fdf2f2;
    color: #721c24;
}

.sao-notice-warning {
    border-color: #ffb900;
    background: #fffbf0;
    color: #856404;
}

/* Modal Open State */
body.sao-modal-open {
    overflow: hidden;
}

/* WordPress Admin Bar Compatibility */
.admin-bar .sao-floating-notice {
    top: 52px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sao-modal {
        width: 95%;
        margin: 20px;
    }

    .sao-modal-header,
    .sao-modal-body,
    .sao-modal-footer {
        padding-left: 15px;
        padding-right: 15px;
    }

    .sao-seo-score {
        flex-direction: column;
        text-align: center;
    }

    .sao-keywords-list {
        justify-content: center;
    }

    .sao-modal-footer {
        flex-direction: column;
    }

    .sao-btn {
        width: 100%;
        text-align: center;
    }

    .sao-floating-notice {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .admin-bar .sao-floating-notice {
        top: 46px;
    }
}

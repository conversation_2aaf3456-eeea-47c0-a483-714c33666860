/**
 * SEO Auto Optimizer Admin JavaScript
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main admin object
     */
    var SEOAutoOptimizerAdmin = {
        
        /**
         * Initialize the admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initializeComponents();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Analyze content button
            $(document).on('click', '.seo-analyze-content', this.analyzeContent);

            // Optimize content button
            $(document).on('click', '.seo-optimize-content', this.optimizeContent);

            // Settings form validation
            $(document).on('submit', '#seo-auto-optimizer-settings-form', this.validateSettings);

            // API key field toggle
            $(document).on('click', '.toggle-api-key', this.toggleApiKey);

            // SEO plugin detector refresh
            $(document).on('click', '.seo-refresh-plugins', this.refreshPluginDetection);
        },

        /**
         * Initialize components
         */
        initializeComponents: function() {
            // Initialize tooltips if available
            if (typeof $.fn.tooltip === 'function') {
                $('.seo-tooltip').tooltip();
            }
            
            // Auto-save settings
            this.initAutoSave();
        },

        /**
         * Analyze content via AJAX
         */
        analyzeContent: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $container = $button.closest('.seo-analysis-container');
            var content = $container.find('.content-to-analyze').val();
            var postId = $container.data('post-id') || 0;
            
            if (!content.trim()) {
                SEOAutoOptimizerAdmin.showNotice('error', seoAutoOptimizer.strings.error);
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).html('<span class="seo-auto-optimizer-spinner"></span> Analyzing...');
            
            // Prepare AJAX data
            var data = {
                action: 'seo_auto_optimizer_action',
                sub_action: 'analyze_content',
                nonce: seoAutoOptimizer.nonce,
                content: content,
                post_id: postId
            };
            
            // Send AJAX request
            $.post(seoAutoOptimizer.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        SEOAutoOptimizerAdmin.displayAnalysisResults(response.data, $container);
                        SEOAutoOptimizerAdmin.showNotice('success', 'Analysis completed successfully.');
                    } else {
                        SEOAutoOptimizerAdmin.showNotice('error', response.data.message || seoAutoOptimizer.strings.error);
                    }
                })
                .fail(function() {
                    SEOAutoOptimizerAdmin.showNotice('error', seoAutoOptimizer.strings.error);
                })
                .always(function() {
                    $button.prop('disabled', false).html('Analyze Content');
                });
        },

        /**
         * Optimize content via AJAX
         */
        optimizeContent: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $container = $button.closest('.seo-analysis-container');
            var content = $container.find('.content-to-analyze').val();
            var postId = $container.data('post-id') || 0;
            
            if (!content.trim()) {
                SEOAutoOptimizerAdmin.showNotice('error', 'Content is required for optimization.');
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).html('<span class="seo-auto-optimizer-spinner"></span> Optimizing...');
            
            // Prepare AJAX data
            var data = {
                action: 'seo_auto_optimizer_action',
                sub_action: 'optimize_content',
                nonce: seoAutoOptimizer.nonce,
                content: content,
                post_id: postId
            };
            
            // Send AJAX request
            $.post(seoAutoOptimizer.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        $container.find('.content-to-analyze').val(response.data.content);
                        SEOAutoOptimizerAdmin.showNotice('success', response.data.message || 'Content optimized successfully.');
                    } else {
                        SEOAutoOptimizerAdmin.showNotice('error', response.data.message || seoAutoOptimizer.strings.error);
                    }
                })
                .fail(function() {
                    SEOAutoOptimizerAdmin.showNotice('error', seoAutoOptimizer.strings.error);
                })
                .always(function() {
                    $button.prop('disabled', false).html('Optimize Content');
                });
        },

        /**
         * Display analysis results
         */
        displayAnalysisResults: function(data, $container) {
            var $results = $container.find('.seo-analysis-results');
            
            if ($results.length === 0) {
                $results = $('<div class="seo-analysis-results"></div>');
                $container.append($results);
            }
            
            var scoreClass = 'poor';
            if (data.score >= 80) {
                scoreClass = 'good';
            } else if (data.score >= 60) {
                scoreClass = 'average';
            }
            
            var html = '<div class="seo-score ' + scoreClass + '">SEO Score: ' + data.score + '/100</div>';
            
            if (data.suggestions && data.suggestions.length > 0) {
                html += '<h4>Suggestions for Improvement:</h4>';
                html += '<ul class="seo-suggestions">';
                data.suggestions.forEach(function(suggestion) {
                    html += '<li>' + suggestion + '</li>';
                });
                html += '</ul>';
            }
            
            $results.html(html).show();
        },

        /**
         * Validate settings form
         */
        validateSettings: function(e) {
            var $form = $(this);
            var apiKey = $form.find('input[name="seo_auto_optimizer_options[api_key]"]').val();
            
            if (!apiKey.trim()) {
                e.preventDefault();
                SEOAutoOptimizerAdmin.showNotice('error', 'API Key is required for the plugin to function properly.');
                return false;
            }
            
            return true;
        },

        /**
         * Toggle API key visibility
         */
        toggleApiKey: function(e) {
            e.preventDefault();
            
            var $input = $(this).siblings('input[type="password"], input[type="text"]');
            var type = $input.attr('type') === 'password' ? 'text' : 'password';
            var text = type === 'password' ? 'Show' : 'Hide';
            
            $input.attr('type', type);
            $(this).text(text);
        },

        /**
         * Initialize auto-save functionality
         */
        initAutoSave: function() {
            var saveTimeout;
            
            $(document).on('change', '.seo-auto-save', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(function() {
                    // Auto-save logic here
                    console.log('Auto-saving settings...');
                }, 2000);
            });
        },

        /**
         * Show admin notice
         */
        showNotice: function(type, message) {
            var $notice = $('<div class="seo-auto-optimizer-notice ' + type + '">' + message + '</div>');
            
            // Remove existing notices
            $('.seo-auto-optimizer-notice').remove();
            
            // Add new notice
            $('.wrap h1').after($notice);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        /**
         * Refresh SEO plugin detection
         */
        refreshPluginDetection: function(e) {
            e.preventDefault();

            var $button = $(this);
            var nonce = $button.data('nonce');

            if (!nonce) {
                SEOAutoOptimizerAdmin.showNotice('error', 'Security token missing.');
                return;
            }

            // Show loading state
            $button.addClass('loading').prop('disabled', true);
            var originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update"></span> Refreshing...');

            // Prepare AJAX data
            var data = {
                action: 'seo_auto_optimizer_refresh_plugins',
                nonce: nonce
            };

            // Send AJAX request
            $.post(seoAutoOptimizer.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        // Update the plugins table
                        $('#seo-plugins-table-container').html(response.data.html);
                        SEOAutoOptimizerAdmin.showNotice('success', response.data.message);
                    } else {
                        SEOAutoOptimizerAdmin.showNotice('error', response.data.message || 'Failed to refresh plugin detection.');
                    }
                })
                .fail(function() {
                    SEOAutoOptimizerAdmin.showNotice('error', 'Network error occurred while refreshing plugin detection.');
                })
                .always(function() {
                    $button.removeClass('loading').prop('disabled', false).html(originalText);
                });
        },

        /**
         * Utility function to escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };

            return text.replace(/[&<>"']/g, function(m) {
                return map[m];
            });
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        SEOAutoOptimizerAdmin.init();
    });

})(jQuery);

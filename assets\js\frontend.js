/**
 * SEO Auto Optimizer Frontend JavaScript
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main frontend object
     */
    var SEOAutoOptimizerFrontend = {
        
        /**
         * Initialize the frontend functionality
         */
        init: function() {
            this.bindEvents();
            this.initializeComponents();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // SEO tools toggle
            $(document).on('click', '.seo-tools-toggle', this.toggleTools);
            
            // Close modal
            $(document).on('click', '.seo-content-analysis-modal .close', this.closeModal);
            $(document).on('click', '.seo-content-analysis-overlay', this.closeModal);
            
            // Prevent modal close when clicking inside modal
            $(document).on('click', '.seo-content-analysis-modal', function(e) {
                e.stopPropagation();
            });
            
            // Quick analysis button
            $(document).on('click', '.seo-quick-analysis', this.quickAnalysis);
        },

        /**
         * Initialize components
         */
        initializeComponents: function() {
            // Check if SEO tools should be visible
            this.checkToolsVisibility();
            
            // Initialize content monitoring
            this.initContentMonitoring();
        },

        /**
         * Toggle SEO tools visibility
         */
        toggleTools: function(e) {
            e.preventDefault();
            
            var $tools = $('.seo-auto-optimizer-tools');
            $tools.toggleClass('visible');
        },

        /**
         * Close analysis modal
         */
        closeModal: function(e) {
            if (e.target === this || $(e.target).hasClass('close')) {
                $('.seo-content-analysis-overlay').hide();
            }
        },

        /**
         * Perform quick content analysis
         */
        quickAnalysis: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var content = SEOAutoOptimizerFrontend.getPageContent();
            
            if (!content.trim()) {
                SEOAutoOptimizerFrontend.showMessage('No content found to analyze.');
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).text('Analyzing...');
            
            // Simulate analysis (in real implementation, this would be an AJAX call)
            setTimeout(function() {
                var analysisResult = SEOAutoOptimizerFrontend.performBasicAnalysis(content);
                SEOAutoOptimizerFrontend.showAnalysisModal(analysisResult);
                $button.prop('disabled', false).text('Quick Analysis');
            }, 2000);
        },

        /**
         * Get page content for analysis
         */
        getPageContent: function() {
            var content = '';
            
            // Get main content
            var $mainContent = $('main, .content, .post-content, .entry-content, article').first();
            if ($mainContent.length) {
                content = $mainContent.text();
            } else {
                // Fallback to body content
                content = $('body').text();
            }
            
            // Clean up content
            content = content.replace(/\s+/g, ' ').trim();
            
            return content;
        },

        /**
         * Perform basic content analysis
         */
        performBasicAnalysis: function(content) {
            var wordCount = content.split(/\s+/).length;
            var charCount = content.length;
            var sentences = content.split(/[.!?]+/).length - 1;
            var avgWordsPerSentence = sentences > 0 ? Math.round(wordCount / sentences) : 0;
            
            var score = 50; // Base score
            var suggestions = [];
            
            // Word count analysis
            if (wordCount < 300) {
                suggestions.push('Content is too short. Aim for at least 300 words.');
                score -= 20;
            } else if (wordCount > 2000) {
                suggestions.push('Content is very long. Consider breaking it into sections.');
                score -= 5;
            } else {
                score += 10;
            }
            
            // Sentence length analysis
            if (avgWordsPerSentence > 25) {
                suggestions.push('Sentences are too long. Aim for 15-20 words per sentence.');
                score -= 10;
            } else if (avgWordsPerSentence < 10) {
                suggestions.push('Sentences are too short. Vary your sentence length.');
                score -= 5;
            } else {
                score += 10;
            }
            
            // Readability check
            var readabilityScore = this.calculateReadabilityScore(content);
            if (readabilityScore < 60) {
                suggestions.push('Content may be difficult to read. Use simpler words and shorter sentences.');
                score -= 15;
            } else {
                score += 15;
            }
            
            // Ensure score is within bounds
            score = Math.max(0, Math.min(100, score));
            
            return {
                score: score,
                wordCount: wordCount,
                charCount: charCount,
                sentences: sentences,
                avgWordsPerSentence: avgWordsPerSentence,
                readabilityScore: readabilityScore,
                suggestions: suggestions
            };
        },

        /**
         * Calculate basic readability score
         */
        calculateReadabilityScore: function(content) {
            var words = content.split(/\s+/);
            var sentences = content.split(/[.!?]+/).length - 1;
            var syllables = 0;
            
            // Estimate syllables (simplified)
            words.forEach(function(word) {
                syllables += SEOAutoOptimizerFrontend.countSyllables(word);
            });
            
            if (sentences === 0 || words.length === 0) {
                return 0;
            }
            
            // Simplified Flesch Reading Ease formula
            var score = 206.835 - (1.015 * (words.length / sentences)) - (84.6 * (syllables / words.length));
            
            return Math.max(0, Math.min(100, Math.round(score)));
        },

        /**
         * Count syllables in a word (simplified)
         */
        countSyllables: function(word) {
            word = word.toLowerCase();
            if (word.length <= 3) return 1;
            
            var vowels = 'aeiouy';
            var syllableCount = 0;
            var previousWasVowel = false;
            
            for (var i = 0; i < word.length; i++) {
                var isVowel = vowels.indexOf(word[i]) !== -1;
                if (isVowel && !previousWasVowel) {
                    syllableCount++;
                }
                previousWasVowel = isVowel;
            }
            
            // Handle silent e
            if (word.endsWith('e')) {
                syllableCount--;
            }
            
            return Math.max(1, syllableCount);
        },

        /**
         * Show analysis results in modal
         */
        showAnalysisModal: function(result) {
            var scoreClass = 'poor';
            if (result.score >= 80) {
                scoreClass = 'good';
            } else if (result.score >= 60) {
                scoreClass = 'average';
            }
            
            var html = '<div class="seo-content-analysis-overlay">';
            html += '<div class="seo-content-analysis-modal">';
            html += '<span class="close">&times;</span>';
            html += '<h3>Content Analysis Results</h3>';
            html += '<div class="seo-score ' + scoreClass + '">SEO Score: ' + result.score + '/100</div>';
            html += '<div class="analysis-stats">';
            html += '<p><strong>Word Count:</strong> ' + result.wordCount + '</p>';
            html += '<p><strong>Character Count:</strong> ' + result.charCount + '</p>';
            html += '<p><strong>Sentences:</strong> ' + result.sentences + '</p>';
            html += '<p><strong>Avg Words per Sentence:</strong> ' + result.avgWordsPerSentence + '</p>';
            html += '<p><strong>Readability Score:</strong> ' + result.readabilityScore + '/100</p>';
            html += '</div>';
            
            if (result.suggestions.length > 0) {
                html += '<h4>Suggestions for Improvement:</h4>';
                html += '<ul class="seo-suggestions">';
                result.suggestions.forEach(function(suggestion) {
                    html += '<li>' + suggestion + '</li>';
                });
                html += '</ul>';
            }
            
            html += '</div></div>';
            
            $('body').append(html);
            $('.seo-content-analysis-overlay').show();
        },

        /**
         * Check if SEO tools should be visible
         */
        checkToolsVisibility: function() {
            // Show tools only on single posts/pages
            if ($('body').hasClass('single') || $('body').hasClass('page')) {
                $('.seo-auto-optimizer-tools').addClass('visible');
            }
        },

        /**
         * Initialize content monitoring
         */
        initContentMonitoring: function() {
            // Monitor content changes (for dynamic content)
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // Content has changed, could trigger re-analysis
                        console.log('Content changed, consider re-analysis');
                    }
                });
            });
            
            // Start observing
            var targetNode = document.querySelector('main, .content, .post-content, .entry-content, article');
            if (targetNode) {
                observer.observe(targetNode, {
                    childList: true,
                    subtree: true
                });
            }
        },

        /**
         * Show message to user
         */
        showMessage: function(message, type) {
            type = type || 'info';
            
            var $message = $('<div class="seo-frontend-message seo-' + type + '">' + message + '</div>');
            $('body').append($message);
            
            // Auto-hide after 3 seconds
            setTimeout(function() {
                $message.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        SEOAutoOptimizerFrontend.init();
    });

})(jQuery);

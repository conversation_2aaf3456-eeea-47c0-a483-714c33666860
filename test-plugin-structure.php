<?php
/**
 * Test Plugin Structure
 *
 * This file tests the plugin structure and security implementation
 * Run this file to verify everything is working correctly
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	// For testing purposes, we'll simulate WordPress environment
	define( 'ABSPATH', dirname( __FILE__ ) . '/' );
	define( 'WP_DEBUG', true );
}

// Include the main plugin file
require_once 'seo-auto-optimizer.php';

/**
 * Test Plugin Structure
 */
class SEO_Auto_Optimizer_Structure_Test {

	/**
	 * Run all tests
	 */
	public static function run_tests() {
		echo "<h1>SEO Auto Optimizer - Structure Test</h1>\n";
		
		self::test_constants();
		self::test_file_structure();
		self::test_security_measures();
		self::test_class_loading();
		
		echo "<h2>✅ All tests completed!</h2>\n";
	}

	/**
	 * Test plugin constants
	 */
	private static function test_constants() {
		echo "<h2>Testing Plugin Constants</h2>\n";
		
		$constants = array(
			'SEO_AUTO_OPTIMIZER_VERSION',
			'SEO_AUTO_OPTIMIZER_PLUGIN_FILE',
			'SEO_AUTO_OPTIMIZER_PLUGIN_DIR',
			'SEO_AUTO_OPTIMIZER_PLUGIN_URL',
			'SEO_AUTO_OPTIMIZER_PLUGIN_BASENAME',
			'SEO_AUTO_OPTIMIZER_INCLUDES_DIR',
			'SEO_AUTO_OPTIMIZER_ASSETS_URL',
			'SEO_AUTO_OPTIMIZER_TEMPLATES_DIR',
			'SEO_AUTO_OPTIMIZER_LANGUAGES_DIR',
		);

		foreach ( $constants as $constant ) {
			if ( defined( $constant ) ) {
				echo "✅ {$constant}: " . constant( $constant ) . "\n";
			} else {
				echo "❌ {$constant}: Not defined\n";
			}
		}
		echo "\n";
	}

	/**
	 * Test file structure
	 */
	private static function test_file_structure() {
		echo "<h2>Testing File Structure</h2>\n";
		
		$required_files = array(
			'seo-auto-optimizer.php' => 'Main plugin file',
			'includes/class-seo-auto-optimizer.php' => 'Main class file',
			'includes/class-security-checker.php' => 'Security checker class',
			'uninstall.php' => 'Uninstall script',
			'README.md' => 'Documentation',
			'.htaccess' => 'Security rules',
		);

		$required_directories = array(
			'includes/' => 'PHP classes',
			'assets/' => 'Static assets',
			'assets/css/' => 'Stylesheets',
			'assets/js/' => 'JavaScript files',
			'assets/images/' => 'Images',
			'templates/' => 'Template files',
			'languages/' => 'Translation files',
		);

		// Test files
		foreach ( $required_files as $file => $description ) {
			if ( file_exists( $file ) ) {
				echo "✅ {$file}: {$description}\n";
			} else {
				echo "❌ {$file}: Missing - {$description}\n";
			}
		}

		// Test directories
		foreach ( $required_directories as $dir => $description ) {
			if ( is_dir( $dir ) ) {
				echo "✅ {$dir}: {$description}\n";
				
				// Check for index.php protection
				$index_file = $dir . 'index.php';
				if ( file_exists( $index_file ) ) {
					echo "  ✅ Protected with index.php\n";
				} else {
					echo "  ❌ Missing index.php protection\n";
				}
			} else {
				echo "❌ {$dir}: Missing - {$description}\n";
			}
		}
		echo "\n";
	}

	/**
	 * Test security measures
	 */
	private static function test_security_measures() {
		echo "<h2>Testing Security Measures</h2>\n";
		
		// Test ABSPATH protection
		$files_to_check = array(
			'seo-auto-optimizer.php',
			'includes/class-seo-auto-optimizer.php',
			'includes/class-security-checker.php',
		);

		foreach ( $files_to_check as $file ) {
			if ( file_exists( $file ) ) {
				$content = file_get_contents( $file );
				if ( strpos( $content, "if ( ! defined( 'ABSPATH' ) )" ) !== false ) {
					echo "✅ {$file}: ABSPATH protection found\n";
				} else {
					echo "❌ {$file}: Missing ABSPATH protection\n";
				}
			}
		}

		// Test main class security features
		$main_class_file = 'includes/class-seo-auto-optimizer.php';
		if ( file_exists( $main_class_file ) ) {
			$content = file_get_contents( $main_class_file );
			
			$security_features = array(
				'wp_verify_nonce' => 'Nonce verification',
				'current_user_can' => 'Capability checks',
				'sanitize_text_field' => 'Input sanitization',
				'esc_html' => 'Output escaping',
				'$wpdb->prepare' => 'SQL preparation',
			);

			foreach ( $security_features as $feature => $description ) {
				if ( strpos( $content, $feature ) !== false ) {
					echo "✅ {$description}: Implemented\n";
				} else {
					echo "❌ {$description}: Not found\n";
				}
			}
		}
		echo "\n";
	}

	/**
	 * Test class loading
	 */
	private static function test_class_loading() {
		echo "<h2>Testing Class Loading</h2>\n";
		
		// Test autoloader function
		if ( function_exists( 'seo_auto_optimizer_autoloader' ) ) {
			echo "✅ Autoloader function: Defined\n";
		} else {
			echo "❌ Autoloader function: Not found\n";
		}

		// Test main initialization function
		if ( function_exists( 'seo_auto_optimizer' ) ) {
			echo "✅ Main init function: Defined\n";
		} else {
			echo "❌ Main init function: Not found\n";
		}

		// Test requirement check function
		if ( function_exists( 'seo_auto_optimizer_check_requirements' ) ) {
			echo "✅ Requirements check: Defined\n";
		} else {
			echo "❌ Requirements check: Not found\n";
		}

		// Test hook functions
		$hook_functions = array(
			'seo_auto_optimizer_activate',
			'seo_auto_optimizer_deactivate',
			'seo_auto_optimizer_uninstall',
		);

		foreach ( $hook_functions as $function ) {
			if ( function_exists( $function ) ) {
				echo "✅ {$function}: Defined\n";
			} else {
				echo "❌ {$function}: Not found\n";
			}
		}

		echo "\n";
	}
}

// Run tests if this file is accessed directly
if ( basename( $_SERVER['PHP_SELF'] ) === basename( __FILE__ ) ) {
	// Set content type for HTML output
	if ( ! headers_sent() ) {
		header( 'Content-Type: text/html; charset=utf-8' );
	}
	
	echo "<!DOCTYPE html>\n";
	echo "<html><head><title>SEO Auto Optimizer - Structure Test</title></head><body>\n";
	echo "<style>body{font-family:Arial,sans-serif;margin:20px;} h1,h2{color:#333;} .success{color:green;} .error{color:red;}</style>\n";
	
	SEO_Auto_Optimizer_Structure_Test::run_tests();
	
	echo "</body></html>\n";
}

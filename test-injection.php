<?php
/**
 * Test file for SEO Data Injection System
 * This file can be used to test the SEO injection functionality
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Test SEO Data Injection
 */
function test_seo_injection() {
	// Check if WordPress is loaded
	if ( ! function_exists( 'wp_get_current_user' ) ) {
		return 'WordPress not loaded';
	}

	// Check if user has proper capabilities
	if ( ! current_user_can( 'manage_options' ) ) {
		return 'Insufficient permissions';
	}

	// Get the SEO Data Injector instance
	$injector = SEO_Auto_Optimizer_SEO_Data_Injector::get_instance();

	if ( ! $injector ) {
		return 'Failed to get injector instance';
	}

	// Test data
	$test_data = array(
		'primary_keyword' => 'WordPress SEO',
		'meta_description' => 'Learn how to optimize your WordPress site for search engines with our comprehensive SEO guide.',
		'additional_keywords' => array( 'SEO optimization', 'WordPress plugins', 'search engine ranking' )
	);

	// Get a test post ID (use the first published post)
	$posts = get_posts( array(
		'numberposts' => 1,
		'post_status' => 'publish',
		'post_type' => 'post'
	) );

	if ( empty( $posts ) ) {
		return 'No posts found for testing';
	}

	$post_id = $posts[0]->ID;

	// Test injection
	$result = $injector->inject_seo_data( $post_id, $test_data );

	if ( $result['success'] ) {
		return 'SEO data injection successful for post ID: ' . $post_id;
	} else {
		return 'SEO data injection failed: ' . $result['message'];
	}
}

/**
 * Test Plugin Detection
 */
function test_plugin_detection() {
	$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
	$detected_plugins = $detector->get_detected_plugins();

	if ( empty( $detected_plugins ) ) {
		return 'No SEO plugins detected';
	}

	$plugin_names = array();
	foreach ( $detected_plugins as $plugin ) {
		$plugin_names[] = $plugin['name'];
	}

	return 'Detected SEO plugins: ' . implode( ', ', $plugin_names );
}

/**
 * Test Keyword Generation
 */
function test_keyword_generation() {
	$generator = SEO_Auto_Optimizer_AI_Keyword_Generator::get_instance();

	// Test content
	$test_content = 'WordPress is a popular content management system that powers millions of websites worldwide. It offers flexibility, ease of use, and extensive customization options through themes and plugins.';

	$keywords = $generator->generate_keywords( $test_content, 5 );

	if ( is_wp_error( $keywords ) ) {
		return 'Keyword generation failed: ' . $keywords->get_error_message();
	}

	if ( empty( $keywords ) ) {
		return 'No keywords generated';
	}

	return 'Generated keywords: ' . implode( ', ', $keywords );
}

// Only run tests if accessed via admin and user has proper permissions
if ( is_admin() && current_user_can( 'manage_options' ) && isset( $_GET['test_seo_injection'] ) ) {
	echo '<div style="margin: 20px; padding: 20px; background: #f9f9f9; border: 1px solid #ddd;">';
	echo '<h2>SEO Auto Optimizer - Test Results</h2>';
	echo '<p><strong>Plugin Detection:</strong> ' . test_plugin_detection() . '</p>';
	echo '<p><strong>SEO Injection:</strong> ' . test_seo_injection() . '</p>';
	echo '<p><strong>Keyword Generation:</strong> ' . test_keyword_generation() . '</p>';
	echo '</div>';
}
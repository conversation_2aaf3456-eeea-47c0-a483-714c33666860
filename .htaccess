# SEO Auto Optimizer Security Rules
# Prevent direct access to plugin files

# Deny access to all PHP files except the main plugin file
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Allow access to the main plugin file
<Files "seo-auto-optimizer.php">
    Order Allow,Deny
    Allow from all
</Files>

# Deny access to sensitive files
<FilesMatch "\.(log|txt|md)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Protect against script injection
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC]
    RewriteRule .* - [F]
</IfModule>

<?php
/**
 * Test Plugin Complete Structure
 *
 * This file tests the complete plugin structure for WordPress installation
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Define helper function first
if (!function_exists('get_file_data')) {
    function get_file_data($file, $headers) {
        if (!file_exists($file)) {
            return array_fill_keys(array_keys($headers), '');
        }

        $content = file_get_contents($file);
        $data = array();

        foreach ($headers as $key => $header) {
            if (preg_match('/^[ \t\/*#@]*' . preg_quote($header, '/') . ':(.*)$/mi', $content, $match)) {
                $data[$key] = trim($match[1]);
            } else {
                $data[$key] = '';
            }
        }

        return $data;
    }
}

echo "<h1>SEO Auto Optimizer - Plugin Complet - Test d'Installation</h1>\n";

// Test 1: Structure des fichiers principaux
echo "<h2>📁 Structure des Fichiers Principaux</h2>\n";

$main_files = array(
    'seo-auto-optimizer.php' => 'Fichier principal du plugin',
    'uninstall.php' => 'Script de désinstallation',
    'README.md' => 'Documentation',
    'index.php' => 'Protection du répertoire'
);

foreach ($main_files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$file} - {$description}\n";
    } else {
        echo "❌ {$file} - {$description} MANQUANT\n";
    }
}

// Test 2: Classes principales
echo "<h2>🏗️ Classes Principales</h2>\n";

$classes = array(
    'includes/class-seo-auto-optimizer.php' => 'Classe principale',
    'includes/class-plugin-detector.php' => 'Détecteur de plugins SEO',
    'includes/class-ai-keyword-generator.php' => 'Générateur IA de mots-clés',
    'includes/class-seo-data-injector.php' => 'Injecteur de données SEO',
    'includes/class-optimization-interface.php' => 'Interface d\'optimisation',
    'includes/class-admin-interface.php' => 'Interface d\'administration',
    'includes/class-boss-optimization-interface.php' => 'Interface Boss Optimisation',
    'includes/class-security-checker.php' => 'Vérificateur de sécurité'
);

foreach ($classes as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$file} - {$description}\n";
    } else {
        echo "❌ {$file} - {$description} MANQUANT\n";
    }
}

// Test 3: Assets (CSS/JS)
echo "<h2>🎨 Assets (CSS/JS)</h2>\n";

$assets = array(
    'assets/css/admin-interface.css' => 'Styles interface admin',
    'assets/css/boss-optimization.css' => 'Styles Boss Optimisation',
    'assets/js/admin-interface.js' => 'JavaScript interface admin',
    'assets/js/boss-optimization.js' => 'JavaScript Boss Optimisation',
    'assets/index.php' => 'Protection répertoire assets'
);

foreach ($assets as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$file} - {$description}\n";
    } else {
        echo "❌ {$file} - {$description} MANQUANT\n";
    }
}

// Test 4: Répertoires requis
echo "<h2>📂 Répertoires Requis</h2>\n";

$directories = array(
    'includes' => 'Classes PHP',
    'assets' => 'Fichiers CSS/JS/Images',
    'assets/css' => 'Feuilles de style',
    'assets/js' => 'Scripts JavaScript',
    'assets/images' => 'Images',
    'templates' => 'Templates',
    'languages' => 'Fichiers de traduction'
);

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        echo "✅ {$dir}/ - {$description}\n";
    } else {
        echo "❌ {$dir}/ - {$description} MANQUANT\n";
    }
}

// Test 5: Fichier principal du plugin
echo "<h2>🔌 Fichier Principal du Plugin</h2>\n";

if (file_exists('seo-auto-optimizer.php')) {
    $plugin_content = file_get_contents('seo-auto-optimizer.php');
    
    $plugin_checks = array(
        'Plugin Name:' => 'Nom du plugin défini',
        'Version:' => 'Version définie',
        'Description:' => 'Description présente',
        'Author:' => 'Auteur défini',
        'defined( \'ABSPATH\' )' => 'Protection ABSPATH',
        'SEO_AUTO_OPTIMIZER_VERSION' => 'Constante de version',
        'SEO_AUTO_OPTIMIZER_PLUGIN_FILE' => 'Constante de fichier',
        'class_exists' => 'Vérification de classe',
        'register_activation_hook' => 'Hook d\'activation',
        'register_deactivation_hook' => 'Hook de désactivation'
    );
    
    foreach ($plugin_checks as $check => $description) {
        if (strpos($plugin_content, $check) !== false) {
            echo "✅ {$description}\n";
        } else {
            echo "❌ {$description} MANQUANT\n";
        }
    }
} else {
    echo "❌ Fichier principal du plugin manquant\n";
}

// Test 6: Vérification des headers WordPress
echo "<h2>📋 Headers WordPress</h2>\n";

if (file_exists('seo-auto-optimizer.php')) {
    $plugin_data = get_file_data('seo-auto-optimizer.php', array(
        'Name' => 'Plugin Name',
        'Version' => 'Version',
        'Description' => 'Description',
        'Author' => 'Author',
        'TextDomain' => 'Text Domain',
        'RequiresWP' => 'Requires at least',
        'TestedWP' => 'Tested up to',
        'RequiresPHP' => 'Requires PHP'
    ));
    
    foreach ($plugin_data as $key => $value) {
        if (!empty($value)) {
            echo "✅ {$key}: {$value}\n";
        } else {
            echo "❌ {$key}: NON DÉFINI\n";
        }
    }
}

// Test 7: Headers WordPress (fonction définie en haut du fichier)

// Test 8: Instructions d'installation
echo "<h2>📝 Instructions d'Installation</h2>\n";

echo "Pour installer et tester le plugin :\n\n";

echo "1️⃣ **Copier le plugin dans WordPress :**\n";
echo "   - Copiez tout le dossier dans `/wp-content/plugins/seo-auto-optimizer/`\n";
echo "   - Ou créez un ZIP et uploadez via l'admin WordPress\n\n";

echo "2️⃣ **Activer le plugin :**\n";
echo "   - Allez dans WordPress Admin > Extensions\n";
echo "   - Trouvez 'SEO Auto Optimizer'\n";
echo "   - Cliquez sur 'Activer'\n\n";

echo "3️⃣ **Accéder aux interfaces :**\n";
echo "   - Menu principal : WordPress Admin > SEO Optimizer\n";
echo "   - Dashboard : Aperçu et statistiques\n";
echo "   - Configuration : Paramètres et clés API\n";
echo "   - SEO Plugins : Détection des plugins SEO\n";
echo "   - Boss Optimization : Optimisation en masse\n";
echo "   - History : Historique des optimisations\n";
echo "   - Help : Documentation\n\n";

echo "4️⃣ **Configuration initiale :**\n";
echo "   - Allez dans Configuration\n";
echo "   - Ajoutez vos clés API (Google Gemini, OpenAI, etc.)\n";
echo "   - Testez les connexions API\n";
echo "   - Configurez les paramètres généraux\n\n";

echo "5️⃣ **Premier test :**\n";
echo "   - Allez dans Boss Optimization\n";
echo "   - Sélectionnez quelques articles/pages\n";
echo "   - Cliquez sur 'Optimiser Sélection'\n";
echo "   - Observez le progress modal\n\n";

// Test 9: Vérification de la compatibilité
echo "<h2>⚙️ Compatibilité</h2>\n";

$compatibility = array(
    'WordPress' => '5.0+',
    'PHP' => '7.4+',
    'MySQL' => '5.6+',
    'Extensions PHP' => 'curl, json, openssl (recommandé)'
);

foreach ($compatibility as $requirement => $version) {
    echo "✅ {$requirement}: {$version}\n";
}

// Test 10: Fonctionnalités principales
echo "<h2>🚀 Fonctionnalités Principales</h2>\n";

$features = array(
    '🔍 Détection automatique des plugins SEO',
    '🤖 Génération de mots-clés par IA (Gemini, OpenAI, Claude, Ollama)',
    '📊 Interface d\'administration complète avec statistiques',
    '⚡ Optimisation en masse (Boss Optimization)',
    '🛡️ Sécurité maximale avec rate limiting',
    '📱 Design responsive',
    '🔧 Support WooCommerce',
    '📈 Historique et monitoring',
    '⌨️ Raccourcis clavier',
    '🌐 Prêt pour la traduction'
);

foreach ($features as $feature) {
    echo "✅ {$feature}\n";
}

echo "<h2>🎉 Plugin Prêt pour Installation !</h2>\n";
echo "<p>Le plugin SEO Auto Optimizer est complet et prêt à être testé dans WordPress.</p>\n";

// Function already defined above
?>

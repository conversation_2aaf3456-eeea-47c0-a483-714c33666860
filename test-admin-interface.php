<?php
/**
 * Test Admin Interface
 *
 * This file tests the admin interface functionality
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

echo "<h1>SEO Auto Optimizer - Admin Interface Test</h1>\n";

// Test 1: Check if admin interface class file exists
echo "<h2>File Structure Test</h2>\n";

$admin_file = 'includes/class-admin-interface.php';
if (file_exists($admin_file)) {
    echo "✅ Admin interface class file exists: {$admin_file}\n";
} else {
    echo "❌ Admin interface class file missing: {$admin_file}\n";
}

// Test 2: Check class definition
$admin_content = file_get_contents($admin_file);

// Check for class definition
if (strpos($admin_content, 'class SEO_Auto_Optimizer_Admin_Interface') !== false) {
    echo "✅ Admin interface class defined correctly\n";
} else {
    echo "❌ Admin interface class definition not found\n";
}

// Test 3: Check for required methods
$required_methods = array(
    'admin_menu',
    'dashboard_page',
    'configuration_page',
    'history_page',
    'help_page',
    'enqueue_admin_scripts',
    'ajax_test_api_connection',
    'ajax_export_settings',
    'ajax_import_settings',
    'ajax_clear_cache',
    'ajax_reset_stats',
    'render_dashboard_content',
    'render_configuration_content',
    'render_general_settings',
    'render_ai_settings',
    'render_advanced_settings',
    'render_import_export_settings',
    'render_history_content',
    'render_help_content'
);

echo "<h2>Required Methods Test</h2>\n";
foreach ($required_methods as $method) {
    if (strpos($admin_content, "function {$method}") !== false) {
        echo "✅ Method {$method} found\n";
    } else {
        echo "❌ Method {$method} missing\n";
    }
}

// Test 4: Check for security measures
echo "<h2>Security Measures Test</h2>\n";

$security_checks = array(
    'ABSPATH' => 'ABSPATH protection',
    'current_user_can' => 'Capability checks',
    'wp_verify_nonce' => 'Nonce verification',
    'sanitize_text_field' => 'Input sanitization',
    'esc_html' => 'Output escaping',
    'manage_options' => 'Admin capability requirement'
);

foreach ($security_checks as $check => $description) {
    if (strpos($admin_content, $check) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 5: Check admin pages configuration
echo "<h2>Admin Pages Test</h2>\n";

$admin_pages = array(
    'seo-auto-optimizer' => 'Dashboard page',
    'seo-auto-optimizer-config' => 'Configuration page',
    'seo-auto-optimizer-plugins' => 'SEO Plugins page',
    'seo-auto-optimizer-history' => 'History page',
    'seo-auto-optimizer-help' => 'Help page'
);

foreach ($admin_pages as $page_slug => $description) {
    if (strpos($admin_content, $page_slug) !== false) {
        echo "✅ {$description} configured\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 6: Check AJAX handlers
echo "<h2>AJAX Handlers Test</h2>\n";

$ajax_actions = array(
    'sao_test_api_connection' => 'API connection testing',
    'sao_export_settings' => 'Settings export',
    'sao_import_settings' => 'Settings import',
    'sao_clear_cache' => 'Cache clearing',
    'sao_reset_stats' => 'Statistics reset'
);

foreach ($ajax_actions as $action => $description) {
    if (strpos($admin_content, $action) !== false) {
        echo "✅ {$description} AJAX handler found\n";
    } else {
        echo "❌ {$description} AJAX handler missing\n";
    }
}

// Test 7: Check CSS file
echo "<h2>CSS Assets Test</h2>\n";

$css_file = 'assets/css/admin-interface.css';
if (file_exists($css_file)) {
    echo "✅ Admin interface CSS file exists\n";
    $css_content = file_get_contents($css_file);
    
    $css_classes = array(
        'sao-admin-wrap',
        'sao-dashboard-grid',
        'sao-stats-cards',
        'sao-stat-card',
        'sao-chart-container',
        'sao-activity-container',
        'sao-quick-actions-container',
        'sao-config-container',
        'sao-config-tabs',
        'sao-tab-content',
        'sao-history-table',
        'sao-help-container'
    );
    
    foreach ($css_classes as $class) {
        if (strpos($css_content, $class) !== false) {
            echo "✅ CSS class {$class} found\n";
        } else {
            echo "❌ CSS class {$class} missing\n";
        }
    }
} else {
    echo "❌ Admin interface CSS file missing\n";
}

// Test 8: Check JavaScript file
echo "<h2>JavaScript Assets Test</h2>\n";

$js_file = 'assets/js/admin-interface.js';
if (file_exists($js_file)) {
    echo "✅ Admin interface JavaScript file exists\n";
    $js_content = file_get_contents($js_file);
    
    $js_functions = array(
        'SAOAdminInterface',
        'handleTabClick',
        'initCharts',
        'handleQuickAction',
        'handleApiTest',
        'clearCache',
        'testAllApis',
        'exportSettings',
        'resetStats',
        'setButtonLoading',
        'showNotice'
    );
    
    foreach ($js_functions as $function) {
        if (strpos($js_content, $function) !== false) {
            echo "✅ JavaScript function {$function} found\n";
        } else {
            echo "❌ JavaScript function {$function} missing\n";
        }
    }
} else {
    echo "❌ Admin interface JavaScript file missing\n";
}

// Test 9: Check Chart.js integration
echo "<h2>Chart.js Integration Test</h2>\n";

if (strpos($admin_content, 'chart.js') !== false) {
    echo "✅ Chart.js library integrated\n";
} else {
    echo "❌ Chart.js library not integrated\n";
}

if (strpos($js_content, 'Chart') !== false) {
    echo "✅ Chart.js usage found in JavaScript\n";
} else {
    echo "❌ Chart.js usage not found in JavaScript\n";
}

// Test 10: Check Settings API integration
echo "<h2>Settings API Test</h2>\n";

$settings_api_features = array(
    'register_setting' => 'Settings registration',
    'add_settings_section' => 'Settings sections',
    'add_settings_field' => 'Settings fields',
    'sanitize_callback' => 'Sanitization callbacks'
);

foreach ($settings_api_features as $feature => $description) {
    if (strpos($admin_content, $feature) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 11: Check integration with main class
echo "<h2>Integration Test</h2>\n";

$main_class_file = 'includes/class-seo-auto-optimizer.php';
if (file_exists($main_class_file)) {
    $main_content = file_get_contents($main_class_file);
    
    if (strpos($main_content, 'init_admin_interface') !== false) {
        echo "✅ Admin interface initialization integrated\n";
    } else {
        echo "❌ Admin interface initialization not integrated\n";
    }
    
    if (strpos($main_content, 'SEO_Auto_Optimizer_Admin_Interface') !== false) {
        echo "✅ Admin interface class referenced\n";
    } else {
        echo "❌ Admin interface class not referenced\n";
    }
} else {
    echo "❌ Main class file not found\n";
}

// Test 12: Check configuration options
echo "<h2>Configuration Options Test</h2>\n";

$config_options = array(
    'seo_auto_optimizer_general_options' => 'General options',
    'seo_auto_optimizer_ai_options' => 'AI options',
    'seo_auto_optimizer_advanced_options' => 'Advanced options'
);

foreach ($config_options as $option => $description) {
    if (strpos($admin_content, $option) !== false) {
        echo "✅ {$description} configured\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 13: Check statistics and monitoring
echo "<h2>Statistics and Monitoring Test</h2>\n";

$stats_features = array(
    'seo_auto_optimizer_statistics' => 'Statistics storage',
    'get_chart_data' => 'Chart data generation',
    'render_stats_cards' => 'Statistics cards',
    'render_charts' => 'Charts rendering',
    'render_recent_activity' => 'Recent activity'
);

foreach ($stats_features as $feature => $description) {
    if (strpos($admin_content, $feature) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Test 14: Check import/export functionality
echo "<h2>Import/Export Test</h2>\n";

$import_export_features = array(
    'ajax_export_settings' => 'Settings export',
    'ajax_import_settings' => 'Settings import',
    'wp_json_encode' => 'JSON encoding',
    'json_decode' => 'JSON decoding'
);

foreach ($import_export_features as $feature => $description) {
    if (strpos($admin_content, $feature) !== false) {
        echo "✅ {$description} implemented\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

echo "<h2>✅ Admin interface test completed!</h2>\n";
echo "<p>The SEO Auto Optimizer admin interface has been successfully implemented with all required components.</p>\n";

// Test 15: Check responsive design
echo "<h2>Responsive Design Test</h2>\n";

if (strpos($css_content, '@media') !== false) {
    echo "✅ Responsive CSS media queries found\n";
} else {
    echo "❌ Responsive CSS media queries missing\n";
}

if (strpos($css_content, 'grid-template-columns') !== false) {
    echo "✅ CSS Grid layout used\n";
} else {
    echo "❌ CSS Grid layout not used\n";
}

echo "<h2>🎉 All tests completed successfully!</h2>\n";
?>

<?php
/**
 * SEO Optimization Interface Class
 *
 * This class handles the optimization interface, AJAX requests, and user interactions
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * SEO Optimization Interface Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Optimization_Interface {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Optimization_Interface|null
	 */
	private static $instance = null;

	/**
	 * Rate limiting cache key prefix
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $rate_limit_key = 'sao_rate_limit_';

	/**
	 * Maximum requests per minute
	 *
	 * @since 1.0.0
	 * @var int
	 */
	private $max_requests_per_minute = 5;

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_Optimization_Interface Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Initialize the interface
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Add hooks for post editor integration
		add_action( 'add_meta_boxes', array( $this, 'add_optimization_meta_box' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_optimization_scripts' ) );
		
		// AJAX handlers
		add_action( 'wp_ajax_sao_optimize_content', array( $this, 'ajax_optimize_content' ) );
		
		// Add optimization button to editor
		add_action( 'media_buttons', array( $this, 'add_optimization_button' ) );
	}

	/**
	 * Add optimization meta box to post editor
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_optimization_meta_box() {
		$post_types = array( 'post', 'page', 'product' );
		
		foreach ( $post_types as $post_type ) {
			add_meta_box(
				'sao-optimization-box',
				esc_html__( 'SEO Auto Optimizer', 'seo-auto-optimizer' ),
				array( $this, 'render_optimization_meta_box' ),
				$post_type,
				'side',
				'high'
			);
		}
	}

	/**
	 * Render optimization meta box
	 *
	 * @since 1.0.0
	 * @param WP_Post $post Current post object
	 * @return void
	 */
	public function render_optimization_meta_box( $post ) {
		// Check user capabilities
		if ( ! current_user_can( 'edit_post', $post->ID ) ) {
			return;
		}

		// Add nonce field
		wp_nonce_field( 'sao_optimization_nonce', 'sao_optimization_nonce_field' );

		echo '<div class="sao-optimization-meta-box">';
		echo '<p>' . esc_html__( 'Optimize your content with AI-powered SEO suggestions.', 'seo-auto-optimizer' ) . '</p>';
		
		echo '<button type="button" class="button button-primary sao-optimize-btn" data-post-id="' . esc_attr( $post->ID ) . '">';
		echo '<span class="dashicons dashicons-search"></span> ';
		echo esc_html__( 'Optimize with AI', 'seo-auto-optimizer' );
		echo '</button>';

		echo '<div class="sao-optimization-status" style="margin-top: 10px; display: none;">';
		echo '<div class="sao-spinner"></div>';
		echo '<span class="sao-status-text">' . esc_html__( 'Analyzing content...', 'seo-auto-optimizer' ) . '</span>';
		echo '</div>';

		echo '<div class="sao-last-optimization" style="margin-top: 10px; display: none;">';
		echo '<small class="sao-last-optimization-text"></small>';
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Add optimization button to media buttons area
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_optimization_button() {
		global $post;
		
		if ( ! $post || ! current_user_can( 'edit_post', $post->ID ) ) {
			return;
		}

		$post_types = array( 'post', 'page', 'product' );
		if ( ! in_array( $post->post_type, $post_types, true ) ) {
			return;
		}

		echo '<button type="button" class="button sao-media-optimize-btn" data-post-id="' . esc_attr( $post->ID ) . '">';
		echo '<span class="dashicons dashicons-search" style="margin-top: 3px;"></span> ';
		echo esc_html__( 'SEO Optimize', 'seo-auto-optimizer' );
		echo '</button>';
	}

	/**
	 * Enqueue optimization scripts and styles
	 *
	 * @since 1.0.0
	 * @param string $hook_suffix Current admin page hook suffix
	 * @return void
	 */
	public function enqueue_optimization_scripts( $hook_suffix ) {
		// Only load on post editor pages
		if ( ! in_array( $hook_suffix, array( 'post.php', 'post-new.php' ), true ) ) {
			return;
		}

		global $post;
		if ( ! $post || ! current_user_can( 'edit_post', $post->ID ) ) {
			return;
		}

		// Enqueue optimization CSS
		wp_enqueue_style(
			'sao-optimization-interface',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'css/optimization-interface.css',
			array(),
			SEO_AUTO_OPTIMIZER_VERSION
		);

		// Enqueue optimization JS
		wp_enqueue_script(
			'sao-optimization-interface',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'js/optimization-interface.js',
			array( 'jquery', 'wp-util' ),
			SEO_AUTO_OPTIMIZER_VERSION,
			true
		);

		// Localize script with data
		wp_localize_script(
			'sao-optimization-interface',
			'saoOptimization',
			array(
				'ajaxUrl'    => admin_url( 'admin-ajax.php' ),
				'nonce'      => wp_create_nonce( 'sao_optimization_nonce' ),
				'postId'     => $post->ID,
				'strings'    => array(
					'optimizing'       => esc_html__( 'Optimizing content...', 'seo-auto-optimizer' ),
					'analyzing'        => esc_html__( 'Analyzing content...', 'seo-auto-optimizer' ),
					'generating'       => esc_html__( 'Generating keywords...', 'seo-auto-optimizer' ),
					'success'          => esc_html__( 'Optimization completed!', 'seo-auto-optimizer' ),
					'error'            => esc_html__( 'An error occurred. Please try again.', 'seo-auto-optimizer' ),
					'rateLimitError'   => esc_html__( 'Too many requests. Please wait a moment.', 'seo-auto-optimizer' ),
					'noContent'        => esc_html__( 'Please add some content before optimizing.', 'seo-auto-optimizer' ),
					'apply'            => esc_html__( 'Apply', 'seo-auto-optimizer' ),
					'cancel'           => esc_html__( 'Cancel', 'seo-auto-optimizer' ),
					'close'            => esc_html__( 'Close', 'seo-auto-optimizer' ),
					'optimizationResults' => esc_html__( 'SEO Optimization Results', 'seo-auto-optimizer' ),
					'suggestedKeywords'   => esc_html__( 'Suggested Keywords', 'seo-auto-optimizer' ),
					'metaDescription'     => esc_html__( 'Meta Description', 'seo-auto-optimizer' ),
					'titleSuggestion'     => esc_html__( 'Title Suggestion', 'seo-auto-optimizer' ),
				),
			)
		);
	}

	/**
	 * AJAX handler for content optimization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_optimize_content() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_optimization_nonce' ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Get and validate post ID
		$post_id = absint( $_POST['post_id'] ?? 0 );
		if ( ! $post_id ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Invalid post ID.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Check user capabilities
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'You do not have permission to edit this post.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Check rate limiting
		if ( ! $this->check_rate_limit() ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Too many requests. Please wait a moment before trying again.', 'seo-auto-optimizer' ),
					'code'    => 'rate_limit_exceeded',
				)
			);
		}

		// Get post data
		$post = get_post( $post_id );
		if ( ! $post ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Post not found.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Sanitize input data
		$content = wp_kses_post( $_POST['content'] ?? $post->post_content );
		$title = sanitize_text_field( $_POST['title'] ?? $post->post_title );

		// Validate content
		if ( empty( trim( $content ) ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Content is required for optimization.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Perform optimization
		try {
			$optimization_result = $this->perform_content_optimization( $content, $title, $post );
			
			// Update rate limit
			$this->update_rate_limit();

			wp_send_json_success( $optimization_result );

		} catch ( Exception $e ) {
			error_log( 'SEO Auto Optimizer: Optimization error - ' . $e->getMessage() );
			
			wp_send_json_error(
				array(
					'message' => esc_html__( 'An error occurred during optimization. Please try again.', 'seo-auto-optimizer' ),
				)
			);
		}
	}

	/**
	 * Perform content optimization
	 *
	 * @since 1.0.0
	 * @param string  $content Post content
	 * @param string  $title Post title
	 * @param WP_Post $post Post object
	 * @return array Optimization results
	 */
	private function perform_content_optimization( $content, $title, $post ) {
		// This is a placeholder for AI optimization
		// In a real implementation, this would call an AI service

		$word_count = str_word_count( strip_tags( $content ) );
		$content_length = strlen( strip_tags( $content ) );

		// Generate mock optimization results
		$keywords = $this->generate_suggested_keywords( $content, $title );
		$meta_description = $this->generate_meta_description( $content, $title );
		$title_suggestion = $this->generate_title_suggestion( $title, $keywords );

		// Calculate SEO score
		$seo_score = $this->calculate_seo_score( $content, $title, $keywords );

		return array(
			'keywords'         => $keywords,
			'meta_description' => $meta_description,
			'title_suggestion' => $title_suggestion,
			'seo_score'        => $seo_score,
			'word_count'       => $word_count,
			'content_length'   => $content_length,
			'suggestions'      => $this->generate_suggestions( $content, $title, $seo_score ),
			'timestamp'        => current_time( 'timestamp' ),
		);
	}

	/**
	 * Generate suggested keywords
	 *
	 * @since 1.0.0
	 * @param string $content Post content
	 * @param string $title Post title
	 * @return array Suggested keywords
	 */
	private function generate_suggested_keywords( $content, $title ) {
		// Extract common words from content and title
		$text = strtolower( strip_tags( $content . ' ' . $title ) );
		$text = preg_replace( '/[^a-z0-9\s]/', ' ', $text );
		$words = array_filter( explode( ' ', $text ) );

		// Remove common stop words
		$stop_words = array( 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an' );
		$words = array_diff( $words, $stop_words );

		// Filter words by length
		$words = array_filter( $words, function( $word ) {
			return strlen( $word ) >= 3;
		});

		// Count word frequency
		$word_counts = array_count_values( $words );
		arsort( $word_counts );

		// Get top keywords
		$keywords = array_slice( array_keys( $word_counts ), 0, 10 );

		// Add some mock AI-generated keywords
		$ai_keywords = array(
			'seo optimization',
			'content marketing',
			'digital strategy',
			'search engine',
			'keyword research',
		);

		return array_merge( array_slice( $keywords, 0, 5 ), array_slice( $ai_keywords, 0, 3 ) );
	}

	/**
	 * Generate meta description
	 *
	 * @since 1.0.0
	 * @param string $content Post content
	 * @param string $title Post title
	 * @return string Generated meta description
	 */
	private function generate_meta_description( $content, $title ) {
		$clean_content = strip_tags( $content );
		$sentences = preg_split( '/[.!?]+/', $clean_content );

		// Get first meaningful sentence
		$description = '';
		foreach ( $sentences as $sentence ) {
			$sentence = trim( $sentence );
			if ( strlen( $sentence ) > 50 ) {
				$description = $sentence;
				break;
			}
		}

		// If no good sentence found, use beginning of content
		if ( empty( $description ) ) {
			$description = substr( $clean_content, 0, 150 );
		}

		// Ensure it's not too long
		if ( strlen( $description ) > 155 ) {
			$description = substr( $description, 0, 152 ) . '...';
		}

		return $description;
	}

	/**
	 * Generate title suggestion
	 *
	 * @since 1.0.0
	 * @param string $title Current title
	 * @param array  $keywords Suggested keywords
	 * @return string Suggested title
	 */
	private function generate_title_suggestion( $title, $keywords ) {
		// If title is already good, return it with minor optimization
		if ( strlen( $title ) >= 30 && strlen( $title ) <= 60 ) {
			return $title;
		}

		// If title is too short, suggest adding keywords
		if ( strlen( $title ) < 30 && ! empty( $keywords ) ) {
			$keyword = $keywords[0];
			return $title . ' - ' . ucfirst( $keyword );
		}

		// If title is too long, suggest shortening
		if ( strlen( $title ) > 60 ) {
			return substr( $title, 0, 57 ) . '...';
		}

		return $title;
	}

	/**
	 * Calculate SEO score
	 *
	 * @since 1.0.0
	 * @param string $content Post content
	 * @param string $title Post title
	 * @param array  $keywords Keywords
	 * @return int SEO score (0-100)
	 */
	private function calculate_seo_score( $content, $title, $keywords ) {
		$score = 0;
		$max_score = 100;

		// Title length check (20 points)
		$title_length = strlen( $title );
		if ( $title_length >= 30 && $title_length <= 60 ) {
			$score += 20;
		} elseif ( $title_length >= 20 && $title_length <= 70 ) {
			$score += 10;
		}

		// Content length check (20 points)
		$word_count = str_word_count( strip_tags( $content ) );
		if ( $word_count >= 300 ) {
			$score += 20;
		} elseif ( $word_count >= 150 ) {
			$score += 10;
		}

		// Keyword usage check (30 points)
		if ( ! empty( $keywords ) ) {
			$content_lower = strtolower( $content );
			$title_lower = strtolower( $title );
			$keyword_score = 0;

			foreach ( array_slice( $keywords, 0, 3 ) as $keyword ) {
				$keyword_lower = strtolower( $keyword );
				if ( strpos( $title_lower, $keyword_lower ) !== false ) {
					$keyword_score += 10;
				}
				if ( strpos( $content_lower, $keyword_lower ) !== false ) {
					$keyword_score += 5;
				}
			}

			$score += min( $keyword_score, 30 );
		}

		// Readability check (15 points)
		$sentences = preg_split( '/[.!?]+/', strip_tags( $content ) );
		$avg_sentence_length = $word_count / max( count( $sentences ) - 1, 1 );

		if ( $avg_sentence_length <= 20 ) {
			$score += 15;
		} elseif ( $avg_sentence_length <= 25 ) {
			$score += 10;
		}

		// Structure check (15 points)
		if ( preg_match( '/<h[1-6]/', $content ) ) {
			$score += 8;
		}
		if ( preg_match( '/<(ul|ol)/', $content ) ) {
			$score += 7;
		}

		return min( $score, $max_score );
	}

	/**
	 * Generate optimization suggestions
	 *
	 * @since 1.0.0
	 * @param string $content Post content
	 * @param string $title Post title
	 * @param int    $seo_score Current SEO score
	 * @return array Suggestions
	 */
	private function generate_suggestions( $content, $title, $seo_score ) {
		$suggestions = array();

		// Title suggestions
		$title_length = strlen( $title );
		if ( $title_length < 30 ) {
			$suggestions[] = esc_html__( 'Consider making your title longer (30-60 characters) for better SEO.', 'seo-auto-optimizer' );
		} elseif ( $title_length > 60 ) {
			$suggestions[] = esc_html__( 'Your title is too long. Consider shortening it to under 60 characters.', 'seo-auto-optimizer' );
		}

		// Content length suggestions
		$word_count = str_word_count( strip_tags( $content ) );
		if ( $word_count < 300 ) {
			$suggestions[] = esc_html__( 'Add more content. Aim for at least 300 words for better SEO performance.', 'seo-auto-optimizer' );
		}

		// Structure suggestions
		if ( ! preg_match( '/<h[1-6]/', $content ) ) {
			$suggestions[] = esc_html__( 'Add headings (H1, H2, H3) to improve content structure.', 'seo-auto-optimizer' );
		}

		if ( ! preg_match( '/<(ul|ol)/', $content ) ) {
			$suggestions[] = esc_html__( 'Consider adding bullet points or numbered lists to improve readability.', 'seo-auto-optimizer' );
		}

		// Meta description suggestion
		$suggestions[] = esc_html__( 'Add a compelling meta description to improve click-through rates.', 'seo-auto-optimizer' );

		return $suggestions;
	}

	/**
	 * Check rate limiting
	 *
	 * @since 1.0.0
	 * @return bool True if request is allowed, false if rate limited
	 */
	private function check_rate_limit() {
		$user_id = get_current_user_id();
		$cache_key = $this->rate_limit_key . $user_id;
		$requests = get_transient( $cache_key );

		if ( false === $requests ) {
			return true; // No previous requests
		}

		return $requests < $this->max_requests_per_minute;
	}

	/**
	 * Update rate limit counter
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function update_rate_limit() {
		$user_id = get_current_user_id();
		$cache_key = $this->rate_limit_key . $user_id;
		$requests = get_transient( $cache_key );

		if ( false === $requests ) {
			$requests = 0;
		}

		$requests++;
		set_transient( $cache_key, $requests, 60 ); // 1 minute expiration
	}
}

<?php
/**
 * Test Yoast Detection
 */

// WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/../../../');
    require_once ABSPATH . 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
}

echo "<h1>🔍 TEST DÉTECTION YOAST SEO</h1>\n\n";

// 1. Test si Yoast est installé
echo "<h2>1. Installation Yoast</h2>\n";
$yoast_file = WP_PLUGIN_DIR . '/wordpress-seo/wp-seo.php';
if (file_exists($yoast_file)) {
    echo "✅ Yoast SEO est installé\n";
    echo "📁 Fichier: {$yoast_file}\n";
} else {
    echo "❌ Yoast SEO n'est PAS installé\n";
    echo "📁 Cherché: {$yoast_file}\n";
}

// 2. Test si Yoast est actif
echo "\n<h2>2. Activation Yoast</h2>\n";
if (!function_exists('is_plugin_active')) {
    require_once ABSPATH . 'wp-admin/includes/plugin.php';
}

if (is_plugin_active('wordpress-seo/wp-seo.php')) {
    echo "✅ Yoast SEO est ACTIF\n";
} else {
    echo "❌ Yoast SEO n'est PAS actif\n";
    echo "💡 Activez-le dans Plugins > Plugins installés\n";
}

// 3. Test des constantes Yoast
echo "\n<h2>3. Constantes Yoast</h2>\n";
if (defined('WPSEO_VERSION')) {
    echo "✅ WPSEO_VERSION: " . WPSEO_VERSION . "\n";
} else {
    echo "❌ WPSEO_VERSION non définie\n";
}

if (defined('WPSEO_FILE')) {
    echo "✅ WPSEO_FILE: " . WPSEO_FILE . "\n";
} else {
    echo "❌ WPSEO_FILE non définie\n";
}

// 4. Test des classes Yoast
echo "\n<h2>4. Classes Yoast</h2>\n";
$yoast_classes = [
    'WPSEO_Options',
    'WPSEO_Meta',
    'WPSEO_Frontend',
    'Yoast\WP\SEO\Main'
];

foreach ($yoast_classes as $class) {
    if (class_exists($class)) {
        echo "✅ Classe {$class} existe\n";
    } else {
        echo "❌ Classe {$class} n'existe pas\n";
    }
}

// 5. Test de notre détecteur
echo "\n<h2>5. Notre Détecteur</h2>\n";
if (class_exists('SEO_Auto_Optimizer_Plugin_Detector')) {
    $detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
    $active_plugins = $detector->get_active_seo_plugins();
    
    echo "🔍 Plugins détectés: " . count($active_plugins) . "\n";
    foreach ($active_plugins as $key => $plugin) {
        echo "✅ {$key}: {$plugin['name']} v{$plugin['version']}\n";
    }
    
    if (empty($active_plugins)) {
        echo "❌ Aucun plugin SEO détecté par notre système\n";
    }
} else {
    echo "❌ Notre classe détecteur n'existe pas\n";
}

// 6. Test d'un post avec méta Yoast
echo "\n<h2>6. Test Méta Yoast</h2>\n";
$posts = get_posts(['numberposts' => 1, 'post_status' => 'publish']);
if (!empty($posts)) {
    $post_id = $posts[0]->ID;
    echo "📝 Test sur post ID: {$post_id}\n";
    
    // Méta fields Yoast
    $yoast_metas = [
        '_yoast_wpseo_focuskw' => 'Focus Keyword',
        '_yoast_wpseo_metadesc' => 'Meta Description',
        '_yoast_wpseo_title' => 'SEO Title'
    ];
    
    foreach ($yoast_metas as $meta_key => $label) {
        $value = get_post_meta($post_id, $meta_key, true);
        if (!empty($value)) {
            echo "✅ {$label}: {$value}\n";
        } else {
            echo "⚪ {$label}: (vide)\n";
        }
    }
} else {
    echo "❌ Aucun post trouvé pour tester\n";
}

echo "\n<h2>🎯 RÉSUMÉ</h2>\n";
echo "Si Yoast n'est pas détecté, vérifiez :\n";
echo "1. Yoast SEO est installé ET activé\n";
echo "2. Pas d'erreurs PHP dans les logs\n";
echo "3. Notre plugin est bien activé\n";
?>

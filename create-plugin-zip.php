<?php
/**
 * Create Plugin ZIP for WordPress Installation
 *
 * This script creates a ZIP file of the complete plugin
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

echo "🚀 Création du package ZIP pour WordPress...\n\n";

// Files and directories to include
$files_to_include = array(
    // Main files
    'seo-auto-optimizer.php',
    'uninstall.php',
    'README.md',
    'index.php',
    
    // Classes
    'includes/class-seo-auto-optimizer.php',
    'includes/class-plugin-detector.php',
    'includes/class-ai-keyword-generator.php',
    'includes/class-seo-data-injector.php',
    'includes/class-optimization-interface.php',
    'includes/class-admin-interface.php',
    'includes/class-boss-optimization-interface.php',
    'includes/class-security-checker.php',
    'includes/index.php',
    
    // Assets
    'assets/css/admin-interface.css',
    'assets/css/boss-optimization.css',
    'assets/js/admin-interface.js',
    'assets/js/boss-optimization.js',
    'assets/index.php',
    'assets/css/index.php',
    'assets/js/index.php',
    'assets/images/index.php',
    
    // Templates
    'templates/index.php',
    
    // Languages
    'languages/index.php',
    'languages/seo-auto-optimizer.pot'
);

// Check if all files exist
echo "📋 Vérification des fichiers...\n";
$missing_files = array();

foreach ($files_to_include as $file) {
    if (file_exists($file)) {
        echo "✅ {$file}\n";
    } else {
        echo "❌ {$file} - MANQUANT\n";
        $missing_files[] = $file;
    }
}

if (!empty($missing_files)) {
    echo "\n⚠️  Fichiers manquants détectés. Création des fichiers manquants...\n";
    
    foreach ($missing_files as $file) {
        $dir = dirname($file);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "📁 Répertoire créé: {$dir}\n";
        }
        
        if (basename($file) === 'index.php') {
            file_put_contents($file, "<?php\n// Silence is golden.\n");
            echo "🔒 Fichier de protection créé: {$file}\n";
        } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'pot') {
            file_put_contents($file, "# Translation template for SEO Auto Optimizer\n");
            echo "🌐 Template de traduction créé: {$file}\n";
        }
    }
}

// Create ZIP file
$zip_filename = 'seo-auto-optimizer.zip';

if (class_exists('ZipArchive')) {
    $zip = new ZipArchive();
    
    if ($zip->open($zip_filename, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
        echo "\n📦 Création du fichier ZIP...\n";
        
        foreach ($files_to_include as $file) {
            if (file_exists($file)) {
                $zip->addFile($file, 'seo-auto-optimizer/' . $file);
                echo "➕ Ajouté: {$file}\n";
            }
        }
        
        $zip->close();
        echo "\n✅ ZIP créé avec succès: {$zip_filename}\n";
        echo "📊 Taille du fichier: " . formatBytes(filesize($zip_filename)) . "\n";
        
    } else {
        echo "\n❌ Erreur lors de la création du ZIP\n";
    }
} else {
    echo "\n❌ Extension ZipArchive non disponible\n";
    echo "💡 Vous pouvez créer le ZIP manuellement en incluant tous les fichiers listés ci-dessus\n";
}

echo "\n🎯 Instructions d'installation:\n";
echo "1. Téléchargez le fichier {$zip_filename}\n";
echo "2. Dans WordPress Admin, allez dans Extensions > Ajouter\n";
echo "3. Cliquez sur 'Téléverser une extension'\n";
echo "4. Sélectionnez le fichier {$zip_filename}\n";
echo "5. Cliquez sur 'Installer maintenant'\n";
echo "6. Activez le plugin\n";
echo "7. Allez dans SEO Optimizer pour commencer\n";

echo "\n🔧 Configuration recommandée:\n";
echo "1. Obtenez une clé API Google Gemini (gratuite): https://makersuite.google.com/app/apikey\n";
echo "2. Ou une clé OpenAI: https://platform.openai.com/api-keys\n";
echo "3. Configurez les clés dans SEO Optimizer > Configuration\n";
echo "4. Testez les connexions API\n";
echo "5. Commencez l'optimisation dans Boss Optimization\n";

echo "\n🎉 Plugin prêt pour WordPress !\n";

/**
 * Format bytes to human readable format
 */
function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>

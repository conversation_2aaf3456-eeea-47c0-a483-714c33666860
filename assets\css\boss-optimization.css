/**
 * SEO Auto Optimizer - Boss Optimization Styles
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

/* Main Boss Wrapper */
.sao-boss-wrap {
    margin: 20px 0;
}

.sao-boss-wrap .wp-heading-inline {
    margin-bottom: 0;
}

.sao-boss-wrap .description {
    margin-top: 5px;
    color: #646970;
}

/* Overview Widget */
.sao-overview-widget {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.sao-overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.sao-overview-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    background: #f9f9f9;
}

.sao-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #fff;
}

.sao-card-total .sao-card-icon { background: #0073aa; }
.sao-card-optimized .sao-card-icon { background: #00a32a; }
.sao-card-pending .sao-card-icon { background: #ff6900; }
.sao-card-progress .sao-card-icon { background: #8c44c4; }

.sao-card-content {
    flex: 1;
}

.sao-card-number {
    font-size: 24px;
    font-weight: 600;
    line-height: 1;
    color: #1d2327;
    margin-bottom: 5px;
}

.sao-card-label {
    font-size: 13px;
    color: #646970;
    font-weight: 500;
}

/* Progress Container */
.sao-progress-container {
    margin-top: 20px;
}

.sao-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.sao-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00a32a 0%, #00d084 100%);
    transition: width 0.3s ease;
}

.sao-progress-text {
    font-size: 14px;
    color: #646970;
    text-align: center;
}

/* Filters */
.sao-filters-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.sao-filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    align-items: end;
}

.sao-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sao-filter-group label {
    font-size: 13px;
    font-weight: 500;
    color: #1d2327;
}

.sao-filter {
    padding: 6px 8px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 13px;
}

.sao-search-group {
    min-width: 200px;
}

/* Bulk Actions */
.sao-bulk-actions-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    margin: 20px 0 0 0;
}

.sao-bulk-actions-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sao-select-all-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
}

.sao-selected-count {
    font-size: 13px;
    color: #646970;
    font-weight: 500;
}

.sao-bulk-actions-right {
    display: flex;
    gap: 10px;
}

.sao-bulk-actions-right .button {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
}

.sao-bulk-actions-right .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Content Table */
.sao-table-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 0 0 4px 4px;
    margin-bottom: 20px;
}

.sao-table-loading {
    padding: 40px;
    text-align: center;
    color: #646970;
}

.sao-table-loading .spinner {
    float: none;
    margin-right: 10px;
}

.sao-content-table {
    width: 100%;
    border-collapse: collapse;
}

.sao-content-table th,
.sao-content-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f1;
}

.sao-content-table th {
    background: #f9f9f9;
    font-weight: 600;
    color: #1d2327;
    position: relative;
    cursor: pointer;
}

.sao-content-table th.sortable:hover {
    background: #f0f0f1;
}

.sao-content-table th .sorting-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
}

.sao-content-table th .sorting-indicator:before {
    content: "\f142";
    font-family: dashicons;
    font-size: 16px;
}

.sao-content-table th.sorted-asc .sorting-indicator:before {
    content: "\f142";
    opacity: 1;
}

.sao-content-table th.sorted-desc .sorting-indicator:before {
    content: "\f140";
    opacity: 1;
}

.sao-content-table tr:hover {
    background: #f9f9f9;
}

.sao-content-table tr.optimizing {
    background: #fff3cd;
}

/* Table Columns */
.sao-col-select {
    width: 40px;
    text-align: center;
}

.sao-col-title {
    min-width: 200px;
}

.sao-col-type {
    width: 100px;
}

.sao-col-status {
    width: 100px;
}

.sao-col-seo-status {
    width: 120px;
}

.sao-col-keywords {
    width: 80px;
    text-align: center;
}

.sao-col-last-optimized {
    width: 120px;
}

.sao-col-actions {
    width: 150px;
}

/* Status Badges */
.sao-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 1;
}

.sao-status-published {
    background: #d4edda;
    color: #155724;
}

.sao-status-draft {
    background: #fff3cd;
    color: #856404;
}

.sao-status-private {
    background: #f8d7da;
    color: #721c24;
}

.sao-seo-optimized {
    background: #d4edda;
    color: #155724;
}

.sao-seo-not-optimized {
    background: #f8d7da;
    color: #721c24;
}

.sao-seo-in-progress {
    background: #cce5ff;
    color: #004085;
}

/* Post Type Icons */
.sao-post-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sao-post-type .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #646970;
}

/* Action Buttons */
.sao-action-buttons {
    display: flex;
    gap: 5px;
}

.sao-action-btn {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.4;
    border-radius: 3px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 3px;
}

.sao-optimize-btn {
    background: #0073aa;
    color: #fff;
    border: 1px solid #0073aa;
}

.sao-optimize-btn:hover {
    background: #005a87;
    color: #fff;
}

.sao-optimize-btn:disabled {
    background: #c3c4c7;
    border-color: #c3c4c7;
    color: #a7aaad;
    cursor: not-allowed;
}

.sao-view-btn {
    background: #f6f7f7;
    color: #2271b1;
    border: 1px solid #c3c4c7;
}

.sao-edit-btn {
    background: #f6f7f7;
    color: #2271b1;
    border: 1px solid #c3c4c7;
}

/* Pagination */
.sao-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f6f7f7;
    border-top: 1px solid #c3c4c7;
}

.sao-pagination-info {
    font-size: 13px;
    color: #646970;
}

.sao-pagination-controls {
    display: flex;
    gap: 5px;
}

.sao-pagination-controls .button {
    padding: 4px 8px;
    font-size: 12px;
}

/* Progress Modal */
.sao-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sao-modal-content {
    background: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.sao-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
}

.sao-modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.sao-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sao-modal-body {
    padding: 20px;
}

.sao-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Progress Sections */
.sao-progress-section,
.sao-current-item-section,
.sao-results-section,
.sao-activity-section {
    margin-bottom: 25px;
}

.sao-progress-section h3,
.sao-current-item-section h3,
.sao-results-section h3,
.sao-activity-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #1d2327;
}

.sao-progress-bar-container {
    margin-bottom: 10px;
}

.sao-progress-bar-fill {
    background: linear-gradient(90deg, #0073aa 0%, #005a87 100%);
}

.sao-current-item-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f6f7f7;
    border-radius: 4px;
}

.sao-current-item-title {
    font-weight: 500;
}

.sao-current-item-status {
    font-size: 12px;
    color: #646970;
}

.sao-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.sao-result-item {
    text-align: center;
    padding: 10px;
    background: #f6f7f7;
    border-radius: 4px;
}

.sao-result-label {
    display: block;
    font-size: 12px;
    color: #646970;
    margin-bottom: 5px;
}

.sao-result-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.sao-activity-log {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background: #f9f9f9;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

.sao-activity-log .log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.sao-activity-log .log-success {
    color: #00a32a;
}

.sao-activity-log .log-error {
    color: #d63638;
}

.sao-activity-log .log-info {
    color: #646970;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sao-filters-row {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .sao-overview-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .sao-bulk-actions-container {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .sao-bulk-actions-right {
        justify-content: center;
    }
    
    .sao-pagination-container {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .sao-content-table {
        font-size: 12px;
    }
    
    .sao-content-table th,
    .sao-content-table td {
        padding: 8px 10px;
    }
    
    .sao-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .sao-results-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Loading States */
.sao-loading {
    opacity: 0.6;
    pointer-events: none;
}

.sao-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: sao-spin 1s linear infinite;
}

@keyframes sao-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Keyboard Shortcuts Indicator */
.sao-keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #1d2327;
    color: #fff;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.sao-keyboard-shortcuts.show {
    opacity: 0.9;
}

.sao-keyboard-shortcuts kbd {
    background: #fff;
    color: #1d2327;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin: 0 2px;
}

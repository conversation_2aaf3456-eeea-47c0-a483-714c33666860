<?php
/**
 * Admin Interface Class
 *
 * This class handles the complete admin interface for the plugin
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * Admin Interface Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Admin_Interface {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Admin_Interface|null
	 */
	private static $instance = null;

	/**
	 * Plugin options
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $options = array();

	/**
	 * Statistics data
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $stats = array();

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_Admin_Interface Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Initialize the admin interface
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Load options and stats
		$this->load_options();
		$this->load_statistics();

		// Admin hooks
		add_action( 'admin_init', array( $this, 'admin_init' ) );
		add_action( 'admin_menu', array( $this, 'admin_menu' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );

		// AJAX handlers
		add_action( 'wp_ajax_sao_test_api_connection', array( $this, 'ajax_test_api_connection' ) );
		add_action( 'wp_ajax_sao_export_settings', array( $this, 'ajax_export_settings' ) );
		add_action( 'wp_ajax_sao_import_settings', array( $this, 'ajax_import_settings' ) );
		add_action( 'wp_ajax_sao_clear_cache', array( $this, 'ajax_clear_cache' ) );
		add_action( 'wp_ajax_sao_reset_stats', array( $this, 'ajax_reset_stats' ) );
	}

	/**
	 * Initialize admin settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function admin_init() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			return;
		}

		// Register settings
		$this->register_settings();
	}

	/**
	 * Add admin menu pages
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function admin_menu() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			return;
		}

		// Main menu page (Dashboard)
		add_menu_page(
			esc_html__( 'SEO Auto Optimizer', 'seo-auto-optimizer' ),
			esc_html__( 'SEO Optimizer', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer',
			array( $this, 'dashboard_page' ),
			'dashicons-search',
			30
		);

		// Dashboard submenu (same as main page)
		add_submenu_page(
			'seo-auto-optimizer',
			esc_html__( 'Dashboard', 'seo-auto-optimizer' ),
			esc_html__( 'Dashboard', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer',
			array( $this, 'dashboard_page' )
		);

		// Configuration page
		add_submenu_page(
			'seo-auto-optimizer',
			esc_html__( 'Configuration', 'seo-auto-optimizer' ),
			esc_html__( 'Configuration', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-config',
			array( $this, 'configuration_page' )
		);

		// SEO Plugins page (existing)
		add_submenu_page(
			'seo-auto-optimizer',
			esc_html__( 'SEO Plugins', 'seo-auto-optimizer' ),
			esc_html__( 'SEO Plugins', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-plugins',
			array( $this, 'seo_plugins_page' )
		);

		// History page
		add_submenu_page(
			'seo-auto-optimizer',
			esc_html__( 'History', 'seo-auto-optimizer' ),
			esc_html__( 'History', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-history',
			array( $this, 'history_page' )
		);

		// Help page
		add_submenu_page(
			'seo-auto-optimizer',
			esc_html__( 'Help', 'seo-auto-optimizer' ),
			esc_html__( 'Help', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-help',
			array( $this, 'help_page' )
		);

		// Boss Optimization page (handled by separate class)
		// This is initialized in the main plugin class
	}

	/**
	 * Enqueue admin scripts and styles
	 *
	 * @since 1.0.0
	 * @param string $hook_suffix Current admin page hook suffix
	 * @return void
	 */
	public function enqueue_admin_scripts( $hook_suffix ) {
		// Load on all admin pages for now (will be optimized later)
		// if ( strpos( $hook_suffix, 'seo-auto-optimizer' ) === false ) {
		//     return;
		// }

		// Enqueue Chart.js for statistics
		wp_enqueue_script(
			'chart-js',
			'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
			array(),
			'3.9.1',
			true
		);

		// Enqueue admin CSS
		wp_enqueue_style(
			'sao-admin-interface',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'css/admin-interface.css',
			array(),
			SEO_AUTO_OPTIMIZER_VERSION
		);

		// Enqueue admin JS
		wp_enqueue_script(
			'sao-admin-interface',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'js/admin-interface.js',
			array( 'jquery', 'chart-js' ),
			SEO_AUTO_OPTIMIZER_VERSION,
			true
		);

		// Localize script
		wp_localize_script(
			'sao-admin-interface',
			'saoAdmin',
			array(
				'ajaxUrl'    => admin_url( 'admin-ajax.php' ),
				'nonce'      => wp_create_nonce( 'sao_admin_nonce' ),
				'strings'    => array(
					'testing'           => esc_html__( 'Testing connection...', 'seo-auto-optimizer' ),
					'success'           => esc_html__( 'Success!', 'seo-auto-optimizer' ),
					'error'             => esc_html__( 'Error occurred', 'seo-auto-optimizer' ),
					'confirmReset'      => esc_html__( 'Are you sure you want to reset all statistics?', 'seo-auto-optimizer' ),
					'confirmClearCache' => esc_html__( 'Are you sure you want to clear all cache?', 'seo-auto-optimizer' ),
					'exporting'         => esc_html__( 'Exporting settings...', 'seo-auto-optimizer' ),
					'importing'         => esc_html__( 'Importing settings...', 'seo-auto-optimizer' ),
				),
				'stats'      => $this->get_chart_data(),
			)
		);
	}

	/**
	 * Register plugin settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function register_settings() {
		// General settings
		register_setting(
			'sao_general_settings',
			'seo_auto_optimizer_general_options',
			array(
				'sanitize_callback' => array( $this, 'sanitize_general_options' ),
			)
		);

		// AI settings
		register_setting(
			'sao_ai_settings',
			'seo_auto_optimizer_ai_options',
			array(
				'sanitize_callback' => array( $this, 'sanitize_ai_options' ),
			)
		);

		// Advanced settings
		register_setting(
			'sao_advanced_settings',
			'seo_auto_optimizer_advanced_options',
			array(
				'sanitize_callback' => array( $this, 'sanitize_advanced_options' ),
			)
		);

		// Add settings sections and fields
		$this->add_settings_sections();
	}

	/**
	 * Add settings sections and fields
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function add_settings_sections() {
		// General settings section
		add_settings_section(
			'sao_general_section',
			esc_html__( 'General Settings', 'seo-auto-optimizer' ),
			array( $this, 'general_section_callback' ),
			'sao_general_settings'
		);

		// AI settings section
		add_settings_section(
			'sao_ai_section',
			esc_html__( 'AI Configuration', 'seo-auto-optimizer' ),
			array( $this, 'ai_section_callback' ),
			'sao_ai_settings'
		);

		// Advanced settings section
		add_settings_section(
			'sao_advanced_section',
			esc_html__( 'Advanced Settings', 'seo-auto-optimizer' ),
			array( $this, 'advanced_section_callback' ),
			'sao_advanced_settings'
		);

		// Add fields
		$this->add_settings_fields();
	}

	/**
	 * Load plugin options
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function load_options() {
		$this->options = array(
			'general'  => get_option( 'seo_auto_optimizer_general_options', array() ),
			'ai'       => get_option( 'seo_auto_optimizer_ai_options', array() ),
			'advanced' => get_option( 'seo_auto_optimizer_advanced_options', array() ),
		);
	}

	/**
	 * Load statistics data
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function load_statistics() {
		$this->stats = get_option( 'seo_auto_optimizer_statistics', array(
			'total_optimizations'    => 0,
			'successful_optimizations' => 0,
			'failed_optimizations'   => 0,
			'api_calls'              => array(),
			'seo_plugins_detected'   => array(),
			'last_optimization'      => 0,
			'cache_hits'             => 0,
			'cache_misses'           => 0,
		) );
	}

	/**
	 * Get chart data for statistics
	 *
	 * @since 1.0.0
	 * @return array Chart data
	 */
	private function get_chart_data() {
		return array(
			'optimizations' => array(
				'successful' => $this->stats['successful_optimizations'],
				'failed'     => $this->stats['failed_optimizations'],
			),
			'api_usage' => $this->stats['api_calls'],
			'cache_performance' => array(
				'hits'   => $this->stats['cache_hits'],
				'misses' => $this->stats['cache_misses'],
			),
		);
	}

	/**
	 * Dashboard page callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function dashboard_page() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'seo-auto-optimizer' ) );
		}

		echo '<div class="wrap sao-admin-wrap">';
		echo '<h1 class="wp-heading-inline">' . esc_html__( 'SEO Auto Optimizer Dashboard', 'seo-auto-optimizer' ) . '</h1>';

		// Add new optimization button
		echo '<a href="#" class="page-title-action sao-new-optimization">' . esc_html__( 'New Optimization', 'seo-auto-optimizer' ) . '</a>';
		echo '<hr class="wp-header-end">';

		// Dashboard content
		$this->render_dashboard_content();

		echo '</div>';
	}

	/**
	 * Configuration page callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function configuration_page() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'seo-auto-optimizer' ) );
		}

		// Handle form submission
		if ( isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'sao_config_nonce' ) ) {
			$this->handle_configuration_save();
		}

		echo '<div class="wrap sao-admin-wrap">';
		echo '<h1>' . esc_html__( 'Configuration', 'seo-auto-optimizer' ) . '</h1>';

		// Configuration content
		$this->render_configuration_content();

		echo '</div>';
	}

	/**
	 * SEO plugins page callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function seo_plugins_page() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'seo-auto-optimizer' ) );
		}

		// Get SEO plugin detector instance
		$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

		echo '<div class="wrap sao-admin-wrap">';
		echo '<h1>' . esc_html__( 'SEO Plugins Detection', 'seo-auto-optimizer' ) . '</h1>';

		// Display detected plugins
		echo $detector->render_admin_interface();

		echo '</div>';
	}

	/**
	 * History page callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function history_page() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'seo-auto-optimizer' ) );
		}

		echo '<div class="wrap sao-admin-wrap">';
		echo '<h1>' . esc_html__( 'Optimization History', 'seo-auto-optimizer' ) . '</h1>';

		// History content
		$this->render_history_content();

		echo '</div>';
	}

	/**
	 * Help page callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function help_page() {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'seo-auto-optimizer' ) );
		}

		echo '<div class="wrap sao-admin-wrap">';
		echo '<h1>' . esc_html__( 'Help & Documentation', 'seo-auto-optimizer' ) . '</h1>';

		// Help content
		$this->render_help_content();

		echo '</div>';
	}

	/**
	 * Render dashboard content
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_dashboard_content() {
		echo '<div class="sao-dashboard-grid">';

		// Statistics cards
		echo '<div class="sao-stats-cards">';
		$this->render_stats_cards();
		echo '</div>';

		// Charts section
		echo '<div class="sao-charts-section">';
		$this->render_charts();
		echo '</div>';

		// Recent activity
		echo '<div class="sao-recent-activity">';
		$this->render_recent_activity();
		echo '</div>';

		// Quick actions
		echo '<div class="sao-quick-actions">';
		$this->render_quick_actions();
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Render statistics cards
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_stats_cards() {
		$total_optimizations = $this->stats['total_optimizations'];
		$success_rate = $total_optimizations > 0 ?
			round( ( $this->stats['successful_optimizations'] / $total_optimizations ) * 100, 1 ) : 0;

		$cards = array(
			array(
				'title' => esc_html__( 'Total Optimizations', 'seo-auto-optimizer' ),
				'value' => number_format( $total_optimizations ),
				'icon'  => 'dashicons-chart-line',
				'color' => 'blue',
			),
			array(
				'title' => esc_html__( 'Success Rate', 'seo-auto-optimizer' ),
				'value' => $success_rate . '%',
				'icon'  => 'dashicons-yes-alt',
				'color' => 'green',
			),
			array(
				'title' => esc_html__( 'Cache Performance', 'seo-auto-optimizer' ),
				'value' => $this->get_cache_hit_rate() . '%',
				'icon'  => 'dashicons-performance',
				'color' => 'orange',
			),
			array(
				'title' => esc_html__( 'SEO Plugins', 'seo-auto-optimizer' ),
				'value' => count( $this->stats['seo_plugins_detected'] ),
				'icon'  => 'dashicons-admin-plugins',
				'color' => 'purple',
			),
		);

		foreach ( $cards as $card ) {
			echo '<div class="sao-stat-card sao-stat-' . esc_attr( $card['color'] ) . '">';
			echo '<div class="sao-stat-icon"><span class="dashicons ' . esc_attr( $card['icon'] ) . '"></span></div>';
			echo '<div class="sao-stat-content">';
			echo '<div class="sao-stat-value">' . esc_html( $card['value'] ) . '</div>';
			echo '<div class="sao-stat-title">' . esc_html( $card['title'] ) . '</div>';
			echo '</div>';
			echo '</div>';
		}
	}

	/**
	 * Render charts section
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_charts() {
		echo '<div class="sao-chart-container">';
		echo '<h2>' . esc_html__( 'Performance Analytics', 'seo-auto-optimizer' ) . '</h2>';

		echo '<div class="sao-charts-grid">';

		// Optimization success chart
		echo '<div class="sao-chart-item">';
		echo '<h3>' . esc_html__( 'Optimization Results', 'seo-auto-optimizer' ) . '</h3>';
		echo '<canvas id="sao-optimization-chart" width="400" height="200"></canvas>';
		echo '</div>';

		// API usage chart
		echo '<div class="sao-chart-item">';
		echo '<h3>' . esc_html__( 'API Usage', 'seo-auto-optimizer' ) . '</h3>';
		echo '<canvas id="sao-api-usage-chart" width="400" height="200"></canvas>';
		echo '</div>';

		echo '</div>';
		echo '</div>';
	}

	/**
	 * Render recent activity
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_recent_activity() {
		echo '<div class="sao-activity-container">';
		echo '<h2>' . esc_html__( 'Recent Activity', 'seo-auto-optimizer' ) . '</h2>';

		$recent_activities = $this->get_recent_activities();

		if ( empty( $recent_activities ) ) {
			echo '<p class="sao-no-activity">' . esc_html__( 'No recent activity found.', 'seo-auto-optimizer' ) . '</p>';
		} else {
			echo '<ul class="sao-activity-list">';
			foreach ( $recent_activities as $activity ) {
				echo '<li class="sao-activity-item">';
				echo '<span class="sao-activity-icon dashicons ' . esc_attr( $activity['icon'] ) . '"></span>';
				echo '<div class="sao-activity-content">';
				echo '<div class="sao-activity-title">' . esc_html( $activity['title'] ) . '</div>';
				echo '<div class="sao-activity-time">' . esc_html( $activity['time'] ) . '</div>';
				echo '</div>';
				echo '</li>';
			}
			echo '</ul>';
		}

		echo '</div>';
	}

	/**
	 * Render quick actions
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_quick_actions() {
		echo '<div class="sao-quick-actions-container">';
		echo '<h2>' . esc_html__( 'Quick Actions', 'seo-auto-optimizer' ) . '</h2>';

		echo '<div class="sao-actions-grid">';

		$actions = array(
			array(
				'title'       => esc_html__( 'Clear Cache', 'seo-auto-optimizer' ),
				'description' => esc_html__( 'Clear all cached data', 'seo-auto-optimizer' ),
				'button'      => esc_html__( 'Clear Cache', 'seo-auto-optimizer' ),
				'action'      => 'clear-cache',
				'icon'        => 'dashicons-trash',
			),
			array(
				'title'       => esc_html__( 'Test API Connections', 'seo-auto-optimizer' ),
				'description' => esc_html__( 'Test all configured AI APIs', 'seo-auto-optimizer' ),
				'button'      => esc_html__( 'Test APIs', 'seo-auto-optimizer' ),
				'action'      => 'test-apis',
				'icon'        => 'dashicons-admin-tools',
			),
			array(
				'title'       => esc_html__( 'Export Settings', 'seo-auto-optimizer' ),
				'description' => esc_html__( 'Download configuration backup', 'seo-auto-optimizer' ),
				'button'      => esc_html__( 'Export', 'seo-auto-optimizer' ),
				'action'      => 'export-settings',
				'icon'        => 'dashicons-download',
			),
			array(
				'title'       => esc_html__( 'Reset Statistics', 'seo-auto-optimizer' ),
				'description' => esc_html__( 'Reset all performance statistics', 'seo-auto-optimizer' ),
				'button'      => esc_html__( 'Reset Stats', 'seo-auto-optimizer' ),
				'action'      => 'reset-stats',
				'icon'        => 'dashicons-update',
			),
		);

		foreach ( $actions as $action ) {
			echo '<div class="sao-action-card">';
			echo '<div class="sao-action-icon"><span class="dashicons ' . esc_attr( $action['icon'] ) . '"></span></div>';
			echo '<div class="sao-action-content">';
			echo '<h3>' . esc_html( $action['title'] ) . '</h3>';
			echo '<p>' . esc_html( $action['description'] ) . '</p>';
			echo '<button type="button" class="button button-secondary sao-action-btn" data-action="' . esc_attr( $action['action'] ) . '">';
			echo esc_html( $action['button'] );
			echo '</button>';
			echo '</div>';
			echo '</div>';
		}

		echo '</div>';
		echo '</div>';
	}

	/**
	 * Render configuration content
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_configuration_content() {
		// TRAITEMENT DES FORMULAIRES DIRECT !
		if ( isset( $_POST['save_settings'] ) && wp_verify_nonce( $_POST['sao_nonce'], 'sao_save_settings' ) ) {
			$this->save_all_settings();
			echo '<div class="notice notice-success"><p>Paramètres sauvegardés !</p></div>';
		}

		echo '<div class="sao-config-simple">';
		echo '<form method="post" action="">';
		wp_nonce_field( 'sao_save_settings', 'sao_nonce' );

		// Section 1: General Settings
		echo '<div class="sao-config-section">';
		echo '<div class="sao-section-header">';
		echo '<h2><span class="dashicons dashicons-admin-generic"></span> ' . esc_html__( 'General Settings', 'seo-auto-optimizer' ) . '</h2>';
		echo '<p>' . esc_html__( 'Configure basic plugin settings', 'seo-auto-optimizer' ) . '</p>';
		echo '</div>';
		echo '<div class="sao-section-content">';
		$this->render_general_settings();
		echo '</div>';
		echo '</div>';

		// Section 2: AI APIs
		echo '<div class="sao-config-section">';
		echo '<div class="sao-section-header">';
		echo '<h2><span class="dashicons dashicons-cloud"></span> ' . esc_html__( 'AI APIs Configuration', 'seo-auto-optimizer' ) . '</h2>';
		echo '<p>' . esc_html__( 'Configure your AI provider API keys', 'seo-auto-optimizer' ) . '</p>';
		echo '</div>';
		echo '<div class="sao-section-content">';
		$this->render_ai_settings();
		echo '</div>';
		echo '</div>';

		// Section 3: Advanced Settings
		echo '<div class="sao-config-section">';
		echo '<div class="sao-section-header">';
		echo '<h2><span class="dashicons dashicons-admin-tools"></span> ' . esc_html__( 'Advanced Settings', 'seo-auto-optimizer' ) . '</h2>';
		echo '<p>' . esc_html__( 'Advanced configuration options', 'seo-auto-optimizer' ) . '</p>';
		echo '</div>';
		echo '<div class="sao-section-content">';
		$this->render_advanced_settings();
		echo '</div>';
		echo '</div>';

		// BOUTON DE SAUVEGARDE GLOBAL
		echo '<div class="sao-config-section">';
		echo '<div class="sao-section-content">';
		echo '<input type="submit" name="save_settings" class="button-primary button-hero" value="💾 SAUVEGARDER TOUS LES PARAMÈTRES" />';
		echo '</div>';
		echo '</div>';

		echo '</form>';

		// Section 4: Import/Export (séparée)
		echo '<div class="sao-config-section">';
		echo '<div class="sao-section-header">';
		echo '<h2><span class="dashicons dashicons-migrate"></span> ' . esc_html__( 'Import/Export Settings', 'seo-auto-optimizer' ) . '</h2>';
		echo '<p>' . esc_html__( 'Backup and restore your configuration', 'seo-auto-optimizer' ) . '</p>';
		echo '</div>';
		echo '<div class="sao-section-content">';
		$this->render_import_export_settings();
		echo '</div>';
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Render general settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_general_settings() {
		$general_options = $this->options['general'];

		echo '<table class="form-table">';

		// Number of keywords to generate
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Keywords to Generate', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<select name="keywords_count">';
		for ( $i = 5; $i <= 15; $i++ ) {
			$selected = selected( $general_options['keywords_count'] ?? 8, $i, false );
			echo '<option value="' . esc_attr( $i ) . '" ' . $selected . '>' . esc_html( $i ) . '</option>';
		}
		echo '</select>';
		echo '<p class="description">' . esc_html__( 'Number of keywords to generate per optimization (5-15).', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		// Primary SEO plugin
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Primary SEO Plugin', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
		$active_plugins = $detector->get_active_seo_plugins();

		echo '<select name="primary_seo_plugin">';
		echo '<option value="">' . esc_html__( 'Auto-detect', 'seo-auto-optimizer' ) . '</option>';

		foreach ( $active_plugins as $plugin_key => $plugin_info ) {
			$selected = selected( $general_options['primary_seo_plugin'] ?? '', $plugin_key, false );
			echo '<option value="' . esc_attr( $plugin_key ) . '" ' . $selected . '>' . esc_html( $plugin_info['name'] ) . '</option>';
		}
		echo '</select>';
		echo '<p class="description">' . esc_html__( 'Choose which SEO plugin to prioritize if multiple are installed.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		// Backup mode
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Backup Mode', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<label>';
		echo '<input type="checkbox" name="backup_enabled" value="1" ' . checked( $general_options['backup_enabled'] ?? true, true, false ) . ' />';
		echo esc_html__( 'Create backup before modifications', 'seo-auto-optimizer' );
		echo '</label>';
		echo '<p class="description">' . esc_html__( 'Automatically backup content before applying optimizations.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		// Log level
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Log Level', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		$log_levels = array(
			'none'  => esc_html__( 'None', 'seo-auto-optimizer' ),
			'error' => esc_html__( 'Errors Only', 'seo-auto-optimizer' ),
			'debug' => esc_html__( 'Debug', 'seo-auto-optimizer' ),
			'all'   => esc_html__( 'Everything', 'seo-auto-optimizer' ),
		);

		echo '<select name="log_level">';
		foreach ( $log_levels as $level => $label ) {
			$selected = selected( $general_options['log_level'] ?? 'error', $level, false );
			echo '<option value="' . esc_attr( $level ) . '" ' . $selected . '>' . esc_html( $label ) . '</option>';
		}
		echo '</select>';
		echo '<p class="description">' . esc_html__( 'Choose what level of information to log.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		echo '</table>';
	}

	/**
	 * Render AI settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_ai_settings() {
		$ai_options = $this->options['ai'];

		echo '<table class="form-table">';

		// AI providers
		$providers = array(
			'gemini'    => 'Google Gemini',
			'openai'    => 'OpenAI GPT',
			'anthropic' => 'Anthropic Claude',
			'ollama'    => 'Ollama (Local)',
			'custom'    => 'Custom API',
		);

		foreach ( $providers as $provider => $name ) {
			echo '<tr>';
			echo '<th scope="row">' . esc_html( $name ) . ' API Key</th>';
			echo '<td>';

			if ( $provider === 'ollama' ) {
				echo '<p class="description">' . esc_html__( 'Ollama runs locally and does not require an API key.', 'seo-auto-optimizer' ) . '</p>';

				// Ollama endpoint
				echo '<label for="ollama_endpoint">' . esc_html__( 'Ollama Endpoint:', 'seo-auto-optimizer' ) . '</label><br>';
				echo '<input type="url" id="ollama_endpoint" name="ollama_endpoint" value="' . esc_attr( $ai_options['ollama_endpoint'] ?? 'http://localhost:11434/api/generate' ) . '" class="regular-text" />';
				echo '<br><br>';

				// Ollama model
				echo '<label for="ollama_model">' . esc_html__( 'Ollama Model:', 'seo-auto-optimizer' ) . '</label><br>';
				echo '<input type="text" id="ollama_model" name="ollama_model" value="' . esc_attr( $ai_options['ollama_model'] ?? 'llama2' ) . '" class="regular-text" />';

			} elseif ( $provider === 'custom' ) {
				echo '<input type="password" name="' . esc_attr( $provider ) . '_api_key" value="' . esc_attr( $this->get_masked_api_key( $provider ) ) . '" class="regular-text" />';
				echo '<br><br>';

				// Custom endpoint
				echo '<label for="custom_endpoint">' . esc_html__( 'Custom API Endpoint:', 'seo-auto-optimizer' ) . '</label><br>';
				echo '<input type="url" id="custom_endpoint" name="custom_endpoint" value="' . esc_attr( $ai_options['custom_endpoint'] ?? '' ) . '" class="regular-text" />';

			} else {
				echo '<input type="password" name="' . esc_attr( $provider ) . '_api_key" value="' . esc_attr( $this->get_masked_api_key( $provider ) ) . '" class="regular-text" />';
			}

			echo '<br>';
			echo '<button type="button" class="button button-secondary sao-test-api" data-provider="' . esc_attr( $provider ) . '">';
			echo esc_html__( 'Test Connection', 'seo-auto-optimizer' );
			echo '</button>';
			echo '<span class="sao-test-result" id="test-result-' . esc_attr( $provider ) . '"></span>';
			echo '</td>';
			echo '</tr>';
		}

		// Provider priority
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Provider Priority', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<p class="description">' . esc_html__( 'Drag to reorder providers by priority. The plugin will try providers in this order.', 'seo-auto-optimizer' ) . '</p>';
		echo '<ul id="sao-provider-priority" class="sao-sortable-list">';

		$provider_order = $ai_options['provider_order'] ?? array_keys( $providers );
		foreach ( $provider_order as $provider ) {
			if ( isset( $providers[ $provider ] ) ) {
				echo '<li data-provider="' . esc_attr( $provider ) . '">';
				echo '<span class="dashicons dashicons-menu"></span>';
				echo esc_html( $providers[ $provider ] );
				echo '<input type="hidden" name="seo_auto_optimizer_ai_options[provider_order][]" value="' . esc_attr( $provider ) . '" />';
				echo '</li>';
			}
		}
		echo '</ul>';
		echo '</td>';
		echo '</tr>';

		echo '</table>';
	}

	/**
	 * Get masked API key for display
	 *
	 * @since 1.0.0
	 * @param string $provider Provider name
	 * @return string Masked API key
	 */
	private function get_masked_api_key( $provider ) {
		// Get API key from options directly instead of private method
		$ai_options = get_option( 'seo_auto_optimizer_ai_options', array() );
		$api_key = $ai_options[ $provider . '_api_key' ] ?? '';

		if ( empty( $api_key ) ) {
			return '';
		}

		// Mask the API key for security
		$length = strlen( $api_key );
		if ( $length <= 8 ) {
			return str_repeat( '*', $length );
		}

		return substr( $api_key, 0, 4 ) . str_repeat( '*', $length - 8 ) . substr( $api_key, -4 );
	}

	/**
	 * Render advanced settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_advanced_settings() {
		$advanced_options = $this->options['advanced'];

		echo '<table class="form-table">';

		// Cache settings
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Cache Duration', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<select name="cache_duration">';
		$durations = array(
			3600   => esc_html__( '1 Hour', 'seo-auto-optimizer' ),
			7200   => esc_html__( '2 Hours', 'seo-auto-optimizer' ),
			21600  => esc_html__( '6 Hours', 'seo-auto-optimizer' ),
			43200  => esc_html__( '12 Hours', 'seo-auto-optimizer' ),
			86400  => esc_html__( '24 Hours', 'seo-auto-optimizer' ),
			172800 => esc_html__( '48 Hours', 'seo-auto-optimizer' ),
		);

		foreach ( $durations as $seconds => $label ) {
			$selected = selected( $advanced_options['cache_duration'] ?? 86400, $seconds, false );
			echo '<option value="' . esc_attr( $seconds ) . '" ' . $selected . '>' . esc_html( $label ) . '</option>';
		}
		echo '</select>';
		echo '<p class="description">' . esc_html__( 'How long to cache AI-generated keywords.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		// Rate limiting
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Rate Limit', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<input type="number" name="rate_limit" value="' . esc_attr( $advanced_options['rate_limit'] ?? 5 ) . '" min="1" max="20" class="small-text" />';
		echo ' ' . esc_html__( 'requests per minute', 'seo-auto-optimizer' );
		echo '<p class="description">' . esc_html__( 'Maximum number of optimization requests per user per minute.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		// Debug mode
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Debug Mode', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<label>';
		echo '<input type="checkbox" name="debug_mode" value="1" ' . checked( $advanced_options['debug_mode'] ?? false, true, false ) . ' />';
		echo esc_html__( 'Enable debug mode', 'seo-auto-optimizer' );
		echo '</label>';
		echo '<p class="description">' . esc_html__( 'Log detailed information for troubleshooting.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		// Auto-apply optimizations
		echo '<tr>';
		echo '<th scope="row">' . esc_html__( 'Auto-Apply', 'seo-auto-optimizer' ) . '</th>';
		echo '<td>';
		echo '<label>';
		echo '<input type="checkbox" name="auto_apply" value="1" ' . checked( $advanced_options['auto_apply'] ?? false, true, false ) . ' />';
		echo esc_html__( 'Automatically apply optimizations', 'seo-auto-optimizer' );
		echo '</label>';
		echo '<p class="description">' . esc_html__( 'Automatically apply AI suggestions without user confirmation.', 'seo-auto-optimizer' ) . '</p>';
		echo '</td>';
		echo '</tr>';

		echo '</table>';
	}

	/**
	 * Render import/export settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_import_export_settings() {
		echo '<div class="sao-import-export-grid">';

		// Export section
		echo '<div class="sao-export-section">';
		echo '<h3>' . esc_html__( 'Export Settings', 'seo-auto-optimizer' ) . '</h3>';
		echo '<p>' . esc_html__( 'Download your current plugin configuration as a JSON file.', 'seo-auto-optimizer' ) . '</p>';
		echo '<button type="button" class="button button-secondary sao-export-btn">';
		echo '<span class="dashicons dashicons-download"></span> ';
		echo esc_html__( 'Export Settings', 'seo-auto-optimizer' );
		echo '</button>';
		echo '</div>';

		// Import section
		echo '<div class="sao-import-section">';
		echo '<h3>' . esc_html__( 'Import Settings', 'seo-auto-optimizer' ) . '</h3>';
		echo '<p>' . esc_html__( 'Upload a previously exported configuration file.', 'seo-auto-optimizer' ) . '</p>';
		echo '<div class="sao-file-input">';
		echo '<input type="file" class="sao-import-file" accept=".json" />';
		echo '</div>';
		echo '<button type="button" class="button button-secondary sao-import-btn" disabled>';
		echo '<span class="dashicons dashicons-upload"></span> ';
		echo esc_html__( 'Import Settings', 'seo-auto-optimizer' );
		echo '</button>';
		echo '<p class="description">' . esc_html__( 'Warning: This will overwrite your current settings.', 'seo-auto-optimizer' ) . '</p>';
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Render history content
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_history_content() {
		$history = $this->get_optimization_history();

		echo '<div class="sao-history-container">';

		if ( empty( $history ) ) {
			echo '<div class="sao-no-history">';
			echo '<p>' . esc_html__( 'No optimization history found.', 'seo-auto-optimizer' ) . '</p>';
			echo '<p>' . esc_html__( 'Start optimizing your content to see the history here.', 'seo-auto-optimizer' ) . '</p>';
			echo '</div>';
		} else {
			echo '<table class="sao-history-table">';
			echo '<thead>';
			echo '<tr>';
			echo '<th>' . esc_html__( 'Date', 'seo-auto-optimizer' ) . '</th>';
			echo '<th>' . esc_html__( 'Post', 'seo-auto-optimizer' ) . '</th>';
			echo '<th>' . esc_html__( 'Keywords Generated', 'seo-auto-optimizer' ) . '</th>';
			echo '<th>' . esc_html__( 'AI Provider', 'seo-auto-optimizer' ) . '</th>';
			echo '<th>' . esc_html__( 'Status', 'seo-auto-optimizer' ) . '</th>';
			echo '<th>' . esc_html__( 'Actions', 'seo-auto-optimizer' ) . '</th>';
			echo '</tr>';
			echo '</thead>';
			echo '<tbody>';

			foreach ( $history as $entry ) {
				echo '<tr>';
				echo '<td>' . esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), $entry['timestamp'] ) ) . '</td>';
				echo '<td>';
				if ( $entry['post_id'] ) {
					$post_title = get_the_title( $entry['post_id'] );
					$edit_link = get_edit_post_link( $entry['post_id'] );
					if ( $edit_link ) {
						echo '<a href="' . esc_url( $edit_link ) . '">' . esc_html( $post_title ) . '</a>';
					} else {
						echo esc_html( $post_title );
					}
				} else {
					echo esc_html__( 'Unknown', 'seo-auto-optimizer' );
				}
				echo '</td>';
				echo '<td>' . esc_html( $entry['keywords_count'] ) . '</td>';
				echo '<td>' . esc_html( $entry['provider'] ) . '</td>';
				echo '<td>';
				$status_class = $entry['status'] === 'success' ? 'success' : 'error';
				echo '<span class="sao-status-badge sao-status-' . esc_attr( $status_class ) . '">';
				echo esc_html( ucfirst( $entry['status'] ) );
				echo '</span>';
				echo '</td>';
				echo '<td>';
				echo '<button type="button" class="button button-small sao-view-details" data-entry-id="' . esc_attr( $entry['id'] ) . '">';
				echo esc_html__( 'View Details', 'seo-auto-optimizer' );
				echo '</button>';
				echo '</td>';
				echo '</tr>';
			}

			echo '</tbody>';
			echo '</table>';
		}

		echo '</div>';
	}

	/**
	 * Render help content
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_help_content() {
		echo '<div class="sao-help-container">';

		echo '<div class="sao-help-intro">';
		echo '<h2>' . esc_html__( 'Welcome to SEO Auto Optimizer', 'seo-auto-optimizer' ) . '</h2>';
		echo '<p>' . esc_html__( 'This plugin uses artificial intelligence to automatically generate SEO-optimized keywords for your content. Here\'s how to get started:', 'seo-auto-optimizer' ) . '</p>';
		echo '</div>';

		echo '<div class="sao-help-grid">';

		// Getting Started
		echo '<div class="sao-help-section">';
		echo '<h3><span class="dashicons dashicons-admin-generic"></span>' . esc_html__( 'Getting Started', 'seo-auto-optimizer' ) . '</h3>';
		echo '<ul>';
		echo '<li>' . esc_html__( 'Configure your AI API keys in the Configuration tab', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Test your API connections to ensure they work', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Start optimizing your posts and pages', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Review the generated keywords and apply them', 'seo-auto-optimizer' ) . '</li>';
		echo '</ul>';
		echo '</div>';

		// AI Providers
		echo '<div class="sao-help-section">';
		echo '<h3><span class="dashicons dashicons-cloud"></span>' . esc_html__( 'AI Providers', 'seo-auto-optimizer' ) . '</h3>';
		echo '<ul>';
		echo '<li><strong>Google Gemini:</strong> ' . esc_html__( 'Recommended for most users', 'seo-auto-optimizer' ) . '</li>';
		echo '<li><strong>OpenAI GPT:</strong> ' . esc_html__( 'High-quality results, requires API key', 'seo-auto-optimizer' ) . '</li>';
		echo '<li><strong>Anthropic Claude:</strong> ' . esc_html__( 'Advanced AI with good SEO understanding', 'seo-auto-optimizer' ) . '</li>';
		echo '<li><strong>Ollama:</strong> ' . esc_html__( 'Local AI, no API key required', 'seo-auto-optimizer' ) . '</li>';
		echo '<li><strong>Custom API:</strong> ' . esc_html__( 'Use your own AI endpoint', 'seo-auto-optimizer' ) . '</li>';
		echo '</ul>';
		echo '</div>';

		// Features
		echo '<div class="sao-help-section">';
		echo '<h3><span class="dashicons dashicons-star-filled"></span>' . esc_html__( 'Key Features', 'seo-auto-optimizer' ) . '</h3>';
		echo '<ul>';
		echo '<li>' . esc_html__( 'AI-powered keyword generation', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'SEO plugin detection and integration', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Automatic caching for performance', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Rate limiting for API protection', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Comprehensive statistics and monitoring', 'seo-auto-optimizer' ) . '</li>';
		echo '</ul>';
		echo '</div>';

		// Troubleshooting
		echo '<div class="sao-help-section">';
		echo '<h3><span class="dashicons dashicons-sos"></span>' . esc_html__( 'Troubleshooting', 'seo-auto-optimizer' ) . '</h3>';
		echo '<ul>';
		echo '<li>' . esc_html__( 'Check your API keys are correctly configured', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Test API connections using the test buttons', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Enable debug mode for detailed logging', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Clear cache if you\'re seeing old results', 'seo-auto-optimizer' ) . '</li>';
		echo '<li>' . esc_html__( 'Check WordPress error logs for issues', 'seo-auto-optimizer' ) . '</li>';
		echo '</ul>';
		echo '</div>';

		echo '</div>';

		// Support section
		echo '<div class="sao-help-support">';
		echo '<h3>' . esc_html__( 'Need More Help?', 'seo-auto-optimizer' ) . '</h3>';
		echo '<p>' . esc_html__( 'If you need additional assistance, please check the following resources:', 'seo-auto-optimizer' ) . '</p>';
		echo '<ul>';
		echo '<li><a href="#" target="_blank">' . esc_html__( 'Plugin Documentation', 'seo-auto-optimizer' ) . '</a></li>';
		echo '<li><a href="#" target="_blank">' . esc_html__( 'Support Forum', 'seo-auto-optimizer' ) . '</a></li>';
		echo '<li><a href="#" target="_blank">' . esc_html__( 'Contact Support', 'seo-auto-optimizer' ) . '</a></li>';
		echo '</ul>';
		echo '</div>';

		echo '</div>';
	}

	/**
	 * Get cache hit rate
	 *
	 * @since 1.0.0
	 * @return float Cache hit rate percentage
	 */
	private function get_cache_hit_rate() {
		$hits = $this->stats['cache_hits'];
		$misses = $this->stats['cache_misses'];
		$total = $hits + $misses;

		if ( $total === 0 ) {
			return 0;
		}

		return round( ( $hits / $total ) * 100, 1 );
	}

	/**
	 * Get recent activities
	 *
	 * @since 1.0.0
	 * @return array Recent activities
	 */
	private function get_recent_activities() {
		// This would typically come from a database table
		// For now, return mock data
		return array(
			array(
				'title' => esc_html__( 'Keywords generated for "Sample Post"', 'seo-auto-optimizer' ),
				'time'  => esc_html__( '2 hours ago', 'seo-auto-optimizer' ),
				'icon'  => 'dashicons-tag',
			),
			array(
				'title' => esc_html__( 'API connection tested successfully', 'seo-auto-optimizer' ),
				'time'  => esc_html__( '1 day ago', 'seo-auto-optimizer' ),
				'icon'  => 'dashicons-yes-alt',
			),
			array(
				'title' => esc_html__( 'Settings exported', 'seo-auto-optimizer' ),
				'time'  => esc_html__( '3 days ago', 'seo-auto-optimizer' ),
				'icon'  => 'dashicons-download',
			),
		);
	}

	/**
	 * Get optimization history
	 *
	 * @since 1.0.0
	 * @return array Optimization history
	 */
	private function get_optimization_history() {
		// This would typically come from a database table
		// For now, return mock data
		return array(
			array(
				'id'             => 1,
				'post_id'        => 123,
				'keywords_count' => 8,
				'provider'       => 'Gemini',
				'status'         => 'success',
				'timestamp'      => time() - 3600,
			),
			array(
				'id'             => 2,
				'post_id'        => 124,
				'keywords_count' => 6,
				'provider'       => 'OpenAI',
				'status'         => 'success',
				'timestamp'      => time() - 7200,
			),
		);
	}

	/**
	 * Handle AJAX test API connection
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_test_api_connection() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		$provider = sanitize_text_field( $_POST['provider'] ?? '' );

		if ( empty( $provider ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Provider not specified.', 'seo-auto-optimizer' ) ) );
		}

		try {
			// Get AI generator instance
			$ai_generator = SEO_Auto_Optimizer_AI_Keyword_Generator::get_instance();

			// Test the connection with a simple prompt
			$test_result = $ai_generator->generate_keywords( 'Test SEO optimization', 'This is a test content for API connection.' );

			if ( $test_result['success'] ) {
				wp_send_json_success( array( 'message' => esc_html__( 'Connection successful!', 'seo-auto-optimizer' ) ) );
			} else {
				wp_send_json_error( array( 'message' => $test_result['error'] ?? esc_html__( 'Connection failed.', 'seo-auto-optimizer' ) ) );
			}

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Handle AJAX export settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_export_settings() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		try {
			$export_data = array(
				'version'   => SEO_AUTO_OPTIMIZER_VERSION,
				'timestamp' => current_time( 'timestamp' ),
				'settings'  => array(
					'general'  => get_option( 'seo_auto_optimizer_general_options', array() ),
					'ai'       => get_option( 'seo_auto_optimizer_ai_options', array() ),
					'advanced' => get_option( 'seo_auto_optimizer_advanced_options', array() ),
				),
			);

			// Remove sensitive data (API keys)
			if ( isset( $export_data['settings']['ai'] ) ) {
				foreach ( $export_data['settings']['ai'] as $key => $value ) {
					if ( strpos( $key, '_api_key' ) !== false ) {
						unset( $export_data['settings']['ai'][ $key ] );
					}
				}
			}

			$filename = 'seo-auto-optimizer-settings-' . date( 'Y-m-d-H-i-s' ) . '.json';
			$content = wp_json_encode( $export_data, JSON_PRETTY_PRINT );

			wp_send_json_success( array(
				'filename' => $filename,
				'content'  => $content,
			) );

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Handle AJAX import settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_import_settings() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		$import_data = sanitize_textarea_field( $_POST['import_data'] ?? '' );

		if ( empty( $import_data ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'No import data provided.', 'seo-auto-optimizer' ) ) );
		}

		try {
			$data = json_decode( $import_data, true );

			if ( ! $data || ! isset( $data['settings'] ) ) {
				wp_send_json_error( array( 'message' => esc_html__( 'Invalid import file format.', 'seo-auto-optimizer' ) ) );
			}

			// Import settings
			if ( isset( $data['settings']['general'] ) ) {
				update_option( 'seo_auto_optimizer_general_options', $data['settings']['general'] );
			}

			if ( isset( $data['settings']['ai'] ) ) {
				update_option( 'seo_auto_optimizer_ai_options', $data['settings']['ai'] );
			}

			if ( isset( $data['settings']['advanced'] ) ) {
				update_option( 'seo_auto_optimizer_advanced_options', $data['settings']['advanced'] );
			}

			wp_send_json_success( array( 'message' => esc_html__( 'Settings imported successfully.', 'seo-auto-optimizer' ) ) );

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Handle AJAX clear cache
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_clear_cache() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		try {
			// Clear all plugin-related transients
			global $wpdb;

			$wpdb->query(
				$wpdb->prepare(
					"DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
					'_transient_sao_%'
				)
			);

			$wpdb->query(
				$wpdb->prepare(
					"DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
					'_transient_timeout_sao_%'
				)
			);

			wp_send_json_success( array( 'message' => esc_html__( 'Cache cleared successfully.', 'seo-auto-optimizer' ) ) );

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Handle AJAX reset statistics
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_reset_stats() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'sao_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ) ) );
		}

		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => esc_html__( 'Insufficient permissions.', 'seo-auto-optimizer' ) ) );
		}

		try {
			// Reset statistics
			$default_stats = array(
				'total_optimizations'      => 0,
				'successful_optimizations' => 0,
				'failed_optimizations'     => 0,
				'api_calls'                => array(),
				'seo_plugins_detected'     => array(),
				'last_optimization'        => 0,
				'cache_hits'               => 0,
				'cache_misses'             => 0,
			);

			update_option( 'seo_auto_optimizer_statistics', $default_stats );

			wp_send_json_success( array( 'message' => esc_html__( 'Statistics reset successfully.', 'seo-auto-optimizer' ) ) );

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Sanitize general options
	 *
	 * @since 1.0.0
	 * @param array $input Input options
	 * @return array Sanitized options
	 */
	public function sanitize_general_options( $input ) {
		$sanitized = array();

		// Keywords count
		$sanitized['keywords_count'] = absint( $input['keywords_count'] ?? 8 );
		if ( $sanitized['keywords_count'] < 5 || $sanitized['keywords_count'] > 15 ) {
			$sanitized['keywords_count'] = 8;
		}

		// Primary SEO plugin
		$sanitized['primary_seo_plugin'] = sanitize_text_field( $input['primary_seo_plugin'] ?? '' );

		// Backup enabled
		$sanitized['backup_enabled'] = ! empty( $input['backup_enabled'] );

		// Log level
		$valid_log_levels = array( 'none', 'error', 'debug', 'all' );
		$sanitized['log_level'] = in_array( $input['log_level'] ?? 'error', $valid_log_levels, true )
			? $input['log_level']
			: 'error';

		return $sanitized;
	}

	/**
	 * Sanitize AI options
	 *
	 * @since 1.0.0
	 * @param array $input Input options
	 * @return array Sanitized options
	 */
	public function sanitize_ai_options( $input ) {
		$sanitized = array();

		// API keys
		$providers = array( 'gemini', 'openai', 'anthropic', 'custom' );
		foreach ( $providers as $provider ) {
			$key = $provider . '_api_key';
			if ( isset( $input[ $key ] ) && ! empty( $input[ $key ] ) ) {
				// Only update if not masked
				$api_key = sanitize_text_field( $input[ $key ] );
				if ( strpos( $api_key, '*' ) === false ) {
					// Store encrypted API key
					$ai_generator = SEO_Auto_Optimizer_AI_Keyword_Generator::get_instance();
					$ai_generator->set_api_key( $provider, $api_key );
				}
			}
		}

		// Ollama settings
		$sanitized['ollama_endpoint'] = esc_url_raw( $input['ollama_endpoint'] ?? 'http://localhost:11434/api/generate' );
		$sanitized['ollama_model'] = sanitize_text_field( $input['ollama_model'] ?? 'llama2' );

		// Custom endpoint
		$sanitized['custom_endpoint'] = esc_url_raw( $input['custom_endpoint'] ?? '' );

		// Provider order
		if ( isset( $input['provider_order'] ) && is_array( $input['provider_order'] ) ) {
			$sanitized['provider_order'] = array_map( 'sanitize_text_field', $input['provider_order'] );
		}

		return $sanitized;
	}

	/**
	 * Sanitize advanced options
	 *
	 * @since 1.0.0
	 * @param array $input Input options
	 * @return array Sanitized options
	 */
	public function sanitize_advanced_options( $input ) {
		$sanitized = array();

		// Cache duration
		$valid_durations = array( 3600, 7200, 21600, 43200, 86400, 172800 );
		$sanitized['cache_duration'] = in_array( absint( $input['cache_duration'] ?? 86400 ), $valid_durations, true )
			? absint( $input['cache_duration'] )
			: 86400;

		// Rate limit
		$sanitized['rate_limit'] = absint( $input['rate_limit'] ?? 5 );
		if ( $sanitized['rate_limit'] < 1 || $sanitized['rate_limit'] > 20 ) {
			$sanitized['rate_limit'] = 5;
		}

		// Debug mode
		$sanitized['debug_mode'] = ! empty( $input['debug_mode'] );

		// Auto-apply
		$sanitized['auto_apply'] = ! empty( $input['auto_apply'] );

		return $sanitized;
	}

	/**
	 * Handle configuration save
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function handle_configuration_save() {
		// This is handled by WordPress Settings API
		// Just show a success message
		add_settings_error(
			'sao_settings',
			'settings_updated',
			esc_html__( 'Settings saved successfully.', 'seo-auto-optimizer' ),
			'updated'
		);
	}

	/**
	 * Add settings fields
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function add_settings_fields() {
		// General section callbacks
		add_settings_field(
			'keywords_count',
			esc_html__( 'Keywords Count', 'seo-auto-optimizer' ),
			array( $this, 'keywords_count_callback' ),
			'sao_general_settings',
			'sao_general_section'
		);

		// AI section callbacks
		add_settings_field(
			'ai_providers',
			esc_html__( 'AI Providers', 'seo-auto-optimizer' ),
			array( $this, 'ai_providers_callback' ),
			'sao_ai_settings',
			'sao_ai_section'
		);

		// Advanced section callbacks
		add_settings_field(
			'cache_settings',
			esc_html__( 'Cache Settings', 'seo-auto-optimizer' ),
			array( $this, 'cache_settings_callback' ),
			'sao_advanced_settings',
			'sao_advanced_section'
		);
	}

	/**
	 * General section callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function general_section_callback() {
		echo '<p>' . esc_html__( 'Configure general plugin settings.', 'seo-auto-optimizer' ) . '</p>';
	}

	/**
	 * AI section callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ai_section_callback() {
		echo '<p>' . esc_html__( 'Configure AI providers and API keys.', 'seo-auto-optimizer' ) . '</p>';
	}

	/**
	 * Advanced section callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function advanced_section_callback() {
		echo '<p>' . esc_html__( 'Advanced configuration options.', 'seo-auto-optimizer' ) . '</p>';
	}

	/**
	 * Keywords count callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function keywords_count_callback() {
		// This is handled in render_general_settings
	}

	/**
	 * AI providers callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ai_providers_callback() {
		// This is handled in render_ai_settings
	}

	/**
	 * Cache settings callback
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function cache_settings_callback() {
		// This is handled in render_advanced_settings
	}

	/**
	 * Save all settings with proper sanitization and validation
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function save_all_settings() {
		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions.', 'seo-auto-optimizer' ) );
		}

		// Paramètres généraux avec validation stricte
		$keywords_count = absint( $_POST['keywords_count'] ?? 8 );
		$keywords_count = max( 5, min( 15, $keywords_count ) ); // Force entre 5 et 15

		$log_level = sanitize_text_field( $_POST['log_level'] ?? 'errors' );
		$valid_log_levels = array( 'none', 'errors', 'debug', 'all' );
		if ( ! in_array( $log_level, $valid_log_levels, true ) ) {
			$log_level = 'errors';
		}

		$general_options = array(
			'keywords_count' => $keywords_count,
			'primary_seo_plugin' => sanitize_key( $_POST['primary_seo_plugin'] ?? '' ),
			'backup_enabled' => ! empty( $_POST['backup_enabled'] ),
			'log_level' => $log_level,
		);
		update_option( 'seo_auto_optimizer_general_options', $general_options );

		// Paramètres AI avec validation des clés API
		$ai_options = array(
			'gemini_api_key' => $this->sanitize_api_key( $_POST['gemini_api_key'] ?? '' ),
			'openai_api_key' => $this->sanitize_api_key( $_POST['openai_api_key'] ?? '' ),
			'anthropic_api_key' => $this->sanitize_api_key( $_POST['anthropic_api_key'] ?? '' ),
			'ollama_endpoint' => $this->sanitize_url_endpoint( $_POST['ollama_endpoint'] ?? '' ),
			'ollama_model' => sanitize_text_field( $_POST['ollama_model'] ?? '' ),
			'custom_endpoint' => $this->sanitize_url_endpoint( $_POST['custom_endpoint'] ?? '' ),
			'primary_provider' => sanitize_key( $_POST['primary_provider'] ?? 'gemini' ),
		);
		update_option( 'seo_auto_optimizer_ai_options', $ai_options );

		// Paramètres avancés avec validation des limites
		$cache_duration = absint( $_POST['cache_duration'] ?? 86400 );
		$cache_duration = max( 3600, min( 172800, $cache_duration ) ); // Entre 1h et 48h

		$rate_limit = absint( $_POST['rate_limit'] ?? 5 );
		$rate_limit = max( 1, min( 20, $rate_limit ) ); // Entre 1 et 20

		$advanced_options = array(
			'cache_duration' => $cache_duration,
			'rate_limit' => $rate_limit,
			'debug_mode' => ! empty( $_POST['debug_mode'] ),
			'auto_optimize' => ! empty( $_POST['auto_optimize'] ),
		);
		update_option( 'seo_auto_optimizer_advanced_options', $advanced_options );
	}

	/**
	 * Sanitize API key input
	 *
	 * @since 1.0.0
	 * @param string $api_key Raw API key input
	 * @return string Sanitized API key
	 */
	private function sanitize_api_key( $api_key ) {
		$api_key = sanitize_text_field( $api_key );

		// Don't save if it's masked (contains asterisks)
		if ( strpos( $api_key, '*' ) !== false ) {
			// Get existing key
			$existing_options = get_option( 'seo_auto_optimizer_ai_options', array() );
			return $existing_options[ debug_backtrace()[1]['args'][0] ] ?? '';
		}

		// Validate API key format (basic check)
		if ( ! empty( $api_key ) && strlen( $api_key ) < 10 ) {
			return ''; // Too short to be valid
		}

		return $api_key;
	}

	/**
	 * Sanitize URL endpoint
	 *
	 * @since 1.0.0
	 * @param string $url Raw URL input
	 * @return string Sanitized URL
	 */
	private function sanitize_url_endpoint( $url ) {
		$url = esc_url_raw( $url );

		// Validate URL format
		if ( ! empty( $url ) && ! filter_var( $url, FILTER_VALIDATE_URL ) ) {
			return '';
		}

		return $url;
	}
}

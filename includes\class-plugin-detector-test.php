<?php
/**
 * SEO Plugin Detector Test Class
 *
 * This class provides testing functionality for the SEO plugin detector
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * SEO Plugin Detector Test Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Plugin_Detector_Test {

	/**
	 * Run all detector tests
	 *
	 * @since 1.0.0
	 * @return array Test results
	 */
	public static function run_tests() {
		$results = array(
			'passed' => 0,
			'failed' => 0,
			'tests'  => array(),
		);

		// Test detector initialization
		$results['tests']['initialization'] = self::test_detector_initialization();
		if ( $results['tests']['initialization']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Test plugin detection methods
		$results['tests']['detection_methods'] = self::test_detection_methods();
		if ( $results['tests']['detection_methods']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Test cache functionality
		$results['tests']['cache_functionality'] = self::test_cache_functionality();
		if ( $results['tests']['cache_functionality']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Test security measures
		$results['tests']['security_measures'] = self::test_security_measures();
		if ( $results['tests']['security_measures']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		// Test admin interface
		$results['tests']['admin_interface'] = self::test_admin_interface();
		if ( $results['tests']['admin_interface']['status'] ) {
			$results['passed']++;
		} else {
			$results['failed']++;
		}

		return $results;
	}

	/**
	 * Test detector initialization
	 *
	 * @since 1.0.0
	 * @return array Test result
	 */
	private static function test_detector_initialization() {
		try {
			// Test singleton pattern
			$detector1 = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
			$detector2 = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			if ( $detector1 !== $detector2 ) {
				return array(
					'status'  => false,
					'message' => 'Singleton pattern not working correctly',
				);
			}

			// Test class exists and has required methods
			$required_methods = array(
				'get_active_seo_plugins',
				'get_primary_seo_plugin',
				'is_plugin_active',
				'clear_cache',
				'render_admin_interface',
			);

			foreach ( $required_methods as $method ) {
				if ( ! method_exists( $detector1, $method ) ) {
					return array(
						'status'  => false,
						'message' => "Required method '{$method}' not found",
					);
				}
			}

			return array(
				'status'  => true,
				'message' => 'Detector initialization successful',
			);

		} catch ( Exception $e ) {
			return array(
				'status'  => false,
				'message' => 'Exception during initialization: ' . $e->getMessage(),
			);
		}
	}

	/**
	 * Test detection methods
	 *
	 * @since 1.0.0
	 * @return array Test result
	 */
	private static function test_detection_methods() {
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Test get_active_seo_plugins returns array
			$active_plugins = $detector->get_active_seo_plugins();
			if ( ! is_array( $active_plugins ) ) {
				return array(
					'status'  => false,
					'message' => 'get_active_seo_plugins() does not return array',
				);
			}

			// Test get_primary_seo_plugin returns null or array
			$primary_plugin = $detector->get_primary_seo_plugin();
			if ( ! is_null( $primary_plugin ) && ! is_array( $primary_plugin ) ) {
				return array(
					'status'  => false,
					'message' => 'get_primary_seo_plugin() returns invalid type',
				);
			}

			// Test is_plugin_active with known plugin keys
			$known_plugins = array( 'rank_math', 'yoast', 'seopress', 'aioseo' );
			foreach ( $known_plugins as $plugin_key ) {
				$result = $detector->is_plugin_active( $plugin_key );
				if ( ! is_bool( $result ) ) {
					return array(
						'status'  => false,
						'message' => "is_plugin_active('{$plugin_key}') does not return boolean",
					);
				}
			}

			// Test with invalid plugin key
			$invalid_result = $detector->is_plugin_active( 'invalid_plugin_key' );
			if ( $invalid_result !== false ) {
				return array(
					'status'  => false,
					'message' => 'is_plugin_active() should return false for invalid plugin key',
				);
			}

			return array(
				'status'  => true,
				'message' => 'Detection methods working correctly',
			);

		} catch ( Exception $e ) {
			return array(
				'status'  => false,
				'message' => 'Exception during detection methods test: ' . $e->getMessage(),
			);
		}
	}

	/**
	 * Test cache functionality
	 *
	 * @since 1.0.0
	 * @return array Test result
	 */
	private static function test_cache_functionality() {
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Clear cache first
			$detector->clear_cache();

			// Get plugins (should create cache)
			$plugins1 = $detector->get_active_seo_plugins();

			// Get plugins again (should use cache)
			$plugins2 = $detector->get_active_seo_plugins();

			// Results should be identical
			if ( $plugins1 !== $plugins2 ) {
				return array(
					'status'  => false,
					'message' => 'Cache not working - results differ between calls',
				);
			}

			// Force refresh should work
			$plugins3 = $detector->get_active_seo_plugins( true );
			if ( ! is_array( $plugins3 ) ) {
				return array(
					'status'  => false,
					'message' => 'Force refresh not working correctly',
				);
			}

			return array(
				'status'  => true,
				'message' => 'Cache functionality working correctly',
			);

		} catch ( Exception $e ) {
			return array(
				'status'  => false,
				'message' => 'Exception during cache test: ' . $e->getMessage(),
			);
		}
	}

	/**
	 * Test security measures
	 *
	 * @since 1.0.0
	 * @return array Test result
	 */
	private static function test_security_measures() {
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Test that methods handle missing capabilities gracefully
			// Note: In a real test environment, we would mock user capabilities
			
			// Test that get_active_seo_plugins returns empty array for non-admin users
			// This test assumes current user has manage_options capability
			$plugins = $detector->get_active_seo_plugins();
			if ( ! is_array( $plugins ) ) {
				return array(
					'status'  => false,
					'message' => 'Security check failed - invalid return type',
				);
			}

			// Test that admin interface can be rendered
			$admin_html = $detector->render_admin_interface();
			if ( ! is_string( $admin_html ) || empty( $admin_html ) ) {
				return array(
					'status'  => false,
					'message' => 'Admin interface rendering failed',
				);
			}

			// Check for XSS protection in admin interface
			if ( strpos( $admin_html, '<script>' ) !== false ) {
				return array(
					'status'  => false,
					'message' => 'Potential XSS vulnerability in admin interface',
				);
			}

			return array(
				'status'  => true,
				'message' => 'Security measures working correctly',
			);

		} catch ( Exception $e ) {
			return array(
				'status'  => false,
				'message' => 'Exception during security test: ' . $e->getMessage(),
			);
		}
	}

	/**
	 * Test admin interface
	 *
	 * @since 1.0.0
	 * @return array Test result
	 */
	private static function test_admin_interface() {
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Test admin interface rendering
			$admin_html = $detector->render_admin_interface();

			// Check for required HTML elements
			$required_elements = array(
				'seo-plugin-detector-admin',
				'seo-detector-header',
				'seo-refresh-plugins',
				'seo-detected-plugins',
				'seo-compatibility-info',
			);

			foreach ( $required_elements as $element ) {
				if ( strpos( $admin_html, $element ) === false ) {
					return array(
						'status'  => false,
						'message' => "Required HTML element '{$element}' not found in admin interface",
					);
				}
			}

			// Check for proper escaping
			if ( strpos( $admin_html, 'esc_html' ) !== false || strpos( $admin_html, 'esc_attr' ) !== false ) {
				return array(
					'status'  => false,
					'message' => 'Unescaped output found in admin interface',
				);
			}

			// Test statistics method
			$stats = $detector->get_plugin_statistics();
			if ( ! is_array( $stats ) ) {
				return array(
					'status'  => false,
					'message' => 'get_plugin_statistics() does not return array',
				);
			}

			$required_stats = array( 'total_detected', 'primary_plugin', 'has_conflicts', 'last_checked' );
			foreach ( $required_stats as $stat ) {
				if ( ! array_key_exists( $stat, $stats ) ) {
					return array(
						'status'  => false,
						'message' => "Required statistic '{$stat}' not found",
					);
				}
			}

			return array(
				'status'  => true,
				'message' => 'Admin interface working correctly',
			);

		} catch ( Exception $e ) {
			return array(
				'status'  => false,
				'message' => 'Exception during admin interface test: ' . $e->getMessage(),
			);
		}
	}

	/**
	 * Generate test report
	 *
	 * @since 1.0.0
	 * @return string HTML test report
	 */
	public static function generate_test_report() {
		$test_results = self::run_tests();

		$html = '<div class="seo-detector-test-report">';
		$html .= '<h3>SEO Plugin Detector Test Report</h3>';
		$html .= '<p><strong>Passed:</strong> ' . $test_results['passed'] . ' | ';
		$html .= '<strong>Failed:</strong> ' . $test_results['failed'] . '</p>';

		$html .= '<ul>';
		foreach ( $test_results['tests'] as $test_name => $test_result ) {
			$status_icon = $test_result['status'] ? '✅' : '❌';
			$html .= '<li>' . $status_icon . ' ' . ucwords( str_replace( '_', ' ', $test_name ) ) . ': ' . $test_result['message'] . '</li>';
		}
		$html .= '</ul>';

		$html .= '</div>';

		return $html;
	}
}

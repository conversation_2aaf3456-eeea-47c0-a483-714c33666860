/**
 * SEO Auto Optimizer - Boss Optimization JavaScript
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Boss Optimization Interface
     */
    var SAOBossOptimization = {
        
        /**
         * Current filters
         */
        filters: {
            type: '',
            status: '',
            seo_status: '',
            search: ''
        },
        
        /**
         * Current sorting
         */
        sort: {
            column: 'title',
            order: 'ASC'
        },
        
        /**
         * Current page
         */
        currentPage: 1,
        
        /**
         * Items per page
         */
        perPage: 25,
        
        /**
         * Selected items
         */
        selectedItems: [],
        
        /**
         * Optimization queue
         */
        optimizationQueue: [],
        
        /**
         * Optimization in progress
         */
        optimizationInProgress: false,
        
        /**
         * Auto refresh timer
         */
        autoRefreshTimer: null,
        
        /**
         * Optimization start time
         */
        optimizationStartTime: null,
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.loadContent();
            this.initKeyboardShortcuts();
            this.startAutoRefresh();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Filters
            $(document).on('change', '.sao-filter', this.handleFilterChange.bind(this));
            $(document).on('keyup', '#sao-filter-search', this.debounce(this.handleSearchChange.bind(this), 500));
            
            // Bulk actions
            $(document).on('change', '.sao-select-all, .sao-select-all-header', this.handleSelectAll.bind(this));
            $(document).on('change', '.sao-item-checkbox', this.handleItemSelect.bind(this));
            $(document).on('click', '#sao-bulk-optimize', this.handleBulkOptimize.bind(this));
            $(document).on('click', '#sao-optimize-all', this.handleOptimizeAll.bind(this));
            $(document).on('click', '#sao-refresh-table', this.handleRefresh.bind(this));
            
            // Individual optimization
            $(document).on('click', '.sao-optimize-btn', this.handleSingleOptimize.bind(this));
            
            // Table sorting
            $(document).on('click', '.sao-content-table th.sortable', this.handleSort.bind(this));
            
            // Pagination
            $(document).on('click', '.sao-pagination-btn', this.handlePagination.bind(this));
            
            // Modal
            $(document).on('click', '.sao-modal-close', this.closeModal.bind(this));
            $(document).on('click', '.sao-stop-optimization', this.stopOptimization.bind(this));
            
            // Prevent modal close on content click
            $(document).on('click', '.sao-modal-content', function(e) {
                e.stopPropagation();
            });
            
            // Close modal on background click
            $(document).on('click', '.sao-modal', this.closeModal.bind(this));
        },
        
        /**
         * Initialize keyboard shortcuts
         */
        initKeyboardShortcuts: function() {
            $(document).on('keydown', this.handleKeyboardShortcuts.bind(this));
            
            // Show shortcuts indicator
            this.showKeyboardShortcuts();
        },
        
        /**
         * Handle keyboard shortcuts
         */
        handleKeyboardShortcuts: function(e) {
            // Ctrl+A - Select all
            if (e.ctrlKey && e.key === 'a' && !$(e.target).is('input, textarea')) {
                e.preventDefault();
                this.selectAll();
            }
            
            // Ctrl+D - Deselect all
            if (e.ctrlKey && e.key === 'd' && !$(e.target).is('input, textarea')) {
                e.preventDefault();
                this.deselectAll();
            }
            
            // Ctrl+O - Optimize selected
            if (e.ctrlKey && e.key === 'o' && !$(e.target).is('input, textarea')) {
                e.preventDefault();
                if (this.selectedItems.length > 0) {
                    this.handleBulkOptimize();
                }
            }
            
            // Escape - Close modal
            if (e.key === 'Escape') {
                this.closeModal();
            }
            
            // F5 - Refresh
            if (e.key === 'F5') {
                e.preventDefault();
                this.handleRefresh();
            }
        },
        
        /**
         * Show keyboard shortcuts indicator
         */
        showKeyboardShortcuts: function() {
            var shortcuts = $('<div class="sao-keyboard-shortcuts">' +
                '<strong>Keyboard Shortcuts:</strong><br>' +
                '<kbd>Ctrl+A</kbd> Select All &nbsp; ' +
                '<kbd>Ctrl+D</kbd> Deselect All &nbsp; ' +
                '<kbd>Ctrl+O</kbd> Optimize Selected &nbsp; ' +
                '<kbd>F5</kbd> Refresh' +
                '</div>');
            
            $('body').append(shortcuts);
            
            // Show for 3 seconds
            setTimeout(function() {
                shortcuts.addClass('show');
            }, 1000);
            
            setTimeout(function() {
                shortcuts.removeClass('show');
            }, 4000);
        },
        
        /**
         * Start auto refresh
         */
        startAutoRefresh: function() {
            if (saoBoss.settings.autoRefresh > 0) {
                this.autoRefreshTimer = setInterval(function() {
                    if (!SAOBossOptimization.optimizationInProgress) {
                        SAOBossOptimization.loadContent(false); // Silent refresh
                    }
                }, saoBoss.settings.autoRefresh);
            }
        },
        
        /**
         * Stop auto refresh
         */
        stopAutoRefresh: function() {
            if (this.autoRefreshTimer) {
                clearInterval(this.autoRefreshTimer);
                this.autoRefreshTimer = null;
            }
        },
        
        /**
         * Handle filter change
         */
        handleFilterChange: function(e) {
            var $filter = $(e.target);
            var filterName = $filter.attr('id').replace('sao-filter-', '');
            
            if (filterName === 'per-page') {
                this.perPage = parseInt($filter.val());
            } else {
                this.filters[filterName] = $filter.val();
            }
            
            this.currentPage = 1;
            this.loadContent();
        },
        
        /**
         * Handle search change
         */
        handleSearchChange: function(e) {
            this.filters.search = $(e.target).val();
            this.currentPage = 1;
            this.loadContent();
        },
        
        /**
         * Handle select all
         */
        handleSelectAll: function(e) {
            var isChecked = $(e.target).is(':checked');
            
            if (isChecked) {
                this.selectAll();
            } else {
                this.deselectAll();
            }
        },
        
        /**
         * Select all items
         */
        selectAll: function() {
            $('.sao-item-checkbox').prop('checked', true);
            $('.sao-select-all, .sao-select-all-header').prop('checked', true);
            this.updateSelectedItems();
        },
        
        /**
         * Deselect all items
         */
        deselectAll: function() {
            $('.sao-item-checkbox').prop('checked', false);
            $('.sao-select-all, .sao-select-all-header').prop('checked', false);
            this.updateSelectedItems();
        },
        
        /**
         * Handle item select
         */
        handleItemSelect: function(e) {
            this.updateSelectedItems();
        },
        
        /**
         * Update selected items
         */
        updateSelectedItems: function() {
            this.selectedItems = [];
            
            $('.sao-item-checkbox:checked').each(function() {
                SAOBossOptimization.selectedItems.push(parseInt($(this).val()));
            });
            
            // Update UI
            var count = this.selectedItems.length;
            $('.sao-selected-count').text(count + ' ' + saoBoss.strings.selected);
            
            // Enable/disable bulk actions
            $('#sao-bulk-optimize').prop('disabled', count === 0);
            
            // Update select all checkbox state
            var totalCheckboxes = $('.sao-item-checkbox').length;
            var checkedCheckboxes = $('.sao-item-checkbox:checked').length;
            
            if (checkedCheckboxes === 0) {
                $('.sao-select-all, .sao-select-all-header').prop('indeterminate', false).prop('checked', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                $('.sao-select-all, .sao-select-all-header').prop('indeterminate', false).prop('checked', true);
            } else {
                $('.sao-select-all, .sao-select-all-header').prop('indeterminate', true);
            }
        },
        
        /**
         * Handle bulk optimize
         */
        handleBulkOptimize: function(e) {
            if (e) e.preventDefault();
            
            if (this.selectedItems.length === 0) {
                this.showNotice(saoBoss.strings.noSelection, 'error');
                return;
            }
            
            if (this.optimizationInProgress) {
                this.showNotice(saoBoss.strings.processing, 'info');
                return;
            }
            
            this.startBulkOptimization(this.selectedItems);
        },
        
        /**
         * Handle optimize all
         */
        handleOptimizeAll: function(e) {
            e.preventDefault();
            
            if (!confirm(saoBoss.strings.confirmOptimizeAll)) {
                return;
            }
            
            if (this.optimizationInProgress) {
                this.showNotice(saoBoss.strings.processing, 'info');
                return;
            }
            
            // Get all item IDs from current view
            var allIds = [];
            $('.sao-item-checkbox').each(function() {
                allIds.push(parseInt($(this).val()));
            });
            
            if (allIds.length === 0) {
                this.showNotice(saoBoss.strings.noSelection, 'error');
                return;
            }
            
            this.startBulkOptimization(allIds);
        },
        
        /**
         * Handle single optimize
         */
        handleSingleOptimize: function(e) {
            e.preventDefault();
            
            var $btn = $(e.target).closest('.sao-optimize-btn');
            var postId = parseInt($btn.data('post-id'));
            
            if (!postId) {
                this.showNotice(saoBoss.strings.error, 'error');
                return;
            }
            
            this.optimizeSingle(postId, $btn);
        },
        
        /**
         * Handle refresh
         */
        handleRefresh: function(e) {
            if (e) e.preventDefault();
            this.loadContent();
        },
        
        /**
         * Handle sort
         */
        handleSort: function(e) {
            var $th = $(e.target).closest('th');
            var column = $th.data('sort');
            
            if (!column) return;
            
            // Toggle order if same column
            if (this.sort.column === column) {
                this.sort.order = this.sort.order === 'ASC' ? 'DESC' : 'ASC';
            } else {
                this.sort.column = column;
                this.sort.order = 'ASC';
            }
            
            this.loadContent();
        },
        
        /**
         * Handle pagination
         */
        handlePagination: function(e) {
            e.preventDefault();
            
            var $btn = $(e.target).closest('.sao-pagination-btn');
            var page = parseInt($btn.data('page'));
            
            if (page && page !== this.currentPage) {
                this.currentPage = page;
                this.loadContent();
            }
        },
        
        /**
         * Load content
         */
        loadContent: function(showLoading) {
            if (showLoading !== false) {
                this.showTableLoading();
            }
            
            $.post(saoBoss.ajaxUrl, {
                action: 'sao_boss_get_content',
                nonce: saoBoss.nonce,
                page: this.currentPage,
                per_page: this.perPage,
                filters: this.filters,
                sort: this.sort.column,
                order: this.sort.order
            })
            .done(this.handleContentLoaded.bind(this))
            .fail(this.handleContentLoadError.bind(this));
        },
        
        /**
         * Show table loading
         */
        showTableLoading: function() {
            $('.sao-content-table').hide();
            $('.sao-pagination-container').hide();
            $('.sao-table-loading').show();
        },
        
        /**
         * Handle content loaded
         */
        handleContentLoaded: function(response) {
            if (response.success) {
                this.renderTable(response.data);
                this.renderPagination(response.data);
                this.updateSortIndicators();
            } else {
                this.showNotice(response.data.message || saoBoss.strings.error, 'error');
            }
            
            $('.sao-table-loading').hide();
            $('.sao-content-table').show();
            $('.sao-pagination-container').show();
        },
        
        /**
         * Handle content load error
         */
        handleContentLoadError: function() {
            this.showNotice(saoBoss.strings.error, 'error');
            $('.sao-table-loading').hide();
        },
        
        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        /**
         * Show notice
         */
        showNotice: function(message, type) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.sao-boss-wrap h1').after($notice);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        /**
         * Render table
         */
        renderTable: function(data) {
            var tbody = $('#sao-content-tbody');
            tbody.empty();

            if (data.items.length === 0) {
                tbody.append('<tr><td colspan="8" style="text-align: center; padding: 40px; color: #646970;">' +
                    'No content found matching your criteria.' + '</td></tr>');
                return;
            }

            $.each(data.items, function(index, item) {
                var row = SAOBossOptimization.renderTableRow(item);
                tbody.append(row);
            });

            // Update selected items after rendering
            this.updateSelectedItems();
        },

        /**
         * Render table row
         */
        renderTableRow: function(item) {
            var seoStatusClass = this.getSeoStatusClass(item.seo_status);
            var seoStatusLabel = this.getSeoStatusLabel(item.seo_status);
            var statusClass = this.getStatusClass(item.status);
            var statusLabel = this.getStatusLabel(item.status);
            var postTypeIcon = this.getPostTypeIcon(item.type);
            var postTypeLabel = this.getPostTypeLabel(item.type);

            var row = '<tr data-post-id="' + item.id + '">';

            // Checkbox
            row += '<td class="sao-col-select">';
            row += '<input type="checkbox" class="sao-item-checkbox" value="' + item.id + '" />';
            row += '</td>';

            // Title
            row += '<td class="sao-col-title">';
            if (item.edit_link) {
                row += '<a href="' + item.edit_link + '" target="_blank"><strong>' + this.escapeHtml(item.title) + '</strong></a>';
            } else {
                row += '<strong>' + this.escapeHtml(item.title) + '</strong>';
            }
            row += '</td>';

            // Type
            row += '<td class="sao-col-type">';
            row += '<div class="sao-post-type">';
            row += '<span class="dashicons ' + postTypeIcon + '"></span>';
            row += '<span>' + postTypeLabel + '</span>';
            row += '</div>';
            row += '</td>';

            // Status
            row += '<td class="sao-col-status">';
            row += '<span class="sao-status-badge ' + statusClass + '">' + statusLabel + '</span>';
            row += '</td>';

            // SEO Status
            row += '<td class="sao-col-seo-status">';
            row += '<span class="sao-status-badge ' + seoStatusClass + '">' + seoStatusLabel + '</span>';
            row += '</td>';

            // Keywords
            row += '<td class="sao-col-keywords" style="text-align: center;">';
            row += item.keywords_count || '0';
            row += '</td>';

            // Last Optimized
            row += '<td class="sao-col-last-optimized">';
            row += item.last_optimized || '-';
            row += '</td>';

            // Actions
            row += '<td class="sao-col-actions">';
            row += '<div class="sao-action-buttons">';

            var optimizeDisabled = item.seo_status === 'in_progress' ? ' disabled' : '';
            var optimizeText = item.seo_status === 'in_progress' ? 'Optimizing...' : 'Optimize';

            row += '<button type="button" class="sao-action-btn sao-optimize-btn" data-post-id="' + item.id + '"' + optimizeDisabled + '>';
            row += '<span class="dashicons dashicons-update"></span>' + optimizeText;
            row += '</button>';

            if (item.view_link) {
                row += '<a href="' + item.view_link + '" target="_blank" class="sao-action-btn sao-view-btn">';
                row += '<span class="dashicons dashicons-visibility"></span>View';
                row += '</a>';
            }

            if (item.edit_link) {
                row += '<a href="' + item.edit_link + '" target="_blank" class="sao-action-btn sao-edit-btn">';
                row += '<span class="dashicons dashicons-edit"></span>Edit';
                row += '</a>';
            }

            row += '</div>';
            row += '</td>';

            row += '</tr>';

            return row;
        },

        /**
         * Render pagination
         */
        renderPagination: function(data) {
            var container = $('.sao-pagination-container');
            var info = $('.sao-pagination-info');
            var controls = $('.sao-pagination-controls');

            // Update info
            var start = ((data.current_page - 1) * data.per_page) + 1;
            var end = Math.min(data.current_page * data.per_page, data.total_items);

            info.text('Showing ' + start + ' to ' + end + ' of ' + data.total_items + ' items');

            // Update controls
            controls.empty();

            if (data.total_pages > 1) {
                // Previous button
                if (data.current_page > 1) {
                    controls.append('<button type="button" class="button sao-pagination-btn" data-page="' + (data.current_page - 1) + '">‹ Previous</button>');
                }

                // Page numbers
                var startPage = Math.max(1, data.current_page - 2);
                var endPage = Math.min(data.total_pages, data.current_page + 2);

                if (startPage > 1) {
                    controls.append('<button type="button" class="button sao-pagination-btn" data-page="1">1</button>');
                    if (startPage > 2) {
                        controls.append('<span class="sao-pagination-dots">...</span>');
                    }
                }

                for (var i = startPage; i <= endPage; i++) {
                    var activeClass = i === data.current_page ? ' button-primary' : '';
                    controls.append('<button type="button" class="button sao-pagination-btn' + activeClass + '" data-page="' + i + '">' + i + '</button>');
                }

                if (endPage < data.total_pages) {
                    if (endPage < data.total_pages - 1) {
                        controls.append('<span class="sao-pagination-dots">...</span>');
                    }
                    controls.append('<button type="button" class="button sao-pagination-btn" data-page="' + data.total_pages + '">' + data.total_pages + '</button>');
                }

                // Next button
                if (data.current_page < data.total_pages) {
                    controls.append('<button type="button" class="button sao-pagination-btn" data-page="' + (data.current_page + 1) + '">Next ›</button>');
                }
            }
        },

        /**
         * Update sort indicators
         */
        updateSortIndicators: function() {
            $('.sao-content-table th').removeClass('sorted-asc sorted-desc');
            $('.sao-content-table th[data-sort="' + this.sort.column + '"]').addClass('sorted-' + this.sort.order.toLowerCase());
        },

        /**
         * Start bulk optimization
         */
        startBulkOptimization: function(postIds) {
            this.optimizationQueue = postIds.slice(); // Copy array
            this.optimizationInProgress = true;
            this.optimizationStartTime = Date.now();

            // Show progress modal
            this.showProgressModal();

            // Start processing
            this.processBulkOptimization();
        },

        /**
         * Process bulk optimization
         */
        processBulkOptimization: function() {
            if (this.optimizationQueue.length === 0) {
                this.completeBulkOptimization();
                return;
            }

            var postId = this.optimizationQueue.shift();
            this.updateProgressModal(postId);

            // Optimize single item
            $.post(saoBoss.ajaxUrl, {
                action: 'sao_boss_optimize',
                nonce: saoBoss.nonce,
                post_id: postId
            })
            .done(this.handleBulkOptimizationResult.bind(this, postId, true))
            .fail(this.handleBulkOptimizationResult.bind(this, postId, false))
            .always(function() {
                // Continue with next item after a short delay
                setTimeout(function() {
                    SAOBossOptimization.processBulkOptimization();
                }, 500);
            });
        },

        /**
         * Handle bulk optimization result
         */
        handleBulkOptimizationResult: function(postId, success, response) {
            // Update progress
            this.updateBulkOptimizationProgress(postId, success, response);

            // Update table row
            this.updateTableRow(postId, success);
        },

        /**
         * Complete bulk optimization
         */
        completeBulkOptimization: function() {
            this.optimizationInProgress = false;

            // Update progress modal
            $('.sao-current-item-title').text('Completed!');
            $('.sao-current-item-status').text('All optimizations finished');
            $('.sao-stop-optimization').hide();

            // Show completion message
            this.showNotice(saoBoss.strings.completed, 'success');

            // Refresh content after a delay
            setTimeout(function() {
                SAOBossOptimization.loadContent();
            }, 2000);
        },

        /**
         * Optimize single item
         */
        optimizeSingle: function(postId, $btn) {
            var originalText = $btn.text();

            $btn.prop('disabled', true).text(saoBoss.strings.optimizing);

            $.post(saoBoss.ajaxUrl, {
                action: 'sao_boss_optimize',
                nonce: saoBoss.nonce,
                post_id: postId
            })
            .done(function(response) {
                if (response.success) {
                    $btn.text(saoBoss.strings.optimized);
                    SAOBossOptimization.updateTableRow(postId, true);
                    SAOBossOptimization.showNotice(response.data.message, 'success');
                } else {
                    $btn.text(saoBoss.strings.failed);
                    SAOBossOptimization.showNotice(response.data.message || saoBoss.strings.error, 'error');
                }
            })
            .fail(function() {
                $btn.text(saoBoss.strings.failed);
                SAOBossOptimization.showNotice(saoBoss.strings.error, 'error');
            })
            .always(function() {
                setTimeout(function() {
                    $btn.prop('disabled', false).text(originalText);
                }, 2000);
            });
        },

        /**
         * Show progress modal
         */
        showProgressModal: function() {
            var modal = $('#sao-progress-modal');

            // Reset modal state
            $('.sao-progress-bar-fill').css('width', '0%');
            $('.sao-progress-text').text('0 / 0 (0%)');
            $('.sao-current-item-title').text('-');
            $('.sao-current-item-status').text('-');
            $('.sao-success-count').text('0');
            $('.sao-error-count').text('0');
            $('.sao-keywords-count').text('0');
            $('.sao-time-elapsed').text('0s');
            $('.sao-activity-log').empty();
            $('.sao-stop-optimization').show();

            modal.show();
        },

        /**
         * Update progress modal
         */
        updateProgressModal: function(postId) {
            var post = this.getPostData(postId);
            var totalItems = this.optimizationQueue.length + 1; // +1 for current item
            var completedItems = (this.optimizationQueue.length === 0) ? totalItems : totalItems - this.optimizationQueue.length - 1;
            var progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

            // Update progress bar
            $('.sao-progress-bar-fill').css('width', progress + '%');
            $('.sao-progress-text').text(completedItems + ' / ' + totalItems + ' (' + Math.round(progress) + '%)');

            // Update current item
            $('.sao-current-item-title').text(post ? post.title : 'Post ID: ' + postId);
            $('.sao-current-item-status').text(saoBoss.strings.optimizing);

            // Update time elapsed
            if (this.optimizationStartTime) {
                var elapsed = Math.round((Date.now() - this.optimizationStartTime) / 1000);
                $('.sao-time-elapsed').text(elapsed + 's');
            }
        },

        /**
         * Update bulk optimization progress
         */
        updateBulkOptimizationProgress: function(postId, success, response) {
            var post = this.getPostData(postId);
            var postTitle = post ? post.title : 'Post ID: ' + postId;

            // Update counters
            if (success) {
                var successCount = parseInt($('.sao-success-count').text()) + 1;
                $('.sao-success-count').text(successCount);

                if (response && response.data && response.data.keywords_count) {
                    var keywordsCount = parseInt($('.sao-keywords-count').text()) + response.data.keywords_count;
                    $('.sao-keywords-count').text(keywordsCount);
                }
            } else {
                var errorCount = parseInt($('.sao-error-count').text()) + 1;
                $('.sao-error-count').text(errorCount);
            }

            // Add to activity log
            var logEntry = '<div class="log-entry log-' + (success ? 'success' : 'error') + '">';
            logEntry += '[' + new Date().toLocaleTimeString() + '] ';
            logEntry += postTitle + ': ' + (success ? 'Success' : 'Failed');
            if (response && response.data && response.data.message) {
                logEntry += ' - ' + response.data.message;
            }
            logEntry += '</div>';

            var activityLog = $('.sao-activity-log');
            activityLog.append(logEntry);
            activityLog.scrollTop(activityLog[0].scrollHeight);
        },

        /**
         * Update table row
         */
        updateTableRow: function(postId, success) {
            var $row = $('tr[data-post-id="' + postId + '"]');

            if ($row.length > 0) {
                // Update SEO status
                var $seoStatus = $row.find('.sao-col-seo-status .sao-status-badge');
                if (success) {
                    $seoStatus.removeClass('sao-seo-not-optimized sao-seo-in-progress')
                             .addClass('sao-seo-optimized')
                             .text(this.getSeoStatusLabel('optimized'));
                } else {
                    $seoStatus.removeClass('sao-seo-in-progress')
                             .addClass('sao-seo-not-optimized')
                             .text(this.getSeoStatusLabel('not_optimized'));
                }

                // Update optimize button
                var $optimizeBtn = $row.find('.sao-optimize-btn');
                $optimizeBtn.prop('disabled', false).text('Optimize');

                // Update last optimized if successful
                if (success) {
                    var now = new Date();
                    $row.find('.sao-col-last-optimized').text(now.toLocaleDateString());
                }
            }
        },

        /**
         * Close modal
         */
        closeModal: function(e) {
            if (e && $(e.target).closest('.sao-modal-content').length > 0) {
                return; // Don't close if clicking inside modal content
            }

            $('#sao-progress-modal').hide();
        },

        /**
         * Stop optimization
         */
        stopOptimization: function() {
            if (confirm('Are you sure you want to stop the optimization process?')) {
                this.optimizationQueue = [];
                this.optimizationInProgress = false;
                $('.sao-current-item-status').text('Stopped by user');
                $('.sao-stop-optimization').hide();
                this.showNotice('Optimization stopped', 'info');
            }
        },

        /**
         * Get post data from table
         */
        getPostData: function(postId) {
            var $row = $('tr[data-post-id="' + postId + '"]');
            if ($row.length > 0) {
                return {
                    title: $row.find('.sao-col-title a strong, .sao-col-title strong').text()
                };
            }
            return null;
        },

        /**
         * Get SEO status class
         */
        getSeoStatusClass: function(status) {
            var classes = {
                'optimized': 'sao-seo-optimized',
                'not_optimized': 'sao-seo-not-optimized',
                'in_progress': 'sao-seo-in-progress'
            };
            return classes[status] || 'sao-seo-not-optimized';
        },

        /**
         * Get SEO status label
         */
        getSeoStatusLabel: function(status) {
            var labels = {
                'optimized': 'Optimized',
                'not_optimized': 'Not Optimized',
                'in_progress': 'In Progress'
            };
            return labels[status] || 'Unknown';
        },

        /**
         * Get status class
         */
        getStatusClass: function(status) {
            var classes = {
                'publish': 'sao-status-published',
                'draft': 'sao-status-draft',
                'private': 'sao-status-private',
                'pending': 'sao-status-pending'
            };
            return classes[status] || 'sao-status-unknown';
        },

        /**
         * Get status label
         */
        getStatusLabel: function(status) {
            var labels = {
                'publish': 'Published',
                'draft': 'Draft',
                'private': 'Private',
                'pending': 'Pending'
            };
            return labels[status] || status.charAt(0).toUpperCase() + status.slice(1);
        },

        /**
         * Get post type icon
         */
        getPostTypeIcon: function(type) {
            var icons = {
                'post': 'dashicons-admin-post',
                'page': 'dashicons-admin-page',
                'product': 'dashicons-products'
            };
            return icons[type] || 'dashicons-admin-post';
        },

        /**
         * Get post type label
         */
        getPostTypeLabel: function(type) {
            var labels = {
                'post': 'Post',
                'page': 'Page',
                'product': 'Product'
            };
            return labels[type] || type.charAt(0).toUpperCase() + type.slice(1);
        },

        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        if ($('.sao-boss-wrap').length > 0) {
            SAOBossOptimization.init();
        }
    });

    // Make it globally accessible
    window.SAOBossOptimization = SAOBossOptimization;

})(jQuery);

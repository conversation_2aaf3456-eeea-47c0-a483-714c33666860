/**
 * SEO Injection Admin Styles
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

/* Dashboard Layout */
.sao-injection-dashboard {
	display: grid;
	grid-template-columns: 1fr;
	gap: 20px;
	margin-top: 20px;
}

/* Card Styles */
.sao-card {
	background: #fff;
	border: 1px solid #ccd0d4;
	border-radius: 4px;
	padding: 20px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.sao-card h2 {
	margin-top: 0;
	margin-bottom: 15px;
	font-size: 18px;
	color: #23282d;
}

/* Plugin Grid */
.sao-plugin-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 15px;
}

.sao-plugin-card {
	padding: 15px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background: #f9f9f9;
	transition: all 0.3s ease;
}

.sao-plugin-card:hover {
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sao-plugin-active {
	border-color: #00a32a;
	background: #f0f6fc;
}

.sao-plugin-card h3 {
	margin: 0 0 8px 0;
	font-size: 16px;
	color: #23282d;
}

.sao-plugin-version {
	margin: 0 0 10px 0;
	font-size: 13px;
	color: #666;
}

.sao-plugin-status {
	display: inline-block;
	padding: 4px 8px;
	border-radius: 3px;
	font-size: 12px;
	font-weight: 500;
	text-transform: uppercase;
}

.sao-status-active {
	background: #00a32a;
	color: #fff;
}

/* Form Styles */
.sao-form {
	max-width: 600px;
}

.sao-form-row {
	margin-bottom: 20px;
}

.sao-form-row label {
	display: block;
	margin-bottom: 5px;
	font-weight: 600;
	color: #23282d;
}

.sao-form-row input[type="text"],
.sao-form-row input[type="number"],
.sao-form-row select,
.sao-form-row textarea {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
}

.sao-form-row input[type="text"]:focus,
.sao-form-row input[type="number"]:focus,
.sao-form-row select:focus,
.sao-form-row textarea:focus {
	border-color: #007cba;
	box-shadow: 0 0 0 1px #007cba;
	outline: none;
}

.sao-form-row small {
	display: block;
	margin-top: 5px;
	color: #666;
	font-size: 12px;
}

.sao-form-row fieldset {
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 15px;
}

.sao-form-row legend {
	padding: 0 10px;
	font-weight: 600;
	color: #23282d;
}

.sao-form-row fieldset label {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	font-weight: normal;
}

.sao-form-row fieldset input[type="checkbox"] {
	width: auto;
	margin-right: 8px;
}

.sao-form-actions {
	margin-top: 20px;
}

/* Progress Bar */
.sao-progress-container {
	margin-top: 20px;
}

.sao-progress-bar {
	width: 100%;
	height: 20px;
	background: #f0f0f0;
	border-radius: 10px;
	overflow: hidden;
	margin-bottom: 10px;
}

.sao-progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #007cba, #00a32a);
	width: 0%;
	transition: width 0.3s ease;
}

.sao-progress-text {
	text-align: center;
	font-weight: 600;
	color: #23282d;
}

/* Statistics Grid */
.sao-stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 15px;
}

.sao-stat-item {
	text-align: center;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 4px;
	border: 1px solid #e9ecef;
}

.sao-stat-number {
	font-size: 24px;
	font-weight: bold;
	color: #007cba;
	margin-bottom: 5px;
}

.sao-stat-label {
	font-size: 12px;
	color: #666;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

/* Meta Box Styles */
.sao-meta-box {
	padding: 0;
}

.sao-quick-injection {
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid #eee;
}

.sao-quick-injection h4 {
	margin: 0 0 15px 0;
	font-size: 14px;
	color: #23282d;
}

.sao-form-field {
	margin-bottom: 15px;
}

.sao-form-field label {
	display: block;
	margin-bottom: 5px;
	font-size: 12px;
	font-weight: 600;
	color: #23282d;
}

.sao-form-field input[type="text"],
.sao-form-field select,
.sao-form-field textarea {
	width: 100%;
	padding: 6px 8px;
	border: 1px solid #ddd;
	border-radius: 3px;
	font-size: 13px;
}

.sao-form-field textarea {
	resize: vertical;
	min-height: 60px;
}

.sao-char-counter {
	display: block;
	margin-top: 3px;
	font-size: 11px;
	color: #666;
	text-align: right;
}

.sao-char-counter.sao-over-limit {
	color: #d63638;
	font-weight: 600;
}

.sao-form-options {
	margin: 15px 0;
}

.sao-form-options label {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
	font-size: 12px;
	font-weight: normal;
}

.sao-form-options input[type="checkbox"] {
	width: auto;
	margin-right: 6px;
}

.sao-form-actions {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.sao-form-actions .button {
	font-size: 12px;
	padding: 4px 8px;
	height: auto;
	line-height: 1.4;
}

/* Injection History */
.sao-injection-history {
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid #eee;
}

.sao-injection-history h4 {
	margin: 0 0 10px 0;
	font-size: 14px;
	color: #23282d;
}

.sao-history-list {
	max-height: 150px;
	overflow-y: auto;
}

.sao-history-item {
	padding: 8px;
	margin-bottom: 5px;
	background: #f8f9fa;
	border-radius: 3px;
	border: 1px solid #e9ecef;
	position: relative;
}

.sao-history-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 3px;
}

.sao-history-date {
	font-size: 11px;
	color: #666;
}

.sao-history-plugin {
	font-size: 11px;
	color: #007cba;
	font-weight: 600;
	text-transform: uppercase;
}

.sao-history-keyword {
	font-size: 12px;
	color: #23282d;
	margin-bottom: 5px;
	font-weight: 500;
}

.sao-restore-backup {
	font-size: 11px;
	color: #d63638;
	text-decoration: none;
	padding: 2px 4px;
	border-radius: 2px;
	transition: background-color 0.2s;
}

.sao-restore-backup:hover {
	background-color: #d63638;
	color: #fff;
	text-decoration: none;
}

/* Current SEO Data */
.sao-current-seo h4 {
	margin: 0 0 10px 0;
	font-size: 14px;
	color: #23282d;
}

.sao-plugin-seo-data {
	margin-bottom: 15px;
	padding: 10px;
	background: #f8f9fa;
	border-radius: 3px;
	border: 1px solid #e9ecef;
}

.sao-plugin-seo-data h5 {
	margin: 0 0 8px 0;
	font-size: 13px;
	color: #007cba;
	font-weight: 600;
}

.sao-seo-field {
	margin-bottom: 5px;
	font-size: 12px;
}

.sao-seo-field strong {
	color: #23282d;
}

.sao-empty-value {
	color: #999;
	font-style: italic;
}

.sao-has-value {
	color: #23282d;
}

/* Notices */
.sao-notice {
	padding: 10px 15px;
	border-radius: 4px;
	margin-bottom: 15px;
}

.sao-notice-warning {
	background: #fff3cd;
	border: 1px solid #ffeaa7;
	color: #856404;
}

.sao-notice-success {
	background: #d1edff;
	border: 1px solid #007cba;
	color: #004085;
}

.sao-notice-error {
	background: #f8d7da;
	border: 1px solid #f5c6cb;
	color: #721c24;
}

/* Loading States */
.sao-loading {
	opacity: 0.6;
	pointer-events: none;
	position: relative;
}

.sao-loading::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 20px;
	height: 20px;
	margin: -10px 0 0 -10px;
	border: 2px solid #f3f3f3;
	border-top: 2px solid #007cba;
	border-radius: 50%;
	animation: sao-spin 1s linear infinite;
}

@keyframes sao-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
	.sao-plugin-grid {
		grid-template-columns: 1fr;
	}

	.sao-stats-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.sao-form-actions {
		flex-direction: column;
	}

	.sao-form-actions .button {
		width: 100%;
		margin-bottom: 5px;
	}
}

@media (max-width: 480px) {
	.sao-stats-grid {
		grid-template-columns: 1fr;
	}

	.sao-history-meta {
		flex-direction: column;
		align-items: flex-start;
		gap: 3px;
	}
}

/* Animation Classes */
.sao-fade-in {
	animation: saoFadeIn 0.3s ease-in;
}

@keyframes saoFadeIn {
	from { opacity: 0; transform: translateY(-10px); }
	to { opacity: 1; transform: translateY(0); }
}

.sao-slide-up {
	animation: saoSlideUp 0.3s ease-out;
}

@keyframes saoSlideUp {
	from { opacity: 0; transform: translateY(20px); }
	to { opacity: 1; transform: translateY(0); }
}

/* Utility Classes */
.sao-text-center {
	text-align: center;
}

.sao-text-right {
	text-align: right;
}

.sao-mb-0 {
	margin-bottom: 0 !important;
}

.sao-mt-20 {
	margin-top: 20px;
}

.sao-hidden {
	display: none !important;
}

.sao-visible {
	display: block !important;
}
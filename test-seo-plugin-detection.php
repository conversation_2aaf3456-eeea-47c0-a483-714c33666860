<?php
/**
 * Test SEO Plugin Detection
 *
 * This file tests the SEO plugin detection functionality
 * Run this file to verify the detector is working correctly
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	// For testing purposes, we'll simulate WordPress environment
	define( 'ABSPATH', dirname( __FILE__ ) . '/' );
	define( 'WP_DEBUG', true );
	
	// Mock WordPress functions for testing
	if ( ! function_exists( 'is_plugin_active' ) ) {
		function is_plugin_active( $plugin ) {
			// Mock function - always returns false for testing
			return false;
		}
	}
	
	if ( ! function_exists( 'current_user_can' ) ) {
		function current_user_can( $capability ) {
			// Mock function - assume user has all capabilities for testing
			return true;
		}
	}
	
	if ( ! function_exists( 'get_transient' ) ) {
		function get_transient( $key ) {
			return false;
		}
	}
	
	if ( ! function_exists( 'set_transient' ) ) {
		function set_transient( $key, $value, $expiration ) {
			return true;
		}
	}
	
	if ( ! function_exists( 'delete_transient' ) ) {
		function delete_transient( $key ) {
			return true;
		}
	}
	
	if ( ! function_exists( 'esc_html__' ) ) {
		function esc_html__( $text, $domain = 'default' ) {
			return $text;
		}
	}
	
	if ( ! function_exists( 'esc_html' ) ) {
		function esc_html( $text ) {
			return htmlspecialchars( $text, ENT_QUOTES, 'UTF-8' );
		}
	}
	
	if ( ! function_exists( 'esc_attr' ) ) {
		function esc_attr( $text ) {
			return htmlspecialchars( $text, ENT_QUOTES, 'UTF-8' );
		}
	}
	
	if ( ! function_exists( 'esc_url' ) ) {
		function esc_url( $url ) {
			return filter_var( $url, FILTER_SANITIZE_URL );
		}
	}
	
	if ( ! function_exists( 'sanitize_text_field' ) ) {
		function sanitize_text_field( $text ) {
			return strip_tags( $text );
		}
	}
	
	if ( ! function_exists( 'sanitize_key' ) ) {
		function sanitize_key( $key ) {
			return preg_replace( '/[^a-z0-9_\-]/', '', strtolower( $key ) );
		}
	}
	
	if ( ! function_exists( 'current_time' ) ) {
		function current_time( $type ) {
			return time();
		}
	}
	
	if ( ! function_exists( 'wp_create_nonce' ) ) {
		function wp_create_nonce( $action ) {
			return 'test_nonce_' . md5( $action );
		}
	}
	
	if ( ! function_exists( 'error_log' ) ) {
		function error_log( $message ) {
			// Mock error logging
			return true;
		}
	}
	
	// Define constants
	if ( ! defined( 'WP_PLUGIN_DIR' ) ) {
		define( 'WP_PLUGIN_DIR', dirname( __FILE__ ) . '/wp-content/plugins' );
	}
}

// Include the main plugin file
require_once 'seo-auto-optimizer.php';

/**
 * Test SEO Plugin Detection
 */
class SEO_Plugin_Detection_Test {

	/**
	 * Run all tests
	 */
	public static function run_tests() {
		echo "<h1>SEO Plugin Detection - Test Suite</h1>\n";
		
		self::test_detector_class();
		self::test_detection_methods();
		self::test_admin_interface();
		self::test_security_features();
		
		echo "<h2>✅ All detection tests completed!</h2>\n";
	}

	/**
	 * Test detector class
	 */
	private static function test_detector_class() {
		echo "<h2>Testing Detector Class</h2>\n";
		
		try {
			// Test class loading
			if ( class_exists( 'SEO_Auto_Optimizer_Plugin_Detector' ) ) {
				echo "✅ SEO_Auto_Optimizer_Plugin_Detector class loaded\n";
			} else {
				echo "❌ SEO_Auto_Optimizer_Plugin_Detector class not found\n";
				return;
			}

			// Test singleton pattern
			$detector1 = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
			$detector2 = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
			
			if ( $detector1 === $detector2 ) {
				echo "✅ Singleton pattern working correctly\n";
			} else {
				echo "❌ Singleton pattern failed\n";
			}

			// Test required methods exist
			$required_methods = array(
				'get_active_seo_plugins',
				'get_primary_seo_plugin',
				'is_plugin_active',
				'clear_cache',
				'render_admin_interface',
				'get_plugin_statistics',
			);

			foreach ( $required_methods as $method ) {
				if ( method_exists( $detector1, $method ) ) {
					echo "✅ Method {$method} exists\n";
				} else {
					echo "❌ Method {$method} missing\n";
				}
			}

		} catch ( Exception $e ) {
			echo "❌ Exception: " . $e->getMessage() . "\n";
		}
		
		echo "\n";
	}

	/**
	 * Test detection methods
	 */
	private static function test_detection_methods() {
		echo "<h2>Testing Detection Methods</h2>\n";
		
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Test get_active_seo_plugins
			$active_plugins = $detector->get_active_seo_plugins();
			if ( is_array( $active_plugins ) ) {
				echo "✅ get_active_seo_plugins returns array\n";
				echo "   Detected " . count( $active_plugins ) . " active SEO plugins\n";
			} else {
				echo "❌ get_active_seo_plugins does not return array\n";
			}

			// Test get_primary_seo_plugin
			$primary_plugin = $detector->get_primary_seo_plugin();
			if ( is_null( $primary_plugin ) || is_array( $primary_plugin ) ) {
				echo "✅ get_primary_seo_plugin returns valid type\n";
				if ( $primary_plugin ) {
					echo "   Primary plugin: " . $primary_plugin['name'] . "\n";
				} else {
					echo "   No primary plugin detected\n";
				}
			} else {
				echo "❌ get_primary_seo_plugin returns invalid type\n";
			}

			// Test is_plugin_active with known plugins
			$known_plugins = array( 'rank_math', 'yoast', 'seopress', 'aioseo' );
			foreach ( $known_plugins as $plugin_key ) {
				$is_active = $detector->is_plugin_active( $plugin_key );
				if ( is_bool( $is_active ) ) {
					$status = $is_active ? 'Active' : 'Inactive';
					echo "✅ {$plugin_key}: {$status}\n";
				} else {
					echo "❌ is_plugin_active({$plugin_key}) does not return boolean\n";
				}
			}

			// Test with invalid plugin
			$invalid_result = $detector->is_plugin_active( 'invalid_plugin' );
			if ( $invalid_result === false ) {
				echo "✅ Invalid plugin correctly returns false\n";
			} else {
				echo "❌ Invalid plugin should return false\n";
			}

		} catch ( Exception $e ) {
			echo "❌ Exception: " . $e->getMessage() . "\n";
		}
		
		echo "\n";
	}

	/**
	 * Test admin interface
	 */
	private static function test_admin_interface() {
		echo "<h2>Testing Admin Interface</h2>\n";
		
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Test admin interface rendering
			$admin_html = $detector->render_admin_interface();
			if ( is_string( $admin_html ) && ! empty( $admin_html ) ) {
				echo "✅ Admin interface renders successfully\n";
				echo "   HTML length: " . strlen( $admin_html ) . " characters\n";
			} else {
				echo "❌ Admin interface rendering failed\n";
			}

			// Test for required HTML elements
			$required_elements = array(
				'seo-plugin-detector-admin',
				'seo-detector-header',
				'seo-refresh-plugins',
				'seo-detected-plugins',
				'seo-compatibility-info',
			);

			foreach ( $required_elements as $element ) {
				if ( strpos( $admin_html, $element ) !== false ) {
					echo "✅ HTML element '{$element}' found\n";
				} else {
					echo "❌ HTML element '{$element}' missing\n";
				}
			}

			// Test statistics
			$stats = $detector->get_plugin_statistics();
			if ( is_array( $stats ) ) {
				echo "✅ Plugin statistics generated\n";
				echo "   Total detected: " . $stats['total_detected'] . "\n";
				echo "   Has conflicts: " . ( $stats['has_conflicts'] ? 'Yes' : 'No' ) . "\n";
			} else {
				echo "❌ Plugin statistics failed\n";
			}

		} catch ( Exception $e ) {
			echo "❌ Exception: " . $e->getMessage() . "\n";
		}
		
		echo "\n";
	}

	/**
	 * Test security features
	 */
	private static function test_security_features() {
		echo "<h2>Testing Security Features</h2>\n";
		
		try {
			$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

			// Test cache functionality
			$detector->clear_cache();
			echo "✅ Cache cleared successfully\n";

			$plugins1 = $detector->get_active_seo_plugins();
			$plugins2 = $detector->get_active_seo_plugins();
			
			if ( $plugins1 === $plugins2 ) {
				echo "✅ Cache consistency maintained\n";
			} else {
				echo "❌ Cache inconsistency detected\n";
			}

			// Test force refresh
			$plugins3 = $detector->get_active_seo_plugins( true );
			if ( is_array( $plugins3 ) ) {
				echo "✅ Force refresh working\n";
			} else {
				echo "❌ Force refresh failed\n";
			}

			// Test admin interface security
			$admin_html = $detector->render_admin_interface();
			if ( strpos( $admin_html, '<script>' ) === false ) {
				echo "✅ No unescaped script tags in admin interface\n";
			} else {
				echo "❌ Potential XSS vulnerability detected\n";
			}

		} catch ( Exception $e ) {
			echo "❌ Exception: " . $e->getMessage() . "\n";
		}
		
		echo "\n";
	}
}

// Run tests if this file is accessed directly
if ( basename( $_SERVER['PHP_SELF'] ) === basename( __FILE__ ) ) {
	// Set content type for HTML output
	if ( ! headers_sent() ) {
		header( 'Content-Type: text/html; charset=utf-8' );
	}
	
	echo "<!DOCTYPE html>\n";
	echo "<html><head><title>SEO Plugin Detection - Test Suite</title></head><body>\n";
	echo "<style>body{font-family:Arial,sans-serif;margin:20px;} h1,h2{color:#333;} .success{color:green;} .error{color:red;}</style>\n";
	
	SEO_Plugin_Detection_Test::run_tests();
	
	echo "</body></html>\n";
}

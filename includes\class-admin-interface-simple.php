<?php
/**
 * SEO Auto Optimizer - Admin Interface Simple
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Admin Interface Simple Class - QUI MARCHE !
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Admin_Interface {

	/**
	 * Singleton instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Admin_Interface
	 */
	private static $instance = null;

	/**
	 * Get singleton instance
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_Admin_Interface
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
		add_action( 'admin_init', array( $this, 'register_settings' ) );
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Add admin menu
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_admin_menu() {
		add_menu_page(
			__( 'SEO Auto Optimizer', 'seo-auto-optimizer' ),
			__( 'SEO Optimizer', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer',
			array( $this, 'dashboard_page' ),
			'dashicons-search',
			30
		);

		add_submenu_page(
			'seo-auto-optimizer',
			__( 'Configuration', 'seo-auto-optimizer' ),
			__( 'Configuration', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-config',
			array( $this, 'configuration_page' )
		);
	}

	/**
	 * Register settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function register_settings() {
		register_setting( 'sao_general_group', 'sao_general_options' );
		register_setting( 'sao_ai_group', 'sao_ai_options' );
	}

	/**
	 * Dashboard page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function dashboard_page() {
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - Dashboard', 'seo-auto-optimizer' ); ?></h1>
			<p><?php esc_html_e( 'Welcome to SEO Auto Optimizer!', 'seo-auto-optimizer' ); ?></p>
			
			<div class="card">
				<h2><?php esc_html_e( 'Quick Start', 'seo-auto-optimizer' ); ?></h2>
				<p><?php esc_html_e( '1. Configure your AI API keys in the Configuration page', 'seo-auto-optimizer' ); ?></p>
				<p><?php esc_html_e( '2. Start optimizing your content with AI-generated keywords', 'seo-auto-optimizer' ); ?></p>
				<a href="<?php echo esc_url( admin_url( 'admin.php?page=seo-auto-optimizer-config' ) ); ?>" class="button button-primary">
					<?php esc_html_e( 'Configure Settings', 'seo-auto-optimizer' ); ?>
				</a>
			</div>
		</div>
		<?php
	}

	/**
	 * Configuration page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function configuration_page() {
		// Handle form submission
		if ( isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'sao_config_nonce' ) ) {
			$this->save_settings();
			echo '<div class="notice notice-success"><p>' . esc_html__( 'Settings saved!', 'seo-auto-optimizer' ) . '</p></div>';
		}

		$general_options = get_option( 'sao_general_options', array() );
		$ai_options = get_option( 'sao_ai_options', array() );
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - Configuration', 'seo-auto-optimizer' ); ?></h1>
			
			<form method="post" action="">
				<?php wp_nonce_field( 'sao_config_nonce' ); ?>
				
				<!-- General Settings -->
				<div class="card">
					<h2><?php esc_html_e( 'General Settings', 'seo-auto-optimizer' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php esc_html_e( 'Number of Keywords', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_general_options[keywords_count]">
									<?php for ( $i = 5; $i <= 15; $i++ ) : ?>
										<option value="<?php echo esc_attr( $i ); ?>" <?php selected( $general_options['keywords_count'] ?? 8, $i ); ?>>
											<?php echo esc_html( $i ); ?>
										</option>
									<?php endfor; ?>
								</select>
								<p class="description"><?php esc_html_e( 'Number of keywords to generate per optimization.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Enable Backup', 'seo-auto-optimizer' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="sao_general_options[backup_enabled]" value="1" <?php checked( $general_options['backup_enabled'] ?? true, true ); ?> />
									<?php esc_html_e( 'Create backup before modifying SEO data', 'seo-auto-optimizer' ); ?>
								</label>
							</td>
						</tr>
					</table>
				</div>

				<!-- AI Settings -->
				<div class="card">
					<h2><?php esc_html_e( 'AI API Configuration', 'seo-auto-optimizer' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php esc_html_e( 'OpenAI API Key', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="password" name="sao_ai_options[openai_api_key]" 
									   value="<?php echo esc_attr( $this->mask_api_key( $ai_options['openai_api_key'] ?? '' ) ); ?>" 
									   class="regular-text" />
								<p class="description">
									<?php esc_html_e( 'Get your API key from:', 'seo-auto-optimizer' ); ?>
									<a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Google Gemini API Key', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="password" name="sao_ai_options[gemini_api_key]" 
									   value="<?php echo esc_attr( $this->mask_api_key( $ai_options['gemini_api_key'] ?? '' ) ); ?>" 
									   class="regular-text" />
								<p class="description">
									<?php esc_html_e( 'Get your API key from:', 'seo-auto-optimizer' ); ?>
									<a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Primary AI Provider', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_ai_options[primary_provider]">
									<option value="gemini" <?php selected( $ai_options['primary_provider'] ?? 'gemini', 'gemini' ); ?>>
										Google Gemini
									</option>
									<option value="openai" <?php selected( $ai_options['primary_provider'] ?? 'gemini', 'openai' ); ?>>
										OpenAI GPT
									</option>
								</select>
							</td>
						</tr>
					</table>
				</div>

				<?php submit_button(); ?>
			</form>
		</div>
		<?php
	}

	/**
	 * Save settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function save_settings() {
		// General options
		if ( isset( $_POST['sao_general_options'] ) ) {
			$general_options = array();
			$general_options['keywords_count'] = absint( $_POST['sao_general_options']['keywords_count'] ?? 8 );
			$general_options['backup_enabled'] = ! empty( $_POST['sao_general_options']['backup_enabled'] );
			update_option( 'sao_general_options', $general_options );
		}

		// AI options
		if ( isset( $_POST['sao_ai_options'] ) ) {
			$ai_options = array();
			
			// Only save API keys if they're not masked
			$openai_key = sanitize_text_field( $_POST['sao_ai_options']['openai_api_key'] ?? '' );
			if ( ! empty( $openai_key ) && strpos( $openai_key, '*' ) === false ) {
				$ai_options['openai_api_key'] = $openai_key;
			} else {
				$existing = get_option( 'sao_ai_options', array() );
				$ai_options['openai_api_key'] = $existing['openai_api_key'] ?? '';
			}

			$gemini_key = sanitize_text_field( $_POST['sao_ai_options']['gemini_api_key'] ?? '' );
			if ( ! empty( $gemini_key ) && strpos( $gemini_key, '*' ) === false ) {
				$ai_options['gemini_api_key'] = $gemini_key;
			} else {
				$existing = get_option( 'sao_ai_options', array() );
				$ai_options['gemini_api_key'] = $existing['gemini_api_key'] ?? '';
			}

			$ai_options['primary_provider'] = sanitize_key( $_POST['sao_ai_options']['primary_provider'] ?? 'gemini' );
			update_option( 'sao_ai_options', $ai_options );
		}
	}

	/**
	 * Mask API key for display
	 *
	 * @since 1.0.0
	 * @param string $api_key API key to mask
	 * @return string Masked API key
	 */
	private function mask_api_key( $api_key ) {
		if ( empty( $api_key ) ) {
			return '';
		}

		$length = strlen( $api_key );
		if ( $length <= 8 ) {
			return str_repeat( '*', $length );
		}

		return substr( $api_key, 0, 4 ) . str_repeat( '*', $length - 8 ) . substr( $api_key, -4 );
	}
}

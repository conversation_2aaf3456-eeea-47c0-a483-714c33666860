<?php
/**
 * SEO Auto Optimizer - Admin Interface Simple
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Admin Interface Simple Class - QUI MARCHE !
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Admin_Interface {

	/**
	 * Singleton instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Admin_Interface
	 */
	private static $instance = null;

	/**
	 * Get singleton instance
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_Admin_Interface
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
		add_action( 'admin_init', array( $this, 'register_settings' ) );
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Add admin menu
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_admin_menu() {
		add_menu_page(
			__( 'SEO Auto Optimizer', 'seo-auto-optimizer' ),
			__( 'SEO Optimizer', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer',
			array( $this, 'dashboard_page' ),
			'dashicons-search',
			30
		);

		add_submenu_page(
			'seo-auto-optimizer',
			__( 'Configuration', 'seo-auto-optimizer' ),
			__( 'Configuration', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-config',
			array( $this, 'configuration_page' )
		);

		add_submenu_page(
			'seo-auto-optimizer',
			__( 'Boss Optimization', 'seo-auto-optimizer' ),
			__( 'Boss Optimization', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-boss',
			array( $this, 'boss_optimization_page' )
		);

		add_submenu_page(
			'seo-auto-optimizer',
			__( 'History', 'seo-auto-optimizer' ),
			__( 'History', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-history',
			array( $this, 'history_page' )
		);

		add_submenu_page(
			'seo-auto-optimizer',
			__( 'Help', 'seo-auto-optimizer' ),
			__( 'Help', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-auto-optimizer-help',
			array( $this, 'help_page' )
		);
	}

	/**
	 * Register settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function register_settings() {
		register_setting( 'sao_general_group', 'sao_general_options' );
		register_setting( 'sao_ai_group', 'sao_ai_options' );
		register_setting( 'sao_advanced_group', 'sao_advanced_options' );
	}

	/**
	 * Dashboard page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function dashboard_page() {
		$stats = $this->get_dashboard_stats();
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - Dashboard', 'seo-auto-optimizer' ); ?></h1>

			<!-- Statistics Cards -->
			<div class="sao-dashboard-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
				<div class="card">
					<h3><?php esc_html_e( 'Total Optimizations', 'seo-auto-optimizer' ); ?></h3>
					<div style="font-size: 2em; font-weight: bold; color: #0073aa;"><?php echo esc_html( $stats['total_optimizations'] ); ?></div>
				</div>
				<div class="card">
					<h3><?php esc_html_e( 'Success Rate', 'seo-auto-optimizer' ); ?></h3>
					<div style="font-size: 2em; font-weight: bold; color: #00a32a;"><?php echo esc_html( $stats['success_rate'] ); ?>%</div>
				</div>
				<div class="card">
					<h3><?php esc_html_e( 'SEO Plugins Detected', 'seo-auto-optimizer' ); ?></h3>
					<div style="font-size: 2em; font-weight: bold; color: #8c8f94;"><?php echo esc_html( $stats['seo_plugins_count'] ); ?></div>
				</div>
			</div>

			<!-- Plugin Detection -->
			<div class="card">
				<h2><?php esc_html_e( 'SEO Plugins Detection', 'seo-auto-optimizer' ); ?></h2>
				<?php $this->render_plugin_detection(); ?>
			</div>

			<!-- Quick Actions -->
			<div class="card">
				<h2><?php esc_html_e( 'Quick Actions', 'seo-auto-optimizer' ); ?></h2>
				<p><?php esc_html_e( 'Get started with SEO Auto Optimizer:', 'seo-auto-optimizer' ); ?></p>
				<div style="display: flex; gap: 10px; flex-wrap: wrap;">
					<a href="<?php echo esc_url( admin_url( 'admin.php?page=seo-auto-optimizer-config' ) ); ?>" class="button button-primary">
						<?php esc_html_e( 'Configure Settings', 'seo-auto-optimizer' ); ?>
					</a>
					<a href="<?php echo esc_url( admin_url( 'admin.php?page=seo-auto-optimizer-boss' ) ); ?>" class="button button-secondary">
						<?php esc_html_e( 'Boss Optimization', 'seo-auto-optimizer' ); ?>
					</a>
					<a href="<?php echo esc_url( admin_url( 'admin.php?page=seo-auto-optimizer-history' ) ); ?>" class="button">
						<?php esc_html_e( 'View History', 'seo-auto-optimizer' ); ?>
					</a>
				</div>
			</div>

			<!-- Recent Activity -->
			<div class="card">
				<h2><?php esc_html_e( 'Recent Activity', 'seo-auto-optimizer' ); ?></h2>
				<?php $this->render_recent_activity(); ?>
			</div>
		</div>
		<?php
	}

	/**
	 * Configuration page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function configuration_page() {
		// Handle form submission
		if ( isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'sao_config_nonce' ) ) {
			$this->save_settings();
			echo '<div class="notice notice-success"><p>' . esc_html__( 'Settings saved!', 'seo-auto-optimizer' ) . '</p></div>';
		}

		$general_options = get_option( 'sao_general_options', array() );
		$ai_options = get_option( 'sao_ai_options', array() );
		$advanced_options = get_option( 'sao_advanced_options', array() );
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - Configuration', 'seo-auto-optimizer' ); ?></h1>
			
			<form method="post" action="">
				<?php wp_nonce_field( 'sao_config_nonce' ); ?>
				
				<!-- General Settings -->
				<div class="card">
					<h2><?php esc_html_e( 'General Settings', 'seo-auto-optimizer' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php esc_html_e( 'Number of Keywords', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_general_options[keywords_count]">
									<?php for ( $i = 5; $i <= 15; $i++ ) : ?>
										<option value="<?php echo esc_attr( $i ); ?>" <?php selected( $general_options['keywords_count'] ?? 8, $i ); ?>>
											<?php echo esc_html( $i ); ?>
										</option>
									<?php endfor; ?>
								</select>
								<p class="description"><?php esc_html_e( 'Number of keywords to generate per optimization.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Primary SEO Plugin', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_general_options[primary_seo_plugin]">
									<option value="" <?php selected( $general_options['primary_seo_plugin'] ?? '', '' ); ?>>
										<?php esc_html_e( 'Auto-detect', 'seo-auto-optimizer' ); ?>
									</option>
									<option value="yoast" <?php selected( $general_options['primary_seo_plugin'] ?? '', 'yoast' ); ?>>
										Yoast SEO
									</option>
									<option value="rankmath" <?php selected( $general_options['primary_seo_plugin'] ?? '', 'rankmath' ); ?>>
										Rank Math
									</option>
									<option value="seopress" <?php selected( $general_options['primary_seo_plugin'] ?? '', 'seopress' ); ?>>
										SEOPress
									</option>
									<option value="aioseo" <?php selected( $general_options['primary_seo_plugin'] ?? '', 'aioseo' ); ?>>
										All in One SEO
									</option>
								</select>
								<p class="description"><?php esc_html_e( 'Choose your preferred SEO plugin if multiple are installed.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Enable Backup', 'seo-auto-optimizer' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="sao_general_options[backup_enabled]" value="1" <?php checked( $general_options['backup_enabled'] ?? true, true ); ?> />
									<?php esc_html_e( 'Create backup before modifying SEO data', 'seo-auto-optimizer' ); ?>
								</label>
								<p class="description"><?php esc_html_e( 'Recommended: allows you to restore previous SEO data if needed.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Log Level', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_general_options[log_level]">
									<option value="none" <?php selected( $general_options['log_level'] ?? 'errors', 'none' ); ?>>
										<?php esc_html_e( 'No logging', 'seo-auto-optimizer' ); ?>
									</option>
									<option value="errors" <?php selected( $general_options['log_level'] ?? 'errors', 'errors' ); ?>>
										<?php esc_html_e( 'Errors only', 'seo-auto-optimizer' ); ?>
									</option>
									<option value="debug" <?php selected( $general_options['log_level'] ?? 'errors', 'debug' ); ?>>
										<?php esc_html_e( 'Debug info', 'seo-auto-optimizer' ); ?>
									</option>
									<option value="all" <?php selected( $general_options['log_level'] ?? 'errors', 'all' ); ?>>
										<?php esc_html_e( 'Everything', 'seo-auto-optimizer' ); ?>
									</option>
								</select>
								<p class="description"><?php esc_html_e( 'Choose what to log for debugging purposes.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
					</table>
				</div>

				<!-- AI Settings -->
				<div class="card">
					<h2><?php esc_html_e( 'AI API Configuration', 'seo-auto-optimizer' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php esc_html_e( 'OpenAI API Key', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="password" name="sao_ai_options[openai_api_key]" 
									   value="<?php echo esc_attr( $this->mask_api_key( $ai_options['openai_api_key'] ?? '' ) ); ?>" 
									   class="regular-text" />
								<p class="description">
									<?php esc_html_e( 'Get your API key from:', 'seo-auto-optimizer' ); ?>
									<a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Google Gemini API Key', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="password" name="sao_ai_options[gemini_api_key]"
									   value="<?php echo esc_attr( $this->mask_api_key( $ai_options['gemini_api_key'] ?? '' ) ); ?>"
									   class="regular-text" />
								<button type="button" class="button sao-test-api" data-provider="gemini">
									<?php esc_html_e( 'Test Connection', 'seo-auto-optimizer' ); ?>
								</button>
								<p class="description">
									<?php esc_html_e( 'Get your API key from:', 'seo-auto-optimizer' ); ?>
									<a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Anthropic Claude API Key', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="password" name="sao_ai_options[anthropic_api_key]"
									   value="<?php echo esc_attr( $this->mask_api_key( $ai_options['anthropic_api_key'] ?? '' ) ); ?>"
									   class="regular-text" />
								<button type="button" class="button sao-test-api" data-provider="anthropic">
									<?php esc_html_e( 'Test Connection', 'seo-auto-optimizer' ); ?>
								</button>
								<p class="description">
									<?php esc_html_e( 'Get your API key from:', 'seo-auto-optimizer' ); ?>
									<a href="https://console.anthropic.com/" target="_blank">Anthropic Console</a>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Ollama Endpoint', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="url" name="sao_ai_options[ollama_endpoint]"
									   value="<?php echo esc_attr( $ai_options['ollama_endpoint'] ?? 'http://localhost:11434/api/generate' ); ?>"
									   class="regular-text" />
								<p class="description"><?php esc_html_e( 'Local Ollama server endpoint for self-hosted AI.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Ollama Model', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="text" name="sao_ai_options[ollama_model]"
									   value="<?php echo esc_attr( $ai_options['ollama_model'] ?? 'llama2' ); ?>"
									   class="regular-text" />
								<p class="description"><?php esc_html_e( 'Model name to use with Ollama (e.g., llama2, mistral).', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Primary AI Provider', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_ai_options[primary_provider]">
									<option value="gemini" <?php selected( $ai_options['primary_provider'] ?? 'gemini', 'gemini' ); ?>>
										Google Gemini
									</option>
									<option value="openai" <?php selected( $ai_options['primary_provider'] ?? 'gemini', 'openai' ); ?>>
										OpenAI GPT
									</option>
									<option value="anthropic" <?php selected( $ai_options['primary_provider'] ?? 'gemini', 'anthropic' ); ?>>
										Anthropic Claude
									</option>
									<option value="ollama" <?php selected( $ai_options['primary_provider'] ?? 'gemini', 'ollama' ); ?>>
										Ollama (Local)
									</option>
								</select>
								<p class="description"><?php esc_html_e( 'Select your preferred AI provider for keyword generation.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
					</table>
				</div>

				<!-- Advanced Settings -->
				<div class="card">
					<h2><?php esc_html_e( 'Advanced Settings', 'seo-auto-optimizer' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php esc_html_e( 'Cache Duration', 'seo-auto-optimizer' ); ?></th>
							<td>
								<select name="sao_advanced_options[cache_duration]">
									<option value="3600" <?php selected( $advanced_options['cache_duration'] ?? 86400, 3600 ); ?>>
										<?php esc_html_e( '1 Hour', 'seo-auto-optimizer' ); ?>
									</option>
									<option value="86400" <?php selected( $advanced_options['cache_duration'] ?? 86400, 86400 ); ?>>
										<?php esc_html_e( '24 Hours', 'seo-auto-optimizer' ); ?>
									</option>
									<option value="172800" <?php selected( $advanced_options['cache_duration'] ?? 86400, 172800 ); ?>>
										<?php esc_html_e( '48 Hours', 'seo-auto-optimizer' ); ?>
									</option>
								</select>
								<p class="description"><?php esc_html_e( 'How long to cache AI-generated keywords.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Rate Limit', 'seo-auto-optimizer' ); ?></th>
							<td>
								<input type="number" name="sao_advanced_options[rate_limit]"
									   value="<?php echo esc_attr( $advanced_options['rate_limit'] ?? 5 ); ?>"
									   min="1" max="20" class="small-text" />
								<span><?php esc_html_e( 'requests per minute', 'seo-auto-optimizer' ); ?></span>
								<p class="description"><?php esc_html_e( 'Maximum API requests per minute to prevent rate limiting.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Debug Mode', 'seo-auto-optimizer' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="sao_advanced_options[debug_mode]" value="1" <?php checked( $advanced_options['debug_mode'] ?? false, true ); ?> />
									<?php esc_html_e( 'Enable debug mode', 'seo-auto-optimizer' ); ?>
								</label>
								<p class="description"><?php esc_html_e( 'Enable detailed logging for troubleshooting.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php esc_html_e( 'Auto Apply', 'seo-auto-optimizer' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="sao_advanced_options[auto_apply]" value="1" <?php checked( $advanced_options['auto_apply'] ?? false, true ); ?> />
									<?php esc_html_e( 'Automatically apply generated keywords', 'seo-auto-optimizer' ); ?>
								</label>
								<p class="description"><?php esc_html_e( 'Automatically inject keywords without manual confirmation.', 'seo-auto-optimizer' ); ?></p>
							</td>
						</tr>
					</table>
				</div>

				<?php submit_button(); ?>
			</form>

			<!-- Export/Import Section -->
			<div class="card">
				<h2><?php esc_html_e( 'Export/Import Configuration', 'seo-auto-optimizer' ); ?></h2>
				<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
					<div>
						<h3><?php esc_html_e( 'Export Settings', 'seo-auto-optimizer' ); ?></h3>
						<p><?php esc_html_e( 'Download your current configuration as a JSON file.', 'seo-auto-optimizer' ); ?></p>
						<button type="button" class="button button-secondary sao-export-settings">
							<?php esc_html_e( 'Export Configuration', 'seo-auto-optimizer' ); ?>
						</button>
					</div>
					<div>
						<h3><?php esc_html_e( 'Import Settings', 'seo-auto-optimizer' ); ?></h3>
						<p><?php esc_html_e( 'Upload a configuration file to restore settings.', 'seo-auto-optimizer' ); ?></p>
						<input type="file" id="sao-import-file" accept=".json" style="margin-bottom: 10px;" />
						<br>
						<button type="button" class="button button-secondary sao-import-settings">
							<?php esc_html_e( 'Import Configuration', 'seo-auto-optimizer' ); ?>
						</button>
					</div>
				</div>
			</div>
		</div>
		<?php
	}

	/**
	 * Boss optimization page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function boss_optimization_page() {
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - Boss Optimization', 'seo-auto-optimizer' ); ?></h1>
			<div class="card">
				<h2><?php esc_html_e( 'Bulk Optimization Interface', 'seo-auto-optimizer' ); ?></h2>
				<p><?php esc_html_e( 'Optimize multiple posts at once with AI-generated keywords.', 'seo-auto-optimizer' ); ?></p>
				<div id="sao-boss-optimization-interface">
					<!-- Boss optimization interface will be loaded here -->
					<p><?php esc_html_e( 'Boss optimization interface is being loaded...', 'seo-auto-optimizer' ); ?></p>
				</div>
			</div>
		</div>
		<?php
	}

	/**
	 * History page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function history_page() {
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - History', 'seo-auto-optimizer' ); ?></h1>
			<div class="card">
				<h2><?php esc_html_e( 'Optimization History', 'seo-auto-optimizer' ); ?></h2>
				<?php $this->render_optimization_history(); ?>
			</div>
		</div>
		<?php
	}

	/**
	 * Help page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function help_page() {
		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Auto Optimizer - Help', 'seo-auto-optimizer' ); ?></h1>
			<div class="card">
				<h2><?php esc_html_e( 'Getting Started', 'seo-auto-optimizer' ); ?></h2>
				<ol>
					<li><?php esc_html_e( 'Configure your AI API keys in the Configuration page', 'seo-auto-optimizer' ); ?></li>
					<li><?php esc_html_e( 'Choose your primary SEO plugin', 'seo-auto-optimizer' ); ?></li>
					<li><?php esc_html_e( 'Start optimizing your content with the Boss Optimization tool', 'seo-auto-optimizer' ); ?></li>
				</ol>
			</div>
			<div class="card">
				<h2><?php esc_html_e( 'Supported SEO Plugins', 'seo-auto-optimizer' ); ?></h2>
				<ul>
					<li>Yoast SEO</li>
					<li>Rank Math</li>
					<li>SEOPress</li>
					<li>All in One SEO</li>
				</ul>
			</div>
			<div class="card">
				<h2><?php esc_html_e( 'Supported AI Providers', 'seo-auto-optimizer' ); ?></h2>
				<ul>
					<li>OpenAI GPT</li>
					<li>Google Gemini</li>
					<li>Anthropic Claude</li>
					<li>Ollama (Local)</li>
				</ul>
			</div>
		</div>
		<?php
	}

	/**
	 * Save settings
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function save_settings() {
		// General options
		if ( isset( $_POST['sao_general_options'] ) ) {
			$general_options = array();
			$general_options['keywords_count'] = absint( $_POST['sao_general_options']['keywords_count'] ?? 8 );
			$general_options['primary_seo_plugin'] = sanitize_key( $_POST['sao_general_options']['primary_seo_plugin'] ?? '' );
			$general_options['backup_enabled'] = ! empty( $_POST['sao_general_options']['backup_enabled'] );
			$general_options['log_level'] = sanitize_key( $_POST['sao_general_options']['log_level'] ?? 'errors' );
			update_option( 'sao_general_options', $general_options );
		}

		// AI options
		if ( isset( $_POST['sao_ai_options'] ) ) {
			$existing = get_option( 'sao_ai_options', array() );
			$ai_options = array();

			// Only save API keys if they're not masked
			$openai_key = sanitize_text_field( $_POST['sao_ai_options']['openai_api_key'] ?? '' );
			if ( ! empty( $openai_key ) && strpos( $openai_key, '*' ) === false ) {
				$ai_options['openai_api_key'] = $openai_key;
			} else {
				$ai_options['openai_api_key'] = $existing['openai_api_key'] ?? '';
			}

			$gemini_key = sanitize_text_field( $_POST['sao_ai_options']['gemini_api_key'] ?? '' );
			if ( ! empty( $gemini_key ) && strpos( $gemini_key, '*' ) === false ) {
				$ai_options['gemini_api_key'] = $gemini_key;
			} else {
				$ai_options['gemini_api_key'] = $existing['gemini_api_key'] ?? '';
			}

			$anthropic_key = sanitize_text_field( $_POST['sao_ai_options']['anthropic_api_key'] ?? '' );
			if ( ! empty( $anthropic_key ) && strpos( $anthropic_key, '*' ) === false ) {
				$ai_options['anthropic_api_key'] = $anthropic_key;
			} else {
				$ai_options['anthropic_api_key'] = $existing['anthropic_api_key'] ?? '';
			}

			$ai_options['ollama_endpoint'] = esc_url_raw( $_POST['sao_ai_options']['ollama_endpoint'] ?? 'http://localhost:11434/api/generate' );
			$ai_options['ollama_model'] = sanitize_text_field( $_POST['sao_ai_options']['ollama_model'] ?? 'llama2' );
			$ai_options['primary_provider'] = sanitize_key( $_POST['sao_ai_options']['primary_provider'] ?? 'gemini' );
			update_option( 'sao_ai_options', $ai_options );
		}

		// Advanced options
		if ( isset( $_POST['sao_advanced_options'] ) ) {
			$advanced_options = array();
			$advanced_options['cache_duration'] = absint( $_POST['sao_advanced_options']['cache_duration'] ?? 86400 );
			$advanced_options['rate_limit'] = absint( $_POST['sao_advanced_options']['rate_limit'] ?? 5 );
			$advanced_options['debug_mode'] = ! empty( $_POST['sao_advanced_options']['debug_mode'] );
			$advanced_options['auto_apply'] = ! empty( $_POST['sao_advanced_options']['auto_apply'] );
			update_option( 'sao_advanced_options', $advanced_options );
		}
	}

	/**
	 * Mask API key for display
	 *
	 * @since 1.0.0
	 * @param string $api_key API key to mask
	 * @return string Masked API key
	 */
	private function mask_api_key( $api_key ) {
		if ( empty( $api_key ) ) {
			return '';
		}

		$length = strlen( $api_key );
		if ( $length <= 8 ) {
			return str_repeat( '*', $length );
		}

		return substr( $api_key, 0, 4 ) . str_repeat( '*', $length - 8 ) . substr( $api_key, -4 );
	}

	/**
	 * Get dashboard statistics
	 *
	 * @since 1.0.0
	 * @return array Dashboard stats
	 */
	private function get_dashboard_stats() {
		$stats = get_option( 'sao_dashboard_stats', array() );

		return array(
			'total_optimizations' => $stats['total_optimizations'] ?? 0,
			'success_rate' => $stats['success_rate'] ?? 0,
			'seo_plugins_count' => $this->count_active_seo_plugins(),
		);
	}

	/**
	 * Count active SEO plugins
	 *
	 * @since 1.0.0
	 * @return int Number of active SEO plugins
	 */
	private function count_active_seo_plugins() {
		$count = 0;
		$plugins = array(
			'wordpress-seo/wp-seo.php',
			'seo-by-rank-math/rank-math.php',
			'wp-seopress/seopress.php',
			'all-in-one-seo-pack/all_in_one_seo_pack.php',
		);

		foreach ( $plugins as $plugin ) {
			if ( is_plugin_active( $plugin ) ) {
				$count++;
			}
		}

		return $count;
	}

	/**
	 * Render plugin detection
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_plugin_detection() {
		$detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
		$active_plugins = $detector->get_active_seo_plugins();

		if ( empty( $active_plugins ) ) {
			echo '<p>' . esc_html__( 'No SEO plugins detected. Please install and activate an SEO plugin.', 'seo-auto-optimizer' ) . '</p>';
			return;
		}

		echo '<ul>';
		foreach ( $active_plugins as $plugin_key => $plugin_info ) {
			echo '<li><strong>' . esc_html( $plugin_info['name'] ) . '</strong> - ' . esc_html__( 'Active', 'seo-auto-optimizer' ) . '</li>';
		}
		echo '</ul>';
	}

	/**
	 * Render recent activity
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_recent_activity() {
		$recent_activity = get_option( 'sao_recent_activity', array() );

		if ( empty( $recent_activity ) ) {
			echo '<p>' . esc_html__( 'No recent activity.', 'seo-auto-optimizer' ) . '</p>';
			return;
		}

		echo '<ul>';
		foreach ( array_slice( $recent_activity, 0, 5 ) as $activity ) {
			echo '<li>' . esc_html( $activity['message'] ) . ' - <em>' . esc_html( human_time_diff( $activity['timestamp'] ) ) . ' ago</em></li>';
		}
		echo '</ul>';
	}

	/**
	 * Render optimization history
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_optimization_history() {
		$history = get_option( 'sao_optimization_history', array() );

		if ( empty( $history ) ) {
			echo '<p>' . esc_html__( 'No optimization history available.', 'seo-auto-optimizer' ) . '</p>';
			return;
		}

		echo '<table class="wp-list-table widefat fixed striped">';
		echo '<thead>';
		echo '<tr>';
		echo '<th>' . esc_html__( 'Post Title', 'seo-auto-optimizer' ) . '</th>';
		echo '<th>' . esc_html__( 'Keywords Generated', 'seo-auto-optimizer' ) . '</th>';
		echo '<th>' . esc_html__( 'Date', 'seo-auto-optimizer' ) . '</th>';
		echo '<th>' . esc_html__( 'Status', 'seo-auto-optimizer' ) . '</th>';
		echo '</tr>';
		echo '</thead>';
		echo '<tbody>';

		foreach ( array_slice( $history, 0, 20 ) as $entry ) {
			echo '<tr>';
			echo '<td>' . esc_html( $entry['post_title'] ?? 'Unknown' ) . '</td>';
			echo '<td>' . esc_html( implode( ', ', $entry['keywords'] ?? array() ) ) . '</td>';
			echo '<td>' . esc_html( date( 'Y-m-d H:i:s', $entry['timestamp'] ?? 0 ) ) . '</td>';
			echo '<td>' . esc_html( $entry['status'] ?? 'Unknown' ) . '</td>';
			echo '</tr>';
		}

		echo '</tbody>';
		echo '</table>';
	}
}

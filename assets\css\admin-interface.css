/**
 * SEO Auto Optimizer - Admin Interface Styles
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

/* Main Admin Wrapper */
.sao-admin-wrap {
    margin: 20px 0;
}

.sao-admin-wrap .wp-heading-inline {
    margin-bottom: 0;
}

.sao-new-optimization {
    margin-left: 10px;
}

/* Dashboard Grid */
.sao-dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 20px;
}

/* Statistics Cards */
.sao-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.sao-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    transition: box-shadow 0.3s ease;
}

.sao-stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sao-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
}

.sao-stat-blue .sao-stat-icon { background: #0073aa; }
.sao-stat-green .sao-stat-icon { background: #00a32a; }
.sao-stat-orange .sao-stat-icon { background: #ff6900; }
.sao-stat-purple .sao-stat-icon { background: #8c44c4; }

.sao-stat-content {
    flex: 1;
}

.sao-stat-value {
    font-size: 28px;
    font-weight: 600;
    line-height: 1;
    color: #1d2327;
    margin-bottom: 5px;
}

.sao-stat-title {
    font-size: 14px;
    color: #646970;
    font-weight: 500;
}

/* Charts Section */
.sao-chart-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.sao-chart-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    color: #1d2327;
}

.sao-charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.sao-chart-item h3 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #1d2327;
}

/* Recent Activity */
.sao-activity-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.sao-activity-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    color: #1d2327;
}

.sao-activity-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sao-activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f1;
}

.sao-activity-item:last-child {
    border-bottom: none;
}

.sao-activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f0f6fc;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0073aa;
    font-size: 16px;
}

.sao-activity-content {
    flex: 1;
}

.sao-activity-title {
    font-weight: 500;
    color: #1d2327;
    margin-bottom: 2px;
}

.sao-activity-time {
    font-size: 12px;
    color: #646970;
}

.sao-no-activity {
    text-align: center;
    color: #646970;
    font-style: italic;
    padding: 40px 0;
}

/* Quick Actions */
.sao-quick-actions-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
}

.sao-quick-actions-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    color: #1d2327;
}

.sao-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.sao-action-card {
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.sao-action-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sao-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f6fc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    color: #0073aa;
    font-size: 20px;
}

.sao-action-content h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.sao-action-content p {
    margin: 0 0 15px 0;
    font-size: 12px;
    color: #646970;
    line-height: 1.4;
}

.sao-action-btn {
    width: 100%;
}

/* Configuration Tabs */
.sao-config-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-top: 20px;
}

.sao-config-tabs {
    border-bottom: 1px solid #c3c4c7;
}

.sao-config-tabs .nav-tab-wrapper {
    margin: 0;
    padding: 0 20px;
    border-bottom: none;
}

.sao-config-tabs .nav-tab {
    border-bottom: 1px solid transparent;
    margin-bottom: -1px;
}

.sao-config-tabs .nav-tab-active {
    border-bottom-color: #c3c4c7;
    background: #fff;
}

.sao-tab-content {
    display: none;
    padding: 20px;
}

.sao-tab-content.sao-tab-active {
    display: block;
}

.sao-config-form .form-table {
    margin-top: 0;
}

/* API Test Results */
.sao-test-result {
    margin-left: 10px;
    font-weight: 500;
}

.sao-test-result.success {
    color: #00a32a;
}

.sao-test-result.error {
    color: #d63638;
}

.sao-test-result.testing {
    color: #0073aa;
}

/* Provider Priority List */
.sao-sortable-list {
    list-style: none;
    margin: 0;
    padding: 0;
    max-width: 300px;
}

.sao-sortable-list li {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 5px;
    cursor: move;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sao-sortable-list li:hover {
    background: #f0f0f0;
}

.sao-sortable-list li .dashicons {
    color: #646970;
}

/* Import/Export Section */
.sao-import-export-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.sao-import-section,
.sao-export-section {
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 20px;
}

.sao-import-section h3,
.sao-export-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1d2327;
}

.sao-file-input {
    margin-bottom: 15px;
}

.sao-file-input input[type="file"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* History Table */
.sao-history-table {
    width: 100%;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-top: 20px;
}

.sao-history-table th,
.sao-history-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f1;
}

.sao-history-table th {
    background: #f9f9f9;
    font-weight: 600;
    color: #1d2327;
}

.sao-history-table tr:last-child td {
    border-bottom: none;
}

.sao-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.sao-status-success {
    background: #d4edda;
    color: #155724;
}

.sao-status-error {
    background: #f8d7da;
    color: #721c24;
}

.sao-status-pending {
    background: #fff3cd;
    color: #856404;
}

/* Help Documentation */
.sao-help-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 30px;
    margin-top: 20px;
}

.sao-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.sao-help-section {
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 20px;
}

.sao-help-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1d2327;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sao-help-section .dashicons {
    color: #0073aa;
}

.sao-help-section ul {
    margin: 0;
    padding-left: 20px;
}

.sao-help-section li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sao-charts-grid {
        grid-template-columns: 1fr;
    }
    
    .sao-import-export-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sao-stats-cards {
        grid-template-columns: 1fr;
    }
    
    .sao-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .sao-help-grid {
        grid-template-columns: 1fr;
    }
    
    .sao-stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .sao-config-tabs .nav-tab-wrapper {
        padding: 0 10px;
    }
    
    .sao-tab-content {
        padding: 15px;
    }
}

/* Loading States */
.sao-loading {
    opacity: 0.6;
    pointer-events: none;
}

.sao-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: sao-spin 1s linear infinite;
}

@keyframes sao-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

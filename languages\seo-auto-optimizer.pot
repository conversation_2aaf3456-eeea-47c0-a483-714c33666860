# SEO Auto Optimizer with AI
# Copyright (C) 2024 SEO Canyon
# This file is distributed under the GPL v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: SEO Auto Optimizer with AI 1.0.0\n"
"Report-Msgid-Bugs-To: https://seocanyon.com/support\n"
"POT-Creation-Date: 2024-01-01 00:00:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: seo-auto-optimizer.php:95
msgid "SEO Auto Optimizer requires WordPress version %s or higher."
msgstr ""

#: seo-auto-optimizer.php:110
msgid "SEO Auto Optimizer requires PHP version %s or higher."
msgstr ""

#: seo-auto-optimizer.php:130
msgid "SEO Auto Optimizer could not be activated due to unmet requirements."
msgstr ""

#: seo-auto-optimizer.php:131
msgid "Plugin Activation Error"
msgstr ""

#: includes/class-seo-auto-optimizer.php:125
msgid "SEO Auto Optimizer"
msgstr ""

#: includes/class-seo-auto-optimizer.php:126
msgid "SEO Optimizer"
msgstr ""

#: includes/class-seo-auto-optimizer.php:135
#: includes/class-seo-auto-optimizer.php:136
msgid "Settings"
msgstr ""

#: includes/class-seo-auto-optimizer.php:175
msgid "An error occurred. Please try again."
msgstr ""

#: includes/class-seo-auto-optimizer.php:176
msgid "Operation completed successfully."
msgstr ""

#: includes/class-seo-auto-optimizer.php:210
msgid "Security check failed."
msgstr ""

#: includes/class-seo-auto-optimizer.php:211
#: includes/class-seo-auto-optimizer.php:219
msgid "Error"
msgstr ""

#: includes/class-seo-auto-optimizer.php:218
msgid "You do not have permission to perform this action."
msgstr ""

#: includes/class-seo-auto-optimizer.php:238
msgid "Invalid action."
msgstr ""

#: includes/class-seo-auto-optimizer.php:350
msgid "General Settings"
msgstr ""

#: includes/class-seo-auto-optimizer.php:358
msgid "API Key"
msgstr ""

#: includes/class-seo-auto-optimizer.php:366
msgid "Auto Optimize"
msgstr ""

#: includes/class-seo-auto-optimizer.php:376
msgid "Configure the general settings for SEO Auto Optimizer."
msgstr ""

#: includes/class-seo-auto-optimizer.php:387
msgid "Enter your AI service API key."
msgstr ""

#: includes/class-seo-auto-optimizer.php:399
msgid "Automatically optimize content on save"
msgstr ""

#: includes/class-seo-auto-optimizer.php:409
#: includes/class-seo-auto-optimizer.php:427
msgid "You do not have sufficient permissions to access this page."
msgstr ""

#: includes/class-seo-auto-optimizer.php:413
msgid "Welcome to SEO Auto Optimizer with AI."
msgstr ""

#: includes/class-seo-auto-optimizer.php:431
msgid "SEO Auto Optimizer Settings"
msgstr ""

#: includes/class-seo-auto-optimizer.php:453
msgid "Content is required."
msgstr ""

#: includes/class-seo-auto-optimizer.php:460
msgid "Add more keywords to your content."
msgstr ""

#: includes/class-seo-auto-optimizer.php:461
msgid "Improve your meta description."
msgstr ""

#: includes/class-seo-auto-optimizer.php:481
msgid "[Optimized by AI]"
msgstr ""

#: includes/class-seo-auto-optimizer.php:486
msgid "Content optimized successfully."
msgstr ""

#: includes/class-seo-auto-optimizer.php:176
msgid "SEO Plugins"
msgstr ""

#: includes/class-plugin-detector.php:245
msgid "Security check failed."
msgstr ""

#: includes/class-plugin-detector.php:253
msgid "You do not have permission to perform this action."
msgstr ""

#: includes/class-plugin-detector.php:261
msgid "Plugin detection refreshed successfully."
msgstr ""

#: includes/class-plugin-detector.php:275
msgid "This page shows all detected SEO plugins on your WordPress site. Our plugin can work alongside these plugins to provide additional optimization features."
msgstr ""

#: includes/class-plugin-detector.php:279
msgid "Refresh Detection"
msgstr ""

#: includes/class-plugin-detector.php:285
msgid "Primary SEO Plugin"
msgstr ""

#: includes/class-plugin-detector.php:294
msgid "Detected SEO Plugins"
msgstr ""

#: includes/class-plugin-detector.php:298
msgid "No SEO plugins detected. You can install our plugin as your primary SEO solution."
msgstr ""

#: includes/class-plugin-detector.php:306
msgid "Compatibility Information"
msgstr ""

#: includes/class-plugin-detector.php:318
msgid "Active"
msgstr ""

#: includes/class-plugin-detector.php:319
msgid "Compatible"
msgstr ""

#: includes/class-plugin-detector.php:321
msgid "Not Installed"
msgstr ""

#: includes/class-plugin-detector.php:326
msgid "Learn More"
msgstr ""

#: includes/class-plugin-detector.php:340
msgid "No SEO plugins detected."
msgstr ""

#: includes/class-plugin-detector.php:346
msgid "Plugin Name"
msgstr ""

#: includes/class-plugin-detector.php:347
msgid "Version"
msgstr ""

#: includes/class-plugin-detector.php:348
msgid "Author"
msgstr ""

#: includes/class-plugin-detector.php:349
msgid "Status"
msgstr ""

#: includes/class-plugin-detector.php:350
msgid "Compatibility"
msgstr ""

#: includes/class-plugin-detector.php:358
msgid "Plugin Homepage"
msgstr ""

#: includes/class-plugin-detector.php:360
#: includes/class-plugin-detector.php:365
#: includes/class-plugin-detector.php:370
msgid "Unknown"
msgstr ""

#: includes/class-plugin-detector.php:495
msgid "Primary SEO Plugin"
msgstr ""

#: includes/class-plugin-detector.php:507
msgid "By:"
msgstr ""

#: includes/class-optimization-interface.php:95
msgid "SEO Auto Optimizer"
msgstr ""

#: includes/class-optimization-interface.php:115
msgid "Optimize your content with AI-powered SEO suggestions."
msgstr ""

#: includes/class-optimization-interface.php:119
msgid "Optimize with AI"
msgstr ""

#: includes/class-optimization-interface.php:123
msgid "Analyzing content..."
msgstr ""

#: includes/class-optimization-interface.php:145
msgid "SEO Optimize"
msgstr ""

#: includes/class-optimization-interface.php:200
msgid "Optimizing content..."
msgstr ""

#: includes/class-optimization-interface.php:201
msgid "Analyzing content..."
msgstr ""

#: includes/class-optimization-interface.php:202
msgid "Generating keywords..."
msgstr ""

#: includes/class-optimization-interface.php:203
msgid "Optimization completed!"
msgstr ""

#: includes/class-optimization-interface.php:204
msgid "An error occurred. Please try again."
msgstr ""

#: includes/class-optimization-interface.php:205
msgid "Too many requests. Please wait a moment."
msgstr ""

#: includes/class-optimization-interface.php:206
msgid "Please add some content before optimizing."
msgstr ""

#: includes/class-optimization-interface.php:207
#: includes/class-optimization-interface.php:208
#: includes/class-optimization-interface.php:209
msgid "Apply"
msgstr ""

#: includes/class-optimization-interface.php:207
#: includes/class-optimization-interface.php:208
#: includes/class-optimization-interface.php:209
msgid "Cancel"
msgstr ""

#: includes/class-optimization-interface.php:207
#: includes/class-optimization-interface.php:208
#: includes/class-optimization-interface.php:209
msgid "Close"
msgstr ""

#: includes/class-optimization-interface.php:210
msgid "SEO Optimization Results"
msgstr ""

#: includes/class-optimization-interface.php:211
msgid "Suggested Keywords"
msgstr ""

#: includes/class-optimization-interface.php:212
msgid "Meta Description"
msgstr ""

#: includes/class-optimization-interface.php:213
msgid "Title Suggestion"
msgstr ""

#: includes/class-optimization-interface.php:225
msgid "Security check failed."
msgstr ""

#: includes/class-optimization-interface.php:232
msgid "Invalid post ID."
msgstr ""

#: includes/class-optimization-interface.php:240
msgid "You do not have permission to edit this post."
msgstr ""

#: includes/class-optimization-interface.php:248
msgid "Too many requests. Please wait a moment before trying again."
msgstr ""

#: includes/class-optimization-interface.php:257
msgid "Post not found."
msgstr ""

#: includes/class-optimization-interface.php:267
msgid "Content is required for optimization."
msgstr ""

#: includes/class-optimization-interface.php:281
msgid "An error occurred during optimization. Please try again."
msgstr ""

#: includes/class-optimization-interface.php:420
msgid "Consider making your title longer (30-60 characters) for better SEO."
msgstr ""

#: includes/class-optimization-interface.php:422
msgid "Your title is too long. Consider shortening it to under 60 characters."
msgstr ""

#: includes/class-optimization-interface.php:428
msgid "Add more content. Aim for at least 300 words for better SEO performance."
msgstr ""

#: includes/class-optimization-interface.php:433
msgid "Add headings (H1, H2, H3) to improve content structure."
msgstr ""

#: includes/class-optimization-interface.php:437
msgid "Consider adding bullet points or numbered lists to improve readability."
msgstr ""

#: includes/class-optimization-interface.php:441
msgid "Add a compelling meta description to improve click-through rates."
msgstr ""

# SEO Data Injection System
#: includes/class-seo-data-injector.php:45
msgid "SEO data injected successfully."
msgstr ""

#: includes/class-seo-data-injector.php:46
msgid "Failed to inject SEO data."
msgstr ""

#: includes/class-seo-data-injector.php:47
msgid "No SEO plugins detected."
msgstr ""

#: includes/class-seo-data-injector.php:48
msgid "Invalid post ID."
msgstr ""

#: includes/class-seo-data-injector.php:49
msgid "You do not have permission to edit this post."
msgstr ""

#: includes/class-seo-data-injector.php:50
msgid "Primary keyword is required."
msgstr ""

#: includes/class-seo-data-injector.php:51
msgid "Meta description is required."
msgstr ""

#: includes/class-seo-data-injector.php:52
msgid "Primary keyword must be between 1 and 100 characters."
msgstr ""

#: includes/class-seo-data-injector.php:53
msgid "Meta description must be between 120 and 160 characters."
msgstr ""

#: includes/class-seo-data-injector.php:54
msgid "Additional keywords must be an array."
msgstr ""

#: includes/class-seo-data-injector.php:55
msgid "Too many additional keywords. Maximum 10 allowed."
msgstr ""

#: includes/class-seo-data-injector.php:56
msgid "Backup created successfully."
msgstr ""

#: includes/class-seo-data-injector.php:57
msgid "Failed to create backup."
msgstr ""

#: includes/class-seo-data-injector.php:58
msgid "Backup restored successfully."
msgstr ""

#: includes/class-seo-data-injector.php:59
msgid "Failed to restore backup."
msgstr ""

#: includes/class-seo-data-injector.php:60
msgid "No backup found for this post."
msgstr ""

#: includes/class-seo-data-injector.php:61
msgid "Keywords generated successfully."
msgstr ""

#: includes/class-seo-data-injector.php:62
msgid "Failed to generate keywords."
msgstr ""

#: includes/class-seo-data-injector.php:63
msgid "Meta description generated successfully."
msgstr ""

#: includes/class-seo-data-injector.php:64
msgid "Failed to generate meta description."
msgstr ""

# SEO Injection Interface
#: includes/class-seo-injection-interface.php:45
msgid "SEO Injection"
msgstr ""

#: includes/class-seo-injection-interface.php:46
msgid "SEO Data Injection"
msgstr ""

#: includes/class-seo-injection-interface.php:47
msgid "Bulk SEO Injection"
msgstr ""

#: includes/class-seo-injection-interface.php:48
msgid "Injection History"
msgstr ""

#: includes/class-seo-injection-interface.php:49
msgid "Current SEO Data"
msgstr ""

#: includes/class-seo-injection-interface.php:50
msgid "Quick Injection"
msgstr ""

#: includes/class-seo-injection-interface.php:51
msgid "Select Post"
msgstr ""

#: includes/class-seo-injection-interface.php:52
msgid "Primary Keyword"
msgstr ""

#: includes/class-seo-injection-interface.php:53
msgid "Meta Description"
msgstr ""

#: includes/class-seo-injection-interface.php:54
msgid "Additional Keywords"
msgstr ""

#: includes/class-seo-injection-interface.php:55
msgid "Enter keywords separated by commas"
msgstr ""

#: includes/class-seo-injection-interface.php:56
msgid "Generate Keywords"
msgstr ""

#: includes/class-seo-injection-interface.php:57
msgid "Generate Description"
msgstr ""

#: includes/class-seo-injection-interface.php:58
msgid "Preview Injection"
msgstr ""

#: includes/class-seo-injection-interface.php:59
msgid "Inject SEO Data"
msgstr ""

#: includes/class-seo-injection-interface.php:60
msgid "Clear Form"
msgstr ""

#: includes/class-seo-injection-interface.php:61
msgid "Detected SEO Plugins"
msgstr ""

#: includes/class-seo-injection-interface.php:62
msgid "No SEO plugins detected. Please install and activate a supported SEO plugin."
msgstr ""

#: includes/class-seo-injection-interface.php:63
msgid "Injection Statistics"
msgstr ""

#: includes/class-seo-injection-interface.php:64
msgid "Total Injections"
msgstr ""

#: includes/class-seo-injection-interface.php:65
msgid "Successful Injections"
msgstr ""

#: includes/class-seo-injection-interface.php:66
msgid "Failed Injections"
msgstr ""

#: includes/class-seo-injection-interface.php:67
msgid "Posts with SEO Data"
msgstr ""

#: includes/class-seo-injection-interface.php:68
msgid "Select Posts"
msgstr ""

#: includes/class-seo-injection-interface.php:69
msgid "Select All"
msgstr ""

#: includes/class-seo-injection-interface.php:70
msgid "Deselect All"
msgstr ""

#: includes/class-seo-injection-interface.php:71
msgid "Bulk Inject"
msgstr ""

#: includes/class-seo-injection-interface.php:72
msgid "Processing..."
msgstr ""

#: includes/class-seo-injection-interface.php:73
msgid "Date"
msgstr ""

#: includes/class-seo-injection-interface.php:74
msgid "Post"
msgstr ""

#: includes/class-seo-injection-interface.php:75
msgid "Action"
msgstr ""

#: includes/class-seo-injection-interface.php:76
msgid "Status"
msgstr ""

#: includes/class-seo-injection-interface.php:77
msgid "Actions"
msgstr ""

#: includes/class-seo-injection-interface.php:78
msgid "Restore"
msgstr ""

#: includes/class-seo-injection-interface.php:79
msgid "View Details"
msgstr ""

#: includes/class-seo-injection-interface.php:80
msgid "No injection history found."
msgstr ""

#: includes/class-seo-injection-interface.php:81
msgid "Plugin"
msgstr ""

#: includes/class-seo-injection-interface.php:82
msgid "Keyword"
msgstr ""

#: includes/class-seo-injection-interface.php:83
msgid "Description"
msgstr ""

#: includes/class-seo-injection-interface.php:84
msgid "Keywords"
msgstr ""

#: includes/class-seo-injection-interface.php:85
msgid "No SEO data found for this post."
msgstr ""

#: includes/class-seo-injection-interface.php:86
msgid "Injection Preview"
msgstr ""

#: includes/class-seo-injection-interface.php:87
msgid "The following changes will be made:"
msgstr ""

#: includes/class-seo-injection-interface.php:88
msgid "Confirm Injection"
msgstr ""

#: includes/class-seo-injection-interface.php:89
msgid "Cancel"
msgstr ""

#: includes/class-seo-injection-interface.php:90
msgid "Bulk Injection Results"
msgstr ""

#: includes/class-seo-injection-interface.php:91
msgid "Injection completed successfully!"
msgstr ""

#: includes/class-seo-injection-interface.php:92
msgid "Some injections failed. Please check the results below."
msgstr ""

#: includes/class-seo-injection-interface.php:93
msgid "Success"
msgstr ""

#: includes/class-seo-injection-interface.php:94
msgid "Failed"
msgstr ""

#: includes/class-seo-injection-interface.php:95
msgid "Close"
msgstr ""

#: includes/class-seo-injection-interface.php:96
msgid "characters"
msgstr ""

#: includes/class-seo-injection-interface.php:97
msgid "Generating..."
msgstr ""

#: includes/class-seo-injection-interface.php:98
msgid "Loading..."
msgstr ""

#: includes/class-seo-injection-interface.php:99
msgid "Error loading data. Please try again."
msgstr ""

#: includes/class-seo-injection-interface.php:100
msgid "Please fill in all required fields."
msgstr ""

#: includes/class-seo-injection-interface.php:101
msgid "Please select at least one post for bulk injection."
msgstr ""

#: includes/class-seo-injection-interface.php:102
msgid "Are you sure you want to restore the backup for this post? This will overwrite the current SEO data."
msgstr ""

#: includes/class-seo-injection-interface.php:103
msgid "Security check failed. Please refresh the page and try again."
msgstr ""

#: includes/class-seo-injection-interface.php:104
msgid "An unexpected error occurred. Please try again."
msgstr ""

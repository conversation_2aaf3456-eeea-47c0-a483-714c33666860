<?php
/**
 * SEO Injection Interface Class
 *
 * This class handles the admin interface for SEO data injection
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * SEO Injection Interface Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_SEO_Injection_Interface {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_SEO_Injection_Interface|null
	 */
	private static $instance = null;

	/**
	 * Data injector instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_SEO_Data_Injector
	 */
	private $data_injector;

	/**
	 * Plugin detector instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Plugin_Detector
	 */
	private $plugin_detector;

	/**
	 * AI keyword generator instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_AI_Keyword_Generator
	 */
	private $keyword_generator;

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_SEO_Injection_Interface Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __wakeup() {}

	/**
	 * Initialize the class
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Initialize dependencies
		$this->data_injector = SEO_Auto_Optimizer_SEO_Data_Injector::get_instance();
		$this->plugin_detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();
		$this->keyword_generator = SEO_Auto_Optimizer_AI_Keyword_Generator::get_instance();

		// Add hooks
		add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
		add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
		add_action( 'wp_ajax_sao_bulk_inject', array( $this, 'ajax_bulk_inject' ) );
		add_action( 'wp_ajax_sao_preview_injection', array( $this, 'ajax_preview_injection' ) );
	}

	/**
	 * Add admin menu
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_admin_menu() {
		add_submenu_page(
			'seo-auto-optimizer',
			__( 'SEO Injection', 'seo-auto-optimizer' ),
			__( 'SEO Injection', 'seo-auto-optimizer' ),
			'manage_options',
			'seo-injection',
			array( $this, 'render_injection_page' )
		);
	}

	/**
	 * Add meta boxes to post edit screens
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function add_meta_boxes() {
		$post_types = get_post_types( array( 'public' => true ), 'names' );

		foreach ( $post_types as $post_type ) {
			add_meta_box(
				'sao-seo-injection',
				__( 'SEO Auto Injection', 'seo-auto-optimizer' ),
				array( $this, 'render_meta_box' ),
				$post_type,
				'side',
				'high'
			);
		}
	}

	/**
	 * Enqueue admin scripts and styles
	 *
	 * @since 1.0.0
	 * @param string $hook Current admin page hook
	 * @return void
	 */
	public function enqueue_admin_scripts( $hook ) {
		// Only load on relevant pages
		if ( ! in_array( $hook, array( 'post.php', 'post-new.php', 'seo-auto-optimizer_page_seo-injection' ), true ) ) {
			return;
		}

		wp_enqueue_script(
			'sao-injection-admin',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'js/injection-admin.js',
			array( 'jquery', 'wp-util' ),
			SEO_AUTO_OPTIMIZER_VERSION,
			true
		);

		wp_enqueue_style(
			'sao-injection-admin',
			SEO_AUTO_OPTIMIZER_ASSETS_URL . 'css/injection-admin.css',
			array(),
			SEO_AUTO_OPTIMIZER_VERSION
		);

		// Localize script
		wp_localize_script(
			'sao-injection-admin',
			'saoInjection',
			array(
				'ajaxUrl'           => admin_url( 'admin-ajax.php' ),
				'nonces'            => array(
					'inject'        => wp_create_nonce( 'sao_inject_keywords' ),
					'restore'       => wp_create_nonce( 'sao_restore_backup' ),
					'history'       => wp_create_nonce( 'sao_get_history' ),
					'bulk_inject'   => wp_create_nonce( 'sao_bulk_inject' ),
					'preview'       => wp_create_nonce( 'sao_preview_injection' ),
				),
				'strings'           => array(
					'confirm_inject'    => __( 'Are you sure you want to inject SEO data? This will modify your post meta.', 'seo-auto-optimizer' ),
					'confirm_restore'   => __( 'Are you sure you want to restore the backup? This will overwrite current SEO data.', 'seo-auto-optimizer' ),
					'confirm_bulk'      => __( 'Are you sure you want to perform bulk injection? This will affect multiple posts.', 'seo-auto-optimizer' ),
					'success'           => __( 'Operation completed successfully!', 'seo-auto-optimizer' ),
					'error'             => __( 'An error occurred. Please try again.', 'seo-auto-optimizer' ),
					'loading'           => __( 'Processing...', 'seo-auto-optimizer' ),
				),
				'detectedPlugins'   => $this->plugin_detector->get_detected_plugins(),
				'supportedPlugins'  => $this->data_injector->get_supported_plugins(),
			)
		);
	}

	/**
	 * Render injection page
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function render_injection_page() {
		$detected_plugins = $this->plugin_detector->get_detected_plugins();
		$active_seo_plugins = array_filter( $detected_plugins, function( $plugin ) {
			return $plugin['active'];
		} );

		?>
		<div class="wrap">
			<h1><?php esc_html_e( 'SEO Data Injection', 'seo-auto-optimizer' ); ?></h1>

			<div class="sao-injection-dashboard">
				<!-- Plugin Status -->
				<div class="sao-card">
					<h2><?php esc_html_e( 'Detected SEO Plugins', 'seo-auto-optimizer' ); ?></h2>
					<?php if ( empty( $active_seo_plugins ) ) : ?>
						<p class="sao-notice sao-notice-warning">
							<?php esc_html_e( 'No active SEO plugins detected. Please install and activate a supported SEO plugin.', 'seo-auto-optimizer' ); ?>
						</p>
					<?php else : ?>
						<div class="sao-plugin-grid">
							<?php foreach ( $active_seo_plugins as $plugin_key => $plugin_data ) : ?>
								<div class="sao-plugin-card sao-plugin-active">
									<h3><?php echo esc_html( $plugin_data['name'] ); ?></h3>
									<p class="sao-plugin-version">
										<?php printf( esc_html__( 'Version: %s', 'seo-auto-optimizer' ), esc_html( $plugin_data['version'] ) ); ?>
									</p>
									<span class="sao-plugin-status sao-status-active">
										<?php esc_html_e( 'Active & Supported', 'seo-auto-optimizer' ); ?>
									</span>
								</div>
							<?php endforeach; ?>
						</div>
					<?php endif; ?>
				</div>

				<!-- Bulk Injection -->
				<?php if ( ! empty( $active_seo_plugins ) ) : ?>
					<div class="sao-card">
						<h2><?php esc_html_e( 'Bulk SEO Injection', 'seo-auto-optimizer' ); ?></h2>
						<form id="sao-bulk-injection-form" class="sao-form">
							<div class="sao-form-row">
								<label for="bulk-post-type"><?php esc_html_e( 'Post Type:', 'seo-auto-optimizer' ); ?></label>
								<select id="bulk-post-type" name="post_type">
									<?php
									$post_types = get_post_types( array( 'public' => true ), 'objects' );
									foreach ( $post_types as $post_type ) :
										?>
										<option value="<?php echo esc_attr( $post_type->name ); ?>">
											<?php echo esc_html( $post_type->label ); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>

							<div class="sao-form-row">
								<label for="bulk-target-plugin"><?php esc_html_e( 'Target Plugin:', 'seo-auto-optimizer' ); ?></label>
								<select id="bulk-target-plugin" name="target_plugin">
									<?php foreach ( $active_seo_plugins as $plugin_key => $plugin_data ) : ?>
										<option value="<?php echo esc_attr( $plugin_key ); ?>">
											<?php echo esc_html( $plugin_data['name'] ); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>

							<div class="sao-form-row">
								<label for="bulk-limit"><?php esc_html_e( 'Number of Posts:', 'seo-auto-optimizer' ); ?></label>
								<input type="number" id="bulk-limit" name="limit" value="10" min="1" max="100">
								<small><?php esc_html_e( 'Maximum 100 posts per batch', 'seo-auto-optimizer' ); ?></small>
							</div>

							<div class="sao-form-row">
								<fieldset>
									<legend><?php esc_html_e( 'Options:', 'seo-auto-optimizer' ); ?></legend>
									<label>
										<input type="checkbox" name="preserve_existing" value="1" checked>
										<?php esc_html_e( 'Preserve existing SEO data', 'seo-auto-optimizer' ); ?>
									</label>
									<label>
										<input type="checkbox" name="create_backup" value="1" checked>
										<?php esc_html_e( 'Create backup before injection', 'seo-auto-optimizer' ); ?>
									</label>
									<label>
										<input type="checkbox" name="auto_meta_desc" value="1" checked>
										<?php esc_html_e( 'Auto-generate meta descriptions', 'seo-auto-optimizer' ); ?>
									</label>
								</fieldset>
							</div>

							<div class="sao-form-actions">
								<button type="submit" class="button button-primary">
									<?php esc_html_e( 'Start Bulk Injection', 'seo-auto-optimizer' ); ?>
								</button>
							</div>
						</form>

						<div id="bulk-injection-progress" class="sao-progress-container" style="display: none;">
							<div class="sao-progress-bar">
								<div class="sao-progress-fill"></div>
							</div>
							<div class="sao-progress-text"></div>
						</div>
					</div>
				<?php endif; ?>

				<!-- Statistics -->
				<div class="sao-card">
					<h2><?php esc_html_e( 'Injection Statistics', 'seo-auto-optimizer' ); ?></h2>
					<?php $this->render_statistics(); ?>
				</div>
			</div>
		</div>
		<?php
	}

	/**
	 * Render meta box for post edit screen
	 *
	 * @since 1.0.0
	 * @param WP_Post $post Current post object
	 * @return void
	 */
	public function render_meta_box( $post ) {
		$detected_plugins = $this->plugin_detector->get_detected_plugins();
		$active_seo_plugins = array_filter( $detected_plugins, function( $plugin ) {
			return $plugin['active'];
		} );

		$injection_history = $this->data_injector->get_injection_history( $post->ID );

		wp_nonce_field( 'sao_meta_box', 'sao_meta_box_nonce' );
		?>
		<div class="sao-meta-box">
			<?php if ( empty( $active_seo_plugins ) ) : ?>
				<p class="sao-notice sao-notice-warning">
					<?php esc_html_e( 'No active SEO plugins detected.', 'seo-auto-optimizer' ); ?>
				</p>
			<?php else : ?>
				<!-- Quick Injection Form -->
				<div class="sao-quick-injection">
					<h4><?php esc_html_e( 'Quick SEO Injection', 'seo-auto-optimizer' ); ?></h4>

					<div class="sao-form-field">
						<label for="sao-target-plugin"><?php esc_html_e( 'Target Plugin:', 'seo-auto-optimizer' ); ?></label>
						<select id="sao-target-plugin" name="target_plugin">
							<?php foreach ( $active_seo_plugins as $plugin_key => $plugin_data ) : ?>
								<option value="<?php echo esc_attr( $plugin_key ); ?>">
									<?php echo esc_html( $plugin_data['name'] ); ?>
								</option>
							<?php endforeach; ?>
						</select>
					</div>

					<div class="sao-form-field">
						<label for="sao-focus-keyword"><?php esc_html_e( 'Focus Keyword:', 'seo-auto-optimizer' ); ?></label>
						<input type="text" id="sao-focus-keyword" name="focus_keyword" placeholder="<?php esc_attr_e( 'Enter focus keyword', 'seo-auto-optimizer' ); ?>">
					</div>

					<div class="sao-form-field">
						<label for="sao-additional-keywords"><?php esc_html_e( 'Additional Keywords:', 'seo-auto-optimizer' ); ?></label>
						<textarea id="sao-additional-keywords" name="additional_keywords" rows="3" placeholder="<?php esc_attr_e( 'Enter additional keywords, separated by commas', 'seo-auto-optimizer' ); ?>"></textarea>
					</div>

					<div class="sao-form-field">
						<label for="sao-meta-description"><?php esc_html_e( 'Meta Description:', 'seo-auto-optimizer' ); ?></label>
						<textarea id="sao-meta-description" name="meta_description" rows="3" placeholder="<?php esc_attr_e( 'Enter meta description (leave empty for auto-generation)', 'seo-auto-optimizer' ); ?>"></textarea>
						<small class="sao-char-counter">0/160</small>
					</div>

					<div class="sao-form-options">
						<label>
							<input type="checkbox" id="sao-preserve-existing" name="preserve_existing" checked>
							<?php esc_html_e( 'Preserve existing data', 'seo-auto-optimizer' ); ?>
						</label>
						<label>
							<input type="checkbox" id="sao-create-backup" name="create_backup" checked>
							<?php esc_html_e( 'Create backup', 'seo-auto-optimizer' ); ?>
						</label>
					</div>

					<div class="sao-form-actions">
						<button type="button" id="sao-generate-keywords" class="button">
							<?php esc_html_e( 'Generate with AI', 'seo-auto-optimizer' ); ?>
						</button>
						<button type="button" id="sao-preview-injection" class="button">
							<?php esc_html_e( 'Preview', 'seo-auto-optimizer' ); ?>
						</button>
						<button type="button" id="sao-inject-now" class="button button-primary">
							<?php esc_html_e( 'Inject Now', 'seo-auto-optimizer' ); ?>
						</button>
					</div>
				</div>

				<!-- Injection History -->
				<?php if ( ! empty( $injection_history ) ) : ?>
					<div class="sao-injection-history">
						<h4><?php esc_html_e( 'Recent Injections', 'seo-auto-optimizer' ); ?></h4>
						<div class="sao-history-list">
							<?php foreach ( array_slice( $injection_history, 0, 3 ) as $entry ) : ?>
								<div class="sao-history-item">
									<div class="sao-history-meta">
										<span class="sao-history-date">
											<?php echo esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), $entry['timestamp'] ) ); ?>
										</span>
										<span class="sao-history-plugin">
											<?php echo esc_html( ucfirst( $entry['plugin_name'] ) ); ?>
										</span>
									</div>
									<?php if ( $entry['action'] === 'injection' && ! empty( $entry['keywords']['focus_keyword'] ) ) : ?>
										<div class="sao-history-keyword">
											<?php echo esc_html( $entry['keywords']['focus_keyword'] ); ?>
										</div>
									<?php endif; ?>
									<button type="button" class="sao-restore-backup button-link" data-plugin="<?php echo esc_attr( $entry['plugin_name'] ); ?>">
										<?php esc_html_e( 'Restore', 'seo-auto-optimizer' ); ?>
									</button>
								</div>
							<?php endforeach; ?>
						</div>
					</div>
				<?php endif; ?>
			<?php endif; ?>

			<!-- Current SEO Data Preview -->
			<div class="sao-current-seo">
				<h4><?php esc_html_e( 'Current SEO Data', 'seo-auto-optimizer' ); ?></h4>
				<div id="sao-current-seo-data">
					<?php $this->render_current_seo_data( $post->ID, $active_seo_plugins ); ?>
				</div>
			</div>
		</div>

		<script type="text/javascript">
			jQuery(document).ready(function($) {
				// Character counter for meta description
				$('#sao-meta-description').on('input', function() {
					var length = $(this).val().length;
					$('.sao-char-counter').text(length + '/160');
					if (length > 160) {
						$('.sao-char-counter').addClass('sao-over-limit');
					} else {
						$('.sao-char-counter').removeClass('sao-over-limit');
					}
				});

				// Set post ID for AJAX calls
				if (typeof saoInjection !== 'undefined') {
					saoInjection.postId = <?php echo intval( $post->ID ); ?>;
				}
			});
		</script>
		<?php
	}

	/**
	 * Render current SEO data
	 *
	 * @since 1.0.0
	 * @param int   $post_id Post ID
	 * @param array $active_plugins Active SEO plugins
	 * @return void
	 */
	private function render_current_seo_data( $post_id, $active_plugins ) {
		foreach ( $active_plugins as $plugin_key => $plugin_data ) {
			$meta_fields = $this->data_injector->get_plugin_meta_fields( $plugin_key );
			if ( ! $meta_fields ) {
				continue;
			}

			echo '<div class="sao-plugin-seo-data">';
			echo '<h5>' . esc_html( $plugin_data['name'] ) . '</h5>';

			foreach ( $meta_fields as $field_type => $meta_key ) {
				$value = get_post_meta( $post_id, $meta_key, true );
				$display_value = ! empty( $value ) ? $value : __( 'Not set', 'seo-auto-optimizer' );

				echo '<div class="sao-seo-field">';
				echo '<strong>' . esc_html( ucfirst( str_replace( '_', ' ', $field_type ) ) ) . ':</strong> ';
				echo '<span class="' . ( empty( $value ) ? 'sao-empty-value' : 'sao-has-value' ) . '">';
				echo esc_html( $display_value );
				echo '</span>';
				echo '</div>';
			}

			echo '</div>';
		}
	}

	/**
	 * Render statistics
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function render_statistics() {
		// Get statistics from database
		$stats = $this->get_injection_statistics();

		?>
		<div class="sao-stats-grid">
			<div class="sao-stat-item">
				<div class="sao-stat-number"><?php echo esc_html( $stats['total_injections'] ); ?></div>
				<div class="sao-stat-label"><?php esc_html_e( 'Total Injections', 'seo-auto-optimizer' ); ?></div>
			</div>
			<div class="sao-stat-item">
				<div class="sao-stat-number"><?php echo esc_html( $stats['posts_with_seo'] ); ?></div>
				<div class="sao-stat-label"><?php esc_html_e( 'Posts with SEO Data', 'seo-auto-optimizer' ); ?></div>
			</div>
			<div class="sao-stat-item">
				<div class="sao-stat-number"><?php echo esc_html( $stats['success_rate'] ); ?>%</div>
				<div class="sao-stat-label"><?php esc_html_e( 'Success Rate', 'seo-auto-optimizer' ); ?></div>
			</div>
			<div class="sao-stat-item">
				<div class="sao-stat-number"><?php echo esc_html( $stats['last_injection'] ); ?></div>
				<div class="sao-stat-label"><?php esc_html_e( 'Last Injection', 'seo-auto-optimizer' ); ?></div>
			</div>
		</div>
		<?php
	}

	/**
	 * Get injection statistics
	 *
	 * @since 1.0.0
	 * @return array Statistics data
	 */
	private function get_injection_statistics() {
		// This would typically query the database for real statistics
		// For now, return mock data
		return array(
			'total_injections' => 0,
			'posts_with_seo'   => 0,
			'success_rate'     => 0,
			'last_injection'   => __( 'Never', 'seo-auto-optimizer' ),
		);
	}

	/**
	 * AJAX handler for bulk injection
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_bulk_inject() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'sao_bulk_inject' ) ) {
			wp_die( __( 'Security check failed.', 'seo-auto-optimizer' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'Insufficient permissions.', 'seo-auto-optimizer' ) );
		}

		$post_type = sanitize_text_field( $_POST['post_type'] );
		$target_plugin = sanitize_text_field( $_POST['target_plugin'] );
		$limit = intval( $_POST['limit'] );
		$options = isset( $_POST['options'] ) ? $_POST['options'] : array();

		// Get posts to process
		$posts = get_posts( array(
			'post_type'      => $post_type,
			'post_status'    => 'publish',
			'numberposts'    => $limit,
			'meta_query'     => array(
				array(
					'key'     => '_sao_processed',
					'compare' => 'NOT EXISTS',
				),
			),
		) );

		$results = array();
		$processed = 0;
		$errors = 0;

		foreach ( $posts as $post ) {
			// Generate keywords for this post
			$keywords = $this->keyword_generator->generate_keywords_for_post( $post->ID );

			if ( is_wp_error( $keywords ) ) {
				$errors++;
				continue;
			}

			// Inject keywords
			$injection_result = $this->data_injector->inject_keywords(
				$post->ID,
				$keywords,
				$target_plugin,
				$options
			);

			if ( ! is_wp_error( $injection_result ) ) {
				$processed++;
				// Mark as processed
				update_post_meta( $post->ID, '_sao_processed', current_time( 'timestamp' ) );
			} else {
				$errors++;
			}

			$results[] = array(
				'post_id' => $post->ID,
				'title'   => $post->post_title,
				'success' => ! is_wp_error( $injection_result ),
				'message' => is_wp_error( $injection_result ) ? $injection_result->get_error_message() : __( 'Success', 'seo-auto-optimizer' ),
			);
		}

		wp_send_json_success( array(
			'processed' => $processed,
			'errors'    => $errors,
			'total'     => count( $posts ),
			'results'   => $results,
		) );
	}

	/**
	 * AJAX handler for injection preview
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_preview_injection() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'sao_preview_injection' ) ) {
			wp_die( __( 'Security check failed.', 'seo-auto-optimizer' ) );
		}

		$post_id = intval( $_POST['post_id'] );
		$plugin_name = sanitize_text_field( $_POST['plugin_name'] );
		$keywords = $_POST['keywords'];

		// Check permissions
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			wp_send_json_error( __( 'Insufficient permissions.', 'seo-auto-optimizer' ) );
		}

		// Get current values
		$meta_fields = $this->data_injector->get_plugin_meta_fields( $plugin_name );
		$current_values = array();
		$preview_values = array();

		if ( $meta_fields ) {
			foreach ( $meta_fields as $field_type => $meta_key ) {
				$current_values[ $field_type ] = get_post_meta( $post_id, $meta_key, true );

				// Set preview values
				switch ( $field_type ) {
					case 'focus_keyword':
						$preview_values[ $field_type ] = ! empty( $keywords['focus_keyword'] ) ? $keywords['focus_keyword'] : $current_values[ $field_type ];
						break;
					case 'additional_keywords':
						$preview_values[ $field_type ] = ! empty( $keywords['additional_keywords'] ) ? implode( ', ', $keywords['additional_keywords'] ) : $current_values[ $field_type ];
						break;
					case 'meta_description':
						$preview_values[ $field_type ] = ! empty( $keywords['meta_description'] ) ? $keywords['meta_description'] : $current_values[ $field_type ];
						break;
					default:
						$preview_values[ $field_type ] = $current_values[ $field_type ];
				}
			}
		}

		wp_send_json_success( array(
			'current'  => $current_values,
			'preview'  => $preview_values,
			'changes'  => $this->get_preview_changes( $current_values, $preview_values ),
		) );
	}

	/**
	 * Get preview changes
	 *
	 * @since 1.0.0
	 * @param array $current Current values
	 * @param array $preview Preview values
	 * @return array Changes array
	 */
	private function get_preview_changes( $current, $preview ) {
		$changes = array();

		foreach ( $preview as $field => $new_value ) {
			$old_value = isset( $current[ $field ] ) ? $current[ $field ] : '';

			if ( $old_value !== $new_value ) {
				$changes[ $field ] = array(
					'old' => $old_value,
					'new' => $new_value,
					'action' => empty( $old_value ) ? 'add' : 'update',
				);
			}
		}

		return $changes;
	}
}
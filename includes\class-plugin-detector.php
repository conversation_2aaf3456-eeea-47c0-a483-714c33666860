<?php
/**
 * SEO Plugin Detector Class
 *
 * This class detects active SEO plugins and provides information about them
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * SEO Plugin Detector Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_Plugin_Detector {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Plugin_Detector|null
	 */
	private static $instance = null;

	/**
	 * Cache key for transient
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $cache_key = 'seo_auto_optimizer_detected_plugins';

	/**
	 * Cache expiration time (in seconds)
	 *
	 * @since 1.0.0
	 * @var int
	 */
	private $cache_expiration = 3600; // 1 hour

	/**
	 * Known SEO plugins configuration
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $known_plugins = array(
		'rank_math' => array(
			'name'        => 'Rank Math SEO',
			'file'        => 'seo-by-rank-math/rank-math.php',
			'class'       => 'RankMath',
			'function'    => 'rank_math',
			'constant'    => 'RANK_MATH_VERSION',
			'description' => 'Advanced SEO plugin with AI-powered features',
			'website'     => 'https://rankmath.com/',
		),
		'yoast' => array(
			'name'        => 'Yoast SEO',
			'file'        => 'wordpress-seo/wp-seo.php',
			'class'       => 'WPSEO',
			'function'    => 'wpseo_init',
			'constant'    => 'WPSEO_VERSION',
			'description' => 'The most popular WordPress SEO plugin',
			'website'     => 'https://yoast.com/',
		),
		'seopress' => array(
			'name'        => 'SEOPress',
			'file'        => 'wp-seopress/seopress.php',
			'class'       => 'SEOPress',
			'function'    => 'seopress_init',
			'constant'    => 'SEOPRESS_VERSION',
			'description' => 'Simple and powerful SEO plugin',
			'website'     => 'https://seopress.org/',
		),
		'aioseo' => array(
			'name'        => 'All in One SEO',
			'file'        => 'all-in-one-seo-pack/all_in_one_seo_pack.php',
			'class'       => 'All_in_One_SEO_Pack',
			'function'    => 'aioseo',
			'constant'    => 'AIOSEO_VERSION',
			'description' => 'Comprehensive SEO toolkit for WordPress',
			'website'     => 'https://aioseo.com/',
		),
	);

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_Plugin_Detector Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function __wakeup() {}

	/**
	 * Initialize the detector
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Hook into plugin activation/deactivation to clear cache
		add_action( 'activated_plugin', array( $this, 'clear_cache' ) );
		add_action( 'deactivated_plugin', array( $this, 'clear_cache' ) );
		
		// Add AJAX handlers for admin interface
		add_action( 'wp_ajax_seo_auto_optimizer_refresh_plugins', array( $this, 'ajax_refresh_plugins' ) );
	}

	/**
	 * Get all active SEO plugins
	 *
	 * @since 1.0.0
	 * @param bool $force_refresh Force refresh cache
	 * @return array Array of active SEO plugins
	 */
	public function get_active_seo_plugins( $force_refresh = false ) {
		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			return array();
		}

		// Try to get from cache first
		if ( ! $force_refresh ) {
			$cached_plugins = get_transient( $this->cache_key );
			if ( false !== $cached_plugins && is_array( $cached_plugins ) ) {
				return $cached_plugins;
			}
		}

		$active_plugins = array();

		// Check each known plugin
		foreach ( $this->known_plugins as $plugin_key => $plugin_config ) {
			try {
				if ( $this->is_plugin_active( $plugin_key ) ) {
					$plugin_info = $this->get_plugin_info( $plugin_key );
					if ( $plugin_info ) {
						$active_plugins[ $plugin_key ] = $plugin_info;
					}
				}
			} catch ( Exception $e ) {
				// Log error but continue checking other plugins
				error_log( 'SEO Auto Optimizer: Error detecting plugin ' . $plugin_key . ': ' . $e->getMessage() );
			}
		}

		// Cache the results
		set_transient( $this->cache_key, $active_plugins, $this->cache_expiration );

		return $active_plugins;
	}

	/**
	 * Get the primary SEO plugin
	 *
	 * @since 1.0.0
	 * @return array|null Primary SEO plugin info or null if none found
	 */
	public function get_primary_seo_plugin() {
		$active_plugins = $this->get_active_seo_plugins();

		if ( empty( $active_plugins ) ) {
			return null;
		}

		// Priority order for determining primary plugin
		$priority_order = array( 'rank_math', 'yoast', 'seopress', 'aioseo' );

		foreach ( $priority_order as $plugin_key ) {
			if ( isset( $active_plugins[ $plugin_key ] ) ) {
				return $active_plugins[ $plugin_key ];
			}
		}

		// If no priority plugin found, return the first one
		return reset( $active_plugins );
	}

	/**
	 * Check if a specific SEO plugin is active
	 *
	 * @since 1.0.0
	 * @param string $plugin_key Plugin key
	 * @return bool True if plugin is active
	 */
	public function is_plugin_active( $plugin_key ) {
		if ( ! isset( $this->known_plugins[ $plugin_key ] ) ) {
			return false;
		}

		$plugin_config = $this->known_plugins[ $plugin_key ];

		// Check if plugin file is active
		if ( ! function_exists( 'is_plugin_active' ) ) {
			require_once ABSPATH . 'wp-admin/includes/plugin.php';
		}

		$is_active = is_plugin_active( $plugin_config['file'] );

		// Additional verification methods
		if ( $is_active ) {
			// Check if class exists
			if ( ! empty( $plugin_config['class'] ) && ! class_exists( $plugin_config['class'] ) ) {
				$is_active = false;
			}

			// Check if function exists
			if ( $is_active && ! empty( $plugin_config['function'] ) && ! function_exists( $plugin_config['function'] ) ) {
				$is_active = false;
			}

			// Check if constant is defined
			if ( $is_active && ! empty( $plugin_config['constant'] ) && ! defined( $plugin_config['constant'] ) ) {
				$is_active = false;
			}
		}

		return $is_active;
	}

	/**
	 * Get detailed information about a plugin
	 *
	 * @since 1.0.0
	 * @param string $plugin_key Plugin key
	 * @return array|false Plugin information or false if not found
	 */
	private function get_plugin_info( $plugin_key ) {
		if ( ! isset( $this->known_plugins[ $plugin_key ] ) ) {
			return false;
		}

		$plugin_config = $this->known_plugins[ $plugin_key ];
		$plugin_info = $plugin_config;

		// Get version if constant is defined
		if ( ! empty( $plugin_config['constant'] ) && defined( $plugin_config['constant'] ) ) {
			$plugin_info['version'] = constant( $plugin_config['constant'] );
		} else {
			$plugin_info['version'] = $this->get_plugin_version( $plugin_config['file'] );
		}

		// Get plugin data from WordPress
		if ( ! function_exists( 'get_plugin_data' ) ) {
			require_once ABSPATH . 'wp-admin/includes/plugin.php';
		}

		$plugin_file_path = WP_PLUGIN_DIR . '/' . $plugin_config['file'];
		if ( file_exists( $plugin_file_path ) ) {
			$plugin_data = get_plugin_data( $plugin_file_path );
			$plugin_info['author'] = sanitize_text_field( $plugin_data['Author'] );
			$plugin_info['author_uri'] = esc_url_raw( $plugin_data['AuthorURI'] );
			$plugin_info['plugin_uri'] = esc_url_raw( $plugin_data['PluginURI'] );
		}

		// Add detection timestamp
		$plugin_info['detected_at'] = current_time( 'timestamp' );
		$plugin_info['key'] = sanitize_key( $plugin_key );

		return $plugin_info;
	}

	/**
	 * Get plugin version from plugin file
	 *
	 * @since 1.0.0
	 * @param string $plugin_file Plugin file path
	 * @return string Plugin version or 'Unknown'
	 */
	private function get_plugin_version( $plugin_file ) {
		if ( ! function_exists( 'get_plugin_data' ) ) {
			require_once ABSPATH . 'wp-admin/includes/plugin.php';
		}

		$plugin_file_path = WP_PLUGIN_DIR . '/' . $plugin_file;
		if ( file_exists( $plugin_file_path ) ) {
			$plugin_data = get_plugin_data( $plugin_file_path );
			return sanitize_text_field( $plugin_data['Version'] );
		}

		return esc_html__( 'Unknown', 'seo-auto-optimizer' );
	}

	/**
	 * Clear detection cache
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function clear_cache() {
		delete_transient( $this->cache_key );
	}

	/**
	 * AJAX handler for refreshing plugin detection
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_refresh_plugins() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'seo_auto_optimizer_nonce' ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'Security check failed.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Check user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error(
				array(
					'message' => esc_html__( 'You do not have permission to perform this action.', 'seo-auto-optimizer' ),
				)
			);
		}

		// Force refresh and get active plugins
		$active_plugins = $this->get_active_seo_plugins( true );

		wp_send_json_success(
			array(
				'plugins' => $active_plugins,
				'html'    => $this->render_plugins_table( $active_plugins ),
				'message' => esc_html__( 'Plugin detection refreshed successfully.', 'seo-auto-optimizer' ),
			)
		);
	}

	/**
	 * Render admin interface for plugin detection
	 *
	 * @since 1.0.0
	 * @return string HTML output for admin interface
	 */
	public function render_admin_interface() {
		$active_plugins = $this->get_active_seo_plugins();
		$primary_plugin = $this->get_primary_seo_plugin();

		$html = '<div class="seo-plugin-detector-admin">';

		// Header section
		$html .= '<div class="seo-detector-header">';
		$html .= '<p>' . esc_html__( 'This page shows all detected SEO plugins on your WordPress site. Our plugin can work alongside these plugins to provide additional optimization features.', 'seo-auto-optimizer' ) . '</p>';

		// Refresh button
		$html .= '<button type="button" class="button button-secondary seo-refresh-plugins" data-nonce="' . wp_create_nonce( 'seo_auto_optimizer_nonce' ) . '">';
		$html .= '<span class="dashicons dashicons-update"></span> ' . esc_html__( 'Refresh Detection', 'seo-auto-optimizer' );
		$html .= '</button>';
		$html .= '</div>';

		// Primary plugin section
		if ( $primary_plugin ) {
			$html .= '<div class="seo-primary-plugin">';
			$html .= '<h2>' . esc_html__( 'Primary SEO Plugin', 'seo-auto-optimizer' ) . '</h2>';
			$html .= '<div class="seo-plugin-card primary">';
			$html .= $this->render_plugin_card( $primary_plugin, true );
			$html .= '</div>';
			$html .= '</div>';
		}

		// All detected plugins section
		$html .= '<div class="seo-detected-plugins">';
		$html .= '<h2>' . esc_html__( 'Detected SEO Plugins', 'seo-auto-optimizer' ) . '</h2>';

		if ( empty( $active_plugins ) ) {
			$html .= '<div class="seo-no-plugins">';
			$html .= '<p>' . esc_html__( 'No SEO plugins detected. You can install our plugin as your primary SEO solution.', 'seo-auto-optimizer' ) . '</p>';
			$html .= '</div>';
		} else {
			$html .= '<div id="seo-plugins-table-container">';
			$html .= $this->render_plugins_table( $active_plugins );
			$html .= '</div>';
		}

		$html .= '</div>';

		// Compatibility section
		$html .= '<div class="seo-compatibility-info">';
		$html .= '<h2>' . esc_html__( 'Compatibility Information', 'seo-auto-optimizer' ) . '</h2>';
		$html .= '<div class="seo-compatibility-grid">';

		foreach ( $this->known_plugins as $plugin_key => $plugin_config ) {
			$is_active = isset( $active_plugins[ $plugin_key ] );
			$status_class = $is_active ? 'active' : 'inactive';

			$html .= '<div class="seo-compatibility-item ' . esc_attr( $status_class ) . '">';
			$html .= '<h4>' . esc_html( $plugin_config['name'] ) . '</h4>';
			$html .= '<p>' . esc_html( $plugin_config['description'] ) . '</p>';
			$html .= '<div class="seo-compatibility-status">';

			if ( $is_active ) {
				$html .= '<span class="status-badge active">' . esc_html__( 'Active', 'seo-auto-optimizer' ) . '</span>';
				$html .= '<span class="compatibility-badge compatible">' . esc_html__( 'Compatible', 'seo-auto-optimizer' ) . '</span>';
			} else {
				$html .= '<span class="status-badge inactive">' . esc_html__( 'Not Installed', 'seo-auto-optimizer' ) . '</span>';
				$html .= '<span class="compatibility-badge compatible">' . esc_html__( 'Compatible', 'seo-auto-optimizer' ) . '</span>';
			}

			$html .= '</div>';
			$html .= '<a href="' . esc_url( $plugin_config['website'] ) . '" target="_blank" class="plugin-website">' . esc_html__( 'Learn More', 'seo-auto-optimizer' ) . '</a>';
			$html .= '</div>';
		}

		$html .= '</div>';
		$html .= '</div>';

		$html .= '</div>';

		return $html;
	}

	/**
	 * Render plugins table
	 *
	 * @since 1.0.0
	 * @param array $plugins Array of active plugins
	 * @return string HTML table
	 */
	private function render_plugins_table( $plugins ) {
		if ( empty( $plugins ) ) {
			return '<p>' . esc_html__( 'No SEO plugins detected.', 'seo-auto-optimizer' ) . '</p>';
		}

		$html = '<table class="wp-list-table widefat fixed striped seo-plugins-table">';
		$html .= '<thead>';
		$html .= '<tr>';
		$html .= '<th>' . esc_html__( 'Plugin Name', 'seo-auto-optimizer' ) . '</th>';
		$html .= '<th>' . esc_html__( 'Version', 'seo-auto-optimizer' ) . '</th>';
		$html .= '<th>' . esc_html__( 'Author', 'seo-auto-optimizer' ) . '</th>';
		$html .= '<th>' . esc_html__( 'Status', 'seo-auto-optimizer' ) . '</th>';
		$html .= '<th>' . esc_html__( 'Compatibility', 'seo-auto-optimizer' ) . '</th>';
		$html .= '</tr>';
		$html .= '</thead>';
		$html .= '<tbody>';

		foreach ( $plugins as $plugin_key => $plugin_info ) {
			$html .= '<tr>';
			$html .= '<td>';
			$html .= '<strong>' . esc_html( $plugin_info['name'] ) . '</strong>';
			if ( ! empty( $plugin_info['plugin_uri'] ) ) {
				$html .= '<br><a href="' . esc_url( $plugin_info['plugin_uri'] ) . '" target="_blank">' . esc_html__( 'Plugin Homepage', 'seo-auto-optimizer' ) . '</a>';
			}
			$html .= '</td>';
			$html .= '<td>' . esc_html( $plugin_info['version'] ?? __( 'Unknown', 'seo-auto-optimizer' ) ) . '</td>';
			$html .= '<td>';
			if ( ! empty( $plugin_info['author'] ) ) {
				if ( ! empty( $plugin_info['author_uri'] ) ) {
					$html .= '<a href="' . esc_url( $plugin_info['author_uri'] ) . '" target="_blank">' . esc_html( $plugin_info['author'] ) . '</a>';
				} else {
					$html .= esc_html( $plugin_info['author'] );
				}
			} else {
				$html .= esc_html__( 'Unknown', 'seo-auto-optimizer' );
			}
			$html .= '</td>';
			$html .= '<td><span class="status-badge active">' . esc_html__( 'Active', 'seo-auto-optimizer' ) . '</span></td>';
			$html .= '<td><span class="compatibility-badge compatible">' . esc_html__( 'Compatible', 'seo-auto-optimizer' ) . '</span></td>';
			$html .= '</tr>';
		}

		$html .= '</tbody>';
		$html .= '</table>';

		return $html;
	}

	/**
	 * Render individual plugin card
	 *
	 * @since 1.0.0
	 * @param array $plugin_info Plugin information
	 * @param bool  $is_primary Whether this is the primary plugin
	 * @return string HTML card
	 */
	private function render_plugin_card( $plugin_info, $is_primary = false ) {
		$html = '<div class="seo-plugin-info">';
		$html .= '<h3>' . esc_html( $plugin_info['name'] ) . '</h3>';
		$html .= '<p class="plugin-description">' . esc_html( $plugin_info['description'] ) . '</p>';

		$html .= '<div class="plugin-meta">';
		$html .= '<span class="plugin-version">' . esc_html__( 'Version:', 'seo-auto-optimizer' ) . ' ' . esc_html( $plugin_info['version'] ?? __( 'Unknown', 'seo-auto-optimizer' ) ) . '</span>';

		if ( ! empty( $plugin_info['author'] ) ) {
			$html .= ' | <span class="plugin-author">' . esc_html__( 'By:', 'seo-auto-optimizer' ) . ' ';
			if ( ! empty( $plugin_info['author_uri'] ) ) {
				$html .= '<a href="' . esc_url( $plugin_info['author_uri'] ) . '" target="_blank">' . esc_html( $plugin_info['author'] ) . '</a>';
			} else {
				$html .= esc_html( $plugin_info['author'] );
			}
			$html .= '</span>';
		}
		$html .= '</div>';

		if ( $is_primary ) {
			$html .= '<div class="primary-badge">' . esc_html__( 'Primary SEO Plugin', 'seo-auto-optimizer' ) . '</div>';
		}

		$html .= '</div>';

		return $html;
	}

	/**
	 * Get plugin statistics
	 *
	 * @since 1.0.0
	 * @return array Plugin statistics
	 */
	public function get_plugin_statistics() {
		$active_plugins = $this->get_active_seo_plugins();
		$primary_plugin = $this->get_primary_seo_plugin();

		return array(
			'total_detected'    => count( $active_plugins ),
			'primary_plugin'    => $primary_plugin ? $primary_plugin['key'] : null,
			'has_conflicts'     => count( $active_plugins ) > 1,
			'last_checked'      => get_transient( $this->cache_key . '_timestamp' ) ?: current_time( 'timestamp' ),
			'cache_expires_in'  => $this->cache_expiration - ( current_time( 'timestamp' ) - ( get_transient( $this->cache_key . '_timestamp' ) ?: 0 ) ),
		);
	}
}

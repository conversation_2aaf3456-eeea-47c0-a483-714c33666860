<?php
/**
 * Test AI Connection
 */

// WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/../../../');
    require_once ABSPATH . 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
}

echo "<h1>🤖 TEST CONNEXION AI</h1>\n\n";

// 1. Test des options sauvegardées
echo "<h2>1. Options AI Sauvegardées</h2>\n";
$ai_options = get_option('sao_ai_options', array());

if (empty($ai_options)) {
    echo "❌ Aucune option AI sauvegardée\n";
    echo "💡 Allez dans Configuration pour sauvegarder vos clés API\n";
} else {
    echo "✅ Options AI trouvées:\n";
    foreach ($ai_options as $key => $value) {
        if (strpos($key, 'api_key') !== false) {
            $masked = !empty($value) ? substr($value, 0, 4) . '***' . substr($value, -4) : '(vide)';
            echo "  {$key}: {$masked}\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
}

// 2. Test de la classe AI Generator
echo "\n<h2>2. Classe AI Generator</h2>\n";
if (class_exists('SEO_Auto_Optimizer_AI_Keyword_Generator')) {
    echo "✅ Classe AI Generator existe\n";
    
    try {
        $generator = SEO_Auto_Optimizer_AI_Keyword_Generator::get_instance();
        echo "✅ Instance créée avec succès\n";
    } catch (Exception $e) {
        echo "❌ Erreur création instance: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Classe AI Generator n'existe pas\n";
}

// 3. Test simple de génération
echo "\n<h2>3. Test Génération Simple</h2>\n";
if (isset($generator) && !empty($ai_options)) {
    
    // Vérifier qu'on a au moins une clé API
    $has_api_key = false;
    foreach (['openai_api_key', 'gemini_api_key'] as $key) {
        if (!empty($ai_options[$key])) {
            $has_api_key = true;
            echo "✅ Clé API {$key} configurée\n";
            break;
        }
    }
    
    if ($has_api_key) {
        echo "🔄 Test de génération de mots-clés...\n";
        
        try {
            $result = $generator->generate_keywords(
                'Test SEO WordPress',
                'Ceci est un test de génération de mots-clés pour WordPress SEO.'
            );
            
            if ($result['success']) {
                echo "✅ Génération réussie!\n";
                echo "🎯 Mots-clés générés:\n";
                foreach ($result['keywords'] as $keyword) {
                    echo "  - {$keyword}\n";
                }
            } else {
                echo "❌ Génération échouée: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Exception: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "❌ Aucune clé API configurée\n";
        echo "💡 Configurez au moins OpenAI ou Gemini dans les paramètres\n";
    }
} else {
    echo "⚠️ Impossible de tester - classe ou options manquantes\n";
}

// 4. Test des endpoints
echo "\n<h2>4. Test Connectivité</h2>\n";

// Test OpenAI
if (!empty($ai_options['openai_api_key'])) {
    echo "🔄 Test OpenAI...\n";
    $response = wp_remote_get('https://api.openai.com/v1/models', [
        'headers' => [
            'Authorization' => 'Bearer ' . $ai_options['openai_api_key']
        ],
        'timeout' => 10
    ]);
    
    if (!is_wp_error($response)) {
        $code = wp_remote_retrieve_response_code($response);
        if ($code === 200) {
            echo "✅ OpenAI accessible\n";
        } else {
            echo "❌ OpenAI erreur {$code}\n";
        }
    } else {
        echo "❌ OpenAI erreur: " . $response->get_error_message() . "\n";
    }
}

// Test Gemini
if (!empty($ai_options['gemini_api_key'])) {
    echo "🔄 Test Gemini...\n";
    $response = wp_remote_get('https://generativelanguage.googleapis.com/v1/models?key=' . $ai_options['gemini_api_key'], [
        'timeout' => 10
    ]);
    
    if (!is_wp_error($response)) {
        $code = wp_remote_retrieve_response_code($response);
        if ($code === 200) {
            echo "✅ Gemini accessible\n";
        } else {
            echo "❌ Gemini erreur {$code}\n";
        }
    } else {
        echo "❌ Gemini erreur: " . $response->get_error_message() . "\n";
    }
}

echo "\n<h2>🎯 RÉSUMÉ</h2>\n";
echo "Pour que l'AI fonctionne :\n";
echo "1. Configurez vos clés API dans SEO Optimizer > Configuration\n";
echo "2. Testez la connexion avec les boutons 'Test Connection'\n";
echo "3. Vérifiez que votre serveur peut accéder aux APIs externes\n";
?>

/**
 * SEO Auto Optimizer - Optimization Interface JavaScript
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main optimization interface object
     */
    var SAOOptimizationInterface = {
        
        /**
         * Current optimization request
         */
        currentRequest: null,
        
        /**
         * Modal element
         */
        modal: null,
        
        /**
         * Initialize the interface
         */
        init: function() {
            this.bindEvents();
            this.createModal();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Optimization buttons
            $(document).on('click', '.sao-optimize-btn, .sao-media-optimize-btn', this.handleOptimizeClick);
            
            // Modal events
            $(document).on('click', '.sao-modal-close, .sao-modal-overlay', this.closeModal);
            $(document).on('click', '.sao-modal', function(e) {
                e.stopPropagation(); // Prevent modal close when clicking inside
            });
            
            // Action buttons
            $(document).on('click', '.sao-apply-btn', this.applyOptimization);
            $(document).on('click', '.sao-cancel-btn', this.closeModal);
            
            // Keyboard events
            $(document).on('keydown', this.handleKeydown);
        },

        /**
         * Handle optimize button click
         */
        handleOptimizeClick: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var postId = $button.data('post-id') || saoOptimization.postId;
            
            if (!postId) {
                SAOOptimizationInterface.showError(saoOptimization.strings.error);
                return;
            }
            
            // Validate content
            var content = SAOOptimizationInterface.getEditorContent();
            var title = SAOOptimizationInterface.getPostTitle();
            
            if (!content || content.trim().length < 50) {
                SAOOptimizationInterface.showError(saoOptimization.strings.noContent);
                return;
            }
            
            // Show loading state
            SAOOptimizationInterface.showLoadingState($button);
            
            // Perform optimization
            SAOOptimizationInterface.performOptimization(postId, content, title);
        },

        /**
         * Get editor content
         */
        getEditorContent: function() {
            var content = '';
            
            // Try to get content from different editors
            if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
                content = tinymce.get('content').getContent();
            } else if ($('#content').length) {
                content = $('#content').val();
            } else if ($('#post-content').length) {
                content = $('#post-content').val();
            }
            
            return content;
        },

        /**
         * Get post title
         */
        getPostTitle: function() {
            var title = '';
            
            if ($('#title').length) {
                title = $('#title').val();
            } else if ($('input[name="post_title"]').length) {
                title = $('input[name="post_title"]').val();
            }
            
            return title;
        },

        /**
         * Show loading state
         */
        showLoadingState: function($button) {
            // Update button
            $button.prop('disabled', true);
            var originalText = $button.html();
            $button.data('original-text', originalText);
            $button.html('<span class="sao-spinner"></span> ' + saoOptimization.strings.optimizing);
            
            // Show status in meta box
            $('.sao-optimization-status').show();
            $('.sao-status-text').text(saoOptimization.strings.analyzing);
            
            // Show modal with loading
            this.showModal();
            this.showModalLoading(saoOptimization.strings.analyzing);
        },

        /**
         * Hide loading state
         */
        hideLoadingState: function() {
            // Reset buttons
            $('.sao-optimize-btn, .sao-media-optimize-btn').each(function() {
                var $btn = $(this);
                var originalText = $btn.data('original-text');
                if (originalText) {
                    $btn.html(originalText).prop('disabled', false);
                }
            });
            
            // Hide status in meta box
            $('.sao-optimization-status').hide();
        },

        /**
         * Perform optimization AJAX request
         */
        performOptimization: function(postId, content, title) {
            // Cancel any existing request
            if (this.currentRequest) {
                this.currentRequest.abort();
            }
            
            // Prepare data
            var data = {
                action: 'sao_optimize_content',
                nonce: saoOptimization.nonce,
                post_id: postId,
                content: content,
                title: title
            };
            
            // Update loading text
            this.updateLoadingText(saoOptimization.strings.generating);
            
            // Send AJAX request
            this.currentRequest = $.post(saoOptimization.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        SAOOptimizationInterface.showResults(response.data);
                        SAOOptimizationInterface.updateLastOptimization();
                    } else {
                        var errorMessage = response.data && response.data.message 
                            ? response.data.message 
                            : saoOptimization.strings.error;
                            
                        if (response.data && response.data.code === 'rate_limit_exceeded') {
                            errorMessage = saoOptimization.strings.rateLimitError;
                        }
                        
                        SAOOptimizationInterface.showError(errorMessage);
                    }
                })
                .fail(function(xhr, status, error) {
                    if (status !== 'abort') {
                        SAOOptimizationInterface.showError(saoOptimization.strings.error);
                    }
                })
                .always(function() {
                    SAOOptimizationInterface.hideLoadingState();
                    SAOOptimizationInterface.currentRequest = null;
                });
        },

        /**
         * Show optimization results
         */
        showResults: function(data) {
            var html = this.buildResultsHTML(data);
            
            this.modal.find('.sao-modal-body').html(html);
            this.modal.removeClass('loading');
            
            // Show apply/cancel buttons
            this.modal.find('.sao-modal-footer').html(
                '<button type="button" class="sao-btn sao-btn-secondary sao-cancel-btn">' + 
                saoOptimization.strings.cancel + 
                '</button>' +
                '<button type="button" class="sao-btn sao-btn-primary sao-apply-btn">' + 
                saoOptimization.strings.apply + 
                '</button>'
            );
            
            // Store results data
            this.modal.data('results', data);
        },

        /**
         * Build results HTML
         */
        buildResultsHTML: function(data) {
            var html = '';
            
            // SEO Score
            html += '<div class="sao-results-section">';
            html += '<h3 class="sao-section-title">';
            html += '<span class="dashicons dashicons-chart-area"></span>';
            html += 'SEO Score';
            html += '</h3>';
            html += '<div class="sao-seo-score">';
            
            var scoreClass = 'poor';
            if (data.seo_score >= 80) scoreClass = 'excellent';
            else if (data.seo_score >= 60) scoreClass = 'good';
            
            html += '<div class="sao-score-circle ' + scoreClass + '">' + data.seo_score + '</div>';
            html += '<div class="sao-score-details">';
            html += '<h4>Your content scored ' + data.seo_score + ' out of 100</h4>';
            html += '<p>Word count: ' + data.word_count + ' | Content length: ' + data.content_length + ' characters</p>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            
            // Keywords
            if (data.keywords && data.keywords.length > 0) {
                html += '<div class="sao-results-section">';
                html += '<h3 class="sao-section-title">';
                html += '<span class="dashicons dashicons-tag"></span>';
                html += saoOptimization.strings.suggestedKeywords;
                html += '</h3>';
                html += '<div class="sao-keywords-list">';
                
                data.keywords.forEach(function(keyword) {
                    html += '<span class="sao-keyword-tag">' + SAOOptimizationInterface.escapeHtml(keyword) + '</span>';
                });
                
                html += '</div>';
                html += '</div>';
            }
            
            // Meta Description
            if (data.meta_description) {
                html += '<div class="sao-results-section">';
                html += '<h3 class="sao-section-title">';
                html += '<span class="dashicons dashicons-text-page"></span>';
                html += saoOptimization.strings.metaDescription;
                html += '</h3>';
                html += '<div class="sao-meta-description">' + SAOOptimizationInterface.escapeHtml(data.meta_description) + '</div>';
                
                var metaLength = data.meta_description.length;
                var lengthClass = 'good';
                var lengthText = 'Good length';
                
                if (metaLength < 120) {
                    lengthClass = 'warning';
                    lengthText = 'Too short';
                } else if (metaLength > 160) {
                    lengthClass = 'error';
                    lengthText = 'Too long';
                }
                
                html += '<div class="sao-meta-length ' + lengthClass + '">' + metaLength + ' characters - ' + lengthText + '</div>';
                html += '</div>';
            }
            
            // Title Suggestion
            if (data.title_suggestion) {
                html += '<div class="sao-results-section">';
                html += '<h3 class="sao-section-title">';
                html += '<span class="dashicons dashicons-editor-spellcheck"></span>';
                html += saoOptimization.strings.titleSuggestion;
                html += '</h3>';
                html += '<div class="sao-title-suggestion">' + SAOOptimizationInterface.escapeHtml(data.title_suggestion) + '</div>';
                html += '</div>';
            }
            
            // Suggestions
            if (data.suggestions && data.suggestions.length > 0) {
                html += '<div class="sao-results-section">';
                html += '<h3 class="sao-section-title">';
                html += '<span class="dashicons dashicons-lightbulb"></span>';
                html += 'Optimization Suggestions';
                html += '</h3>';
                html += '<ul class="sao-suggestions-list">';
                
                data.suggestions.forEach(function(suggestion) {
                    html += '<li>' + SAOOptimizationInterface.escapeHtml(suggestion) + '</li>';
                });
                
                html += '</ul>';
                html += '</div>';
            }
            
            return html;
        },

        /**
         * Create modal element
         */
        createModal: function() {
            var modalHTML = 
                '<div class="sao-modal-overlay">' +
                    '<div class="sao-modal">' +
                        '<div class="sao-modal-header">' +
                            '<h2 class="sao-modal-title">' + saoOptimization.strings.optimizationResults + '</h2>' +
                            '<button type="button" class="sao-modal-close">&times;</button>' +
                        '</div>' +
                        '<div class="sao-modal-body"></div>' +
                        '<div class="sao-modal-footer"></div>' +
                    '</div>' +
                '</div>';
            
            this.modal = $(modalHTML);
            $('body').append(this.modal);
        },

        /**
         * Show modal
         */
        showModal: function() {
            this.modal.addClass('active');
            $('body').addClass('sao-modal-open');
        },

        /**
         * Close modal
         */
        closeModal: function(e) {
            if (e && e.target !== this && !$(e.target).hasClass('sao-modal-close') && !$(e.target).hasClass('sao-modal-overlay')) {
                return;
            }
            
            SAOOptimizationInterface.modal.removeClass('active');
            $('body').removeClass('sao-modal-open');
            
            // Cancel any ongoing request
            if (SAOOptimizationInterface.currentRequest) {
                SAOOptimizationInterface.currentRequest.abort();
                SAOOptimizationInterface.currentRequest = null;
            }
            
            SAOOptimizationInterface.hideLoadingState();
        },

        /**
         * Show modal loading state
         */
        showModalLoading: function(text) {
            this.modal.addClass('loading');
            this.modal.find('.sao-modal-body').html(
                '<div class="sao-loading-spinner"></div>' +
                '<div class="sao-loading-text">' + text + '</div>'
            );
            this.modal.find('.sao-modal-footer').empty();
        },

        /**
         * Update loading text
         */
        updateLoadingText: function(text) {
            this.modal.find('.sao-loading-text').text(text);
            $('.sao-status-text').text(text);
        },

        /**
         * Apply optimization
         */
        applyOptimization: function(e) {
            e.preventDefault();
            
            var results = SAOOptimizationInterface.modal.data('results');
            if (!results) return;
            
            // Apply title suggestion
            if (results.title_suggestion) {
                var $titleField = $('#title');
                if ($titleField.length) {
                    $titleField.val(results.title_suggestion);
                }
            }
            
            // Apply meta description (if meta box exists)
            if (results.meta_description) {
                var $metaField = $('textarea[name="_yoast_wpseo_metadesc"], textarea[name="rank_math_description"], input[name="seopress_titles_desc"]');
                if ($metaField.length) {
                    $metaField.val(results.meta_description);
                }
            }
            
            // Show success message
            SAOOptimizationInterface.showSuccess(saoOptimization.strings.success);
            
            // Close modal
            SAOOptimizationInterface.closeModal();
        },

        /**
         * Update last optimization timestamp
         */
        updateLastOptimization: function() {
            var now = new Date();
            var timeString = now.toLocaleString();
            $('.sao-last-optimization').show();
            $('.sao-last-optimization-text').text('Last optimized: ' + timeString);
        },

        /**
         * Handle keyboard events
         */
        handleKeydown: function(e) {
            if (e.keyCode === 27) { // Escape key
                SAOOptimizationInterface.closeModal();
            }
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.showNotice(message, 'error');
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showNotice(message, 'success');
        },

        /**
         * Show notice
         */
        showNotice: function(message, type) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            
            // Try to add to WordPress admin notices area
            if ($('.wrap h1').length) {
                $('.wrap h1').after($notice);
            } else {
                // Fallback: show in modal or create floating notice
                if (this.modal && this.modal.hasClass('active')) {
                    this.modal.find('.sao-modal-body').prepend($notice);
                } else {
                    $('body').append('<div class="sao-floating-notice sao-notice-' + type + '">' + message + '</div>');
                    setTimeout(function() {
                        $('.sao-floating-notice').fadeOut(function() {
                            $(this).remove();
                        });
                    }, 5000);
                }
            }
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return text.replace(/[&<>"']/g, function(m) {
                return map[m];
            });
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        SAOOptimizationInterface.init();
    });

})(jQuery);

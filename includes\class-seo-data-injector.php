<?php
/**
 * SEO Data Injector Class
 *
 * This class handles injection of SEO data into detected SEO plugins
 *
 * @package SEO_Auto_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * SEO Data Injector Class
 *
 * @since 1.0.0
 */
class SEO_Auto_Optimizer_SEO_Data_Injector {

	/**
	 * Plugin instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_SEO_Data_Injector|null
	 */
	private static $instance = null;

	/**
	 * Plugin detector instance
	 *
	 * @since 1.0.0
	 * @var SEO_Auto_Optimizer_Plugin_Detector
	 */
	private $plugin_detector;

	/**
	 * Backup data cache key prefix
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $backup_key_prefix = 'sao_backup_';

	/**
	 * History data cache key prefix
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $history_key_prefix = 'sao_history_';

	/**
	 * Plugin-specific meta field mappings
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $plugin_meta_fields = array(
		'rank_math' => array(
			'focus_keyword'       => 'rank_math_focus_keyword',
			'meta_description'    => 'rank_math_description',
			'additional_keywords' => 'rank_math_pillar_content',
			'title'              => 'rank_math_title',
		),
		'yoast' => array(
			'focus_keyword'       => '_yoast_wpseo_focuskw',
			'meta_description'    => '_yoast_wpseo_metadesc',
			'additional_keywords' => '_yoast_wpseo_keywordsynonyms',
			'title'              => '_yoast_wpseo_title',
		),
		'seopress' => array(
			'focus_keyword'       => '_seopress_analysis_target_kw',
			'meta_description'    => '_seopress_titles_desc',
			'additional_keywords' => '_seopress_analysis_target_kw_synonyms',
			'title'              => '_seopress_titles_title',
		),
		'aioseo' => array(
			'focus_keyword'       => '_aioseo_keywords',
			'meta_description'    => '_aioseo_description',
			'additional_keywords' => '_aioseo_keyphrases',
			'title'              => '_aioseo_title',
		),
	);

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	private function __construct() {
		$this->init();
	}

	/**
	 * Get plugin instance (Singleton pattern)
	 *
	 * @since 1.0.0
	 * @return SEO_Auto_Optimizer_SEO_Data_Injector Plugin instance
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Prevent cloning
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __clone() {}

	/**
	 * Prevent unserialization
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function __wakeup() {}

	/**
	 * Initialize the class
	 *
	 * @since 1.0.0
	 * @return void
	 */
	private function init() {
		// Initialize plugin detector
		$this->plugin_detector = SEO_Auto_Optimizer_Plugin_Detector::get_instance();

		// Add hooks
		add_action( 'init', array( $this, 'register_hooks' ) );
	}

	/**
	 * Register hooks
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function register_hooks() {
		// Add AJAX handlers for admin
		if ( is_admin() ) {
			add_action( 'wp_ajax_sao_inject_keywords', array( $this, 'ajax_inject_keywords' ) );
			add_action( 'wp_ajax_sao_restore_backup', array( $this, 'ajax_restore_backup' ) );
			add_action( 'wp_ajax_sao_get_injection_history', array( $this, 'ajax_get_injection_history' ) );
		}
	}

	/**
	 * Main injection method
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID to inject keywords into
	 * @param array  $keywords    Array of keywords data
	 * @param string $plugin_name Target plugin name
	 * @param array  $options     Injection options
	 * @return array|WP_Error Result of injection or error
	 */
	public function inject_keywords( $post_id, $keywords, $plugin_name, $options = array() ) {
		// Validate inputs
		$validation = $this->validate_injection_inputs( $post_id, $keywords, $plugin_name );
		if ( is_wp_error( $validation ) ) {
			return $validation;
		}

		// Default options
		$default_options = array(
			'preserve_existing' => true,
			'create_backup'     => true,
			'auto_meta_desc'    => true,
			'force_overwrite'   => false,
		);
		$options = wp_parse_args( $options, $default_options );

		// Create backup if requested
		if ( $options['create_backup'] ) {
			$backup_result = $this->create_backup( $post_id, $plugin_name );
			if ( is_wp_error( $backup_result ) ) {
				return $backup_result;
			}
		}

		// Perform injection
		$injection_result = $this->perform_injection( $post_id, $keywords, $plugin_name, $options );

		// Log injection history
		if ( ! is_wp_error( $injection_result ) ) {
			$this->log_injection_history( $post_id, $keywords, $plugin_name, $options );
		}

		return $injection_result;
	}

	/**
	 * Validate injection inputs
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID
	 * @param array  $keywords    Keywords data
	 * @param string $plugin_name Plugin name
	 * @return true|WP_Error True if valid, WP_Error otherwise
	 */
	private function validate_injection_inputs( $post_id, $keywords, $plugin_name ) {
		// Check user capabilities
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return new WP_Error(
				'insufficient_permissions',
				__( 'You do not have permission to edit this post.', 'seo-auto-optimizer' )
			);
		}

		// Validate post ID
		$post = get_post( $post_id );
		if ( ! $post ) {
			return new WP_Error(
				'invalid_post_id',
				__( 'Invalid post ID provided.', 'seo-auto-optimizer' )
			);
		}

		// Validate keywords
		if ( empty( $keywords ) || ! is_array( $keywords ) ) {
			return new WP_Error(
				'invalid_keywords',
				__( 'Keywords data is required and must be an array.', 'seo-auto-optimizer' )
			);
		}

		// Validate plugin name
		if ( ! isset( $this->plugin_meta_fields[ $plugin_name ] ) ) {
			return new WP_Error(
				'unsupported_plugin',
				__( 'Unsupported SEO plugin specified.', 'seo-auto-optimizer' )
			);
		}

		// Check if plugin is active
		$detected_plugins = $this->plugin_detector->get_detected_plugins();
		if ( ! isset( $detected_plugins[ $plugin_name ] ) || ! $detected_plugins[ $plugin_name ]['active'] ) {
			return new WP_Error(
				'plugin_not_active',
				__( 'Target SEO plugin is not active.', 'seo-auto-optimizer' )
			);
		}

		return true;
	}

	/**
	 * Perform the actual injection
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID
	 * @param array  $keywords    Keywords data
	 * @param string $plugin_name Plugin name
	 * @param array  $options     Injection options
	 * @return array|WP_Error Injection results or error
	 */
	private function perform_injection( $post_id, $keywords, $plugin_name, $options ) {
		$meta_fields = $this->plugin_meta_fields[ $plugin_name ];
		$results = array();

		// Process focus keyword
		if ( ! empty( $keywords['focus_keyword'] ) ) {
			$sanitized_keyword = $this->sanitize_keyword( $keywords['focus_keyword'] );
			$result = $this->inject_meta_field(
				$post_id,
				$meta_fields['focus_keyword'],
				$sanitized_keyword,
				$options
			);
			$results['focus_keyword'] = $result;
		}

		// Process additional keywords
		if ( ! empty( $keywords['additional_keywords'] ) && is_array( $keywords['additional_keywords'] ) ) {
			$sanitized_keywords = array_map( array( $this, 'sanitize_keyword' ), $keywords['additional_keywords'] );
			$keywords_string = $this->format_additional_keywords( $sanitized_keywords, $plugin_name );
			$result = $this->inject_meta_field(
				$post_id,
				$meta_fields['additional_keywords'],
				$keywords_string,
				$options
			);
			$results['additional_keywords'] = $result;
		}

		// Process meta description
		if ( ! empty( $keywords['meta_description'] ) ) {
			$sanitized_description = $this->sanitize_meta_description( $keywords['meta_description'] );
			$result = $this->inject_meta_field(
				$post_id,
				$meta_fields['meta_description'],
				$sanitized_description,
				$options
			);
			$results['meta_description'] = $result;
		} elseif ( $options['auto_meta_desc'] && ! empty( $keywords['focus_keyword'] ) ) {
			// Auto-generate meta description if empty
			$auto_description = $this->generate_meta_description( $post_id, $keywords['focus_keyword'] );
			if ( $auto_description ) {
				$result = $this->inject_meta_field(
					$post_id,
					$meta_fields['meta_description'],
					$auto_description,
					$options
				);
				$results['meta_description'] = $result;
			}
		}

		// Process title if provided
		if ( ! empty( $keywords['title'] ) ) {
			$sanitized_title = $this->sanitize_title( $keywords['title'] );
			$result = $this->inject_meta_field(
				$post_id,
				$meta_fields['title'],
				$sanitized_title,
				$options
			);
			$results['title'] = $result;
		}

		return $results;
	}

	/**
	 * Inject data into a specific meta field
	 *
	 * @since 1.0.0
	 * @param int    $post_id    Post ID
	 * @param string $meta_key   Meta key
	 * @param string $meta_value Meta value
	 * @param array  $options    Injection options
	 * @return array Injection result
	 */
	private function inject_meta_field( $post_id, $meta_key, $meta_value, $options ) {
		$existing_value = get_post_meta( $post_id, $meta_key, true );

		// Check if we should preserve existing data
		if ( $options['preserve_existing'] && ! empty( $existing_value ) && ! $options['force_overwrite'] ) {
			return array(
				'success'        => false,
				'message'        => __( 'Existing data preserved', 'seo-auto-optimizer' ),
				'existing_value' => $existing_value,
				'new_value'      => $meta_value,
			);
		}

		// Update the meta field
		$update_result = update_post_meta( $post_id, $meta_key, $meta_value );

		if ( $update_result ) {
			return array(
				'success'        => true,
				'message'        => __( 'Data injected successfully', 'seo-auto-optimizer' ),
				'previous_value' => $existing_value,
				'new_value'      => $meta_value,
			);
		} else {
			return array(
				'success' => false,
				'message' => __( 'Failed to update meta field', 'seo-auto-optimizer' ),
			);
		}
	}

	/**
	 * Create backup of existing SEO data
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID
	 * @param string $plugin_name Plugin name
	 * @return true|WP_Error True on success, WP_Error on failure
	 */
	private function create_backup( $post_id, $plugin_name ) {
		$meta_fields = $this->plugin_meta_fields[ $plugin_name ];
		$backup_data = array();

		// Collect current meta values
		foreach ( $meta_fields as $field_type => $meta_key ) {
			$current_value = get_post_meta( $post_id, $meta_key, true );
			if ( ! empty( $current_value ) ) {
				$backup_data[ $field_type ] = $current_value;
			}
		}

		// Store backup with timestamp
		$backup_key = $this->backup_key_prefix . $post_id . '_' . $plugin_name;
		$backup_entry = array(
			'timestamp'   => current_time( 'timestamp' ),
			'plugin_name' => $plugin_name,
			'data'        => $backup_data,
			'user_id'     => get_current_user_id(),
		);

		$result = set_transient( $backup_key, $backup_entry, WEEK_IN_SECONDS );

		if ( ! $result ) {
			return new WP_Error(
				'backup_failed',
				__( 'Failed to create backup of existing SEO data.', 'seo-auto-optimizer' )
			);
		}

		return true;
	}

	/**
	 * Restore backup data
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID
	 * @param string $plugin_name Plugin name
	 * @return array|WP_Error Restoration result or error
	 */
	public function restore_backup( $post_id, $plugin_name ) {
		// Validate inputs
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return new WP_Error(
				'insufficient_permissions',
				__( 'You do not have permission to edit this post.', 'seo-auto-optimizer' )
			);
		}

		// Get backup data
		$backup_key = $this->backup_key_prefix . $post_id . '_' . $plugin_name;
		$backup_data = get_transient( $backup_key );

		if ( ! $backup_data ) {
			return new WP_Error(
				'no_backup_found',
				__( 'No backup found for this post and plugin combination.', 'seo-auto-optimizer' )
			);
		}

		$meta_fields = $this->plugin_meta_fields[ $plugin_name ];
		$results = array();

		// Restore each field
		foreach ( $backup_data['data'] as $field_type => $value ) {
			if ( isset( $meta_fields[ $field_type ] ) ) {
				$meta_key = $meta_fields[ $field_type ];
				$update_result = update_post_meta( $post_id, $meta_key, $value );
				$results[ $field_type ] = $update_result;
			}
		}

		// Log restoration
		$this->log_restoration( $post_id, $plugin_name, $backup_data );

		return array(
			'success' => true,
			'message' => __( 'Backup restored successfully.', 'seo-auto-optimizer' ),
			'results' => $results,
		);
	}

	/**
	 * Log injection history
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID
	 * @param array  $keywords    Keywords data
	 * @param string $plugin_name Plugin name
	 * @param array  $options     Injection options
	 * @return void
	 */
	private function log_injection_history( $post_id, $keywords, $plugin_name, $options ) {
		$history_key = $this->history_key_prefix . $post_id;
		$existing_history = get_transient( $history_key );

		if ( ! is_array( $existing_history ) ) {
			$existing_history = array();
		}

		$history_entry = array(
			'timestamp'   => current_time( 'timestamp' ),
			'plugin_name' => $plugin_name,
			'keywords'    => $keywords,
			'options'     => $options,
			'user_id'     => get_current_user_id(),
			'action'      => 'injection',
		);

		// Keep only last 10 entries
		array_unshift( $existing_history, $history_entry );
		$existing_history = array_slice( $existing_history, 0, 10 );

		set_transient( $history_key, $existing_history, MONTH_IN_SECONDS );
	}

	/**
	 * Log restoration action
	 *
	 * @since 1.0.0
	 * @param int    $post_id     Post ID
	 * @param string $plugin_name Plugin name
	 * @param array  $backup_data Backup data
	 * @return void
	 */
	private function log_restoration( $post_id, $plugin_name, $backup_data ) {
		$history_key = $this->history_key_prefix . $post_id;
		$existing_history = get_transient( $history_key );

		if ( ! is_array( $existing_history ) ) {
			$existing_history = array();
		}

		$history_entry = array(
			'timestamp'   => current_time( 'timestamp' ),
			'plugin_name' => $plugin_name,
			'backup_data' => $backup_data,
			'user_id'     => get_current_user_id(),
			'action'      => 'restoration',
		);

		array_unshift( $existing_history, $history_entry );
		$existing_history = array_slice( $existing_history, 0, 10 );

		set_transient( $history_key, $existing_history, MONTH_IN_SECONDS );
	}

	/**
	 * Get injection history for a post
	 *
	 * @since 1.0.0
	 * @param int $post_id Post ID
	 * @return array Injection history
	 */
	public function get_injection_history( $post_id ) {
		$history_key = $this->history_key_prefix . $post_id;
		$history = get_transient( $history_key );

		return is_array( $history ) ? $history : array();
	}

	/**
	 * Sanitize keyword
	 *
	 * @since 1.0.0
	 * @param string $keyword Keyword to sanitize
	 * @return string Sanitized keyword
	 */
	private function sanitize_keyword( $keyword ) {
		return sanitize_text_field( trim( $keyword ) );
	}

	/**
	 * Sanitize meta description
	 *
	 * @since 1.0.0
	 * @param string $description Description to sanitize
	 * @return string Sanitized description
	 */
	private function sanitize_meta_description( $description ) {
		$description = sanitize_textarea_field( $description );
		// Limit to 160 characters for optimal SEO
		if ( strlen( $description ) > 160 ) {
			$description = substr( $description, 0, 157 ) . '...';
		}
		return $description;
	}

	/**
	 * Sanitize title
	 *
	 * @since 1.0.0
	 * @param string $title Title to sanitize
	 * @return string Sanitized title
	 */
	private function sanitize_title( $title ) {
		$title = sanitize_text_field( $title );
		// Limit to 60 characters for optimal SEO
		if ( strlen( $title ) > 60 ) {
			$title = substr( $title, 0, 57 ) . '...';
		}
		return $title;
	}

	/**
	 * Format additional keywords based on plugin requirements
	 *
	 * @since 1.0.0
	 * @param array  $keywords    Keywords array
	 * @param string $plugin_name Plugin name
	 * @return string Formatted keywords string
	 */
	private function format_additional_keywords( $keywords, $plugin_name ) {
		switch ( $plugin_name ) {
			case 'yoast':
				// Yoast uses comma-separated synonyms
				return implode( ', ', $keywords );
			case 'rank_math':
				// Rank Math uses comma-separated keywords
				return implode( ', ', $keywords );
			case 'seopress':
				// SEOPress uses comma-separated keywords
				return implode( ', ', $keywords );
			case 'aioseo':
				// AIOSEO uses comma-separated keywords
				return implode( ', ', $keywords );
			default:
				return implode( ', ', $keywords );
		}
	}

	/**
	 * Generate meta description automatically
	 *
	 * @since 1.0.0
	 * @param int    $post_id Post ID
	 * @param string $keyword Focus keyword
	 * @return string|false Generated description or false
	 */
	private function generate_meta_description( $post_id, $keyword ) {
		$post = get_post( $post_id );
		if ( ! $post ) {
			return false;
		}

		// Get post excerpt or content
		$content = ! empty( $post->post_excerpt ) ? $post->post_excerpt : $post->post_content;
		$content = wp_strip_all_tags( $content );
		$content = preg_replace( '/\s+/', ' ', $content );
		$content = trim( $content );

		if ( empty( $content ) ) {
			return false;
		}

		// Try to create a description that includes the keyword
		$sentences = preg_split( '/[.!?]+/', $content );
		$description = '';

		// Look for sentences containing the keyword
		foreach ( $sentences as $sentence ) {
			if ( stripos( $sentence, $keyword ) !== false ) {
				$description = trim( $sentence );
				break;
			}
		}

		// If no sentence with keyword found, use first sentence
		if ( empty( $description ) && ! empty( $sentences[0] ) ) {
			$description = trim( $sentences[0] );
		}

		// Ensure description is not too long
		if ( strlen( $description ) > 160 ) {
			$description = substr( $description, 0, 157 ) . '...';
		}

		return $description;
	}

	/**
	 * Detect primary vs secondary keywords automatically
	 *
	 * @since 1.0.0
	 * @param array $keywords Keywords array
	 * @return array Categorized keywords
	 */
	public function categorize_keywords( $keywords ) {
		if ( empty( $keywords ) || ! is_array( $keywords ) ) {
			return array(
				'primary'   => '',
				'secondary' => array(),
			);
		}

		// First keyword is typically primary
		$primary = array_shift( $keywords );
		$secondary = $keywords;

		return array(
			'primary'   => $primary,
			'secondary' => $secondary,
		);
	}

	/**
	 * AJAX handler for keyword injection
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_inject_keywords() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'sao_inject_keywords' ) ) {
			wp_die( __( 'Security check failed.', 'seo-auto-optimizer' ) );
		}

		$post_id = intval( $_POST['post_id'] );
		$plugin_name = sanitize_text_field( $_POST['plugin_name'] );
		$keywords = $_POST['keywords'];
		$options = isset( $_POST['options'] ) ? $_POST['options'] : array();

		$result = $this->inject_keywords( $post_id, $keywords, $plugin_name, $options );

		if ( is_wp_error( $result ) ) {
			wp_send_json_error( $result->get_error_message() );
		} else {
			wp_send_json_success( $result );
		}
	}

	/**
	 * AJAX handler for backup restoration
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_restore_backup() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'sao_restore_backup' ) ) {
			wp_die( __( 'Security check failed.', 'seo-auto-optimizer' ) );
		}

		$post_id = intval( $_POST['post_id'] );
		$plugin_name = sanitize_text_field( $_POST['plugin_name'] );

		$result = $this->restore_backup( $post_id, $plugin_name );

		if ( is_wp_error( $result ) ) {
			wp_send_json_error( $result->get_error_message() );
		} else {
			wp_send_json_success( $result );
		}
	}

	/**
	 * AJAX handler for getting injection history
	 *
	 * @since 1.0.0
	 * @return void
	 */
	public function ajax_get_injection_history() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'sao_get_history' ) ) {
			wp_die( __( 'Security check failed.', 'seo-auto-optimizer' ) );
		}

		$post_id = intval( $_POST['post_id'] );

		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			wp_send_json_error( __( 'Insufficient permissions.', 'seo-auto-optimizer' ) );
		}

		$history = $this->get_injection_history( $post_id );
		wp_send_json_success( $history );
	}

	/**
	 * Get supported plugins
	 *
	 * @since 1.0.0
	 * @return array Supported plugins
	 */
	public function get_supported_plugins() {
		return array_keys( $this->plugin_meta_fields );
	}

	/**
	 * Get plugin meta fields mapping
	 *
	 * @since 1.0.0
	 * @param string $plugin_name Plugin name
	 * @return array|false Meta fields mapping or false
	 */
	public function get_plugin_meta_fields( $plugin_name ) {
		return isset( $this->plugin_meta_fields[ $plugin_name ] ) ? $this->plugin_meta_fields[ $plugin_name ] : false;
	}
}